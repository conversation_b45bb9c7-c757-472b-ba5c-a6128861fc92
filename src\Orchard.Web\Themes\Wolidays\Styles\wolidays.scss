﻿/* seminarlocations theme for discoverize.com portal - created 5.1.2020 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */
@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "4_layouts/_search-page--grid-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
@import "4_layouts/_search-page--grid";
$cb-bg-hp: rgba(255, 255, 255, 1);

/* end import */

/* overwrites */

/* graphics */

body {
    background: none;
    font-family: trebuchet ms, sans-serif; 

    .sf61{
        color: #fff !important;
    }
}

/* end graphics */

/* global */

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #343434; 
}

/* end global */ 

/* layout */
.search-page {
    margin-top: 0;
    padding-top: 0;
}
.button-light {
    color: $secondary;
}
.custom-checkbox-input:checked ~ .custom-checkbox-label::before {
    background-color: $positive;
    border-color: darken($positive, 10%);
}
/* end layout */

/* header */

.header-top {
    background: #000;
}
.tagline {
    @include primary-font;
    color: #fff;
    padding: 0;
    float: left;
    font-size: 0.85em;
}
.mini-nav {
    a,
    a:hover {
        color: #fff;
        border-right: none;
    }
    a:hover {
        background: $primary;
    }
    span {
        color: #eee;
    }
    .mn11 {
        border-right: none;
    }
}

.header-main {
    background: $primary;
}
.logo {
    margin-top: 10px;
}
.sticky-header {
    background-color: $primary;
    .logo {
        margin-top: 12px;
    }
}

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 37px 16px 32px 16px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav {
//     @include border-box;
//     z-index: 3;
// }
// .nav a {
//     @include primary-font;
// }
// .nav .current {
//     background: lighten($primary, 30%);
//     color: $txt-on-primary;
// }

// .nav > li:hover > a {
//     background: $primary-darker;
// }

// a.nav01,
// a.nav10,
// a.nav21,
// a.nav31,
// a.nav41 {
//     @include border-box;
//     padding: 37px 16px 32px 16px;
// }
// .nav .nav19,
// .nav .nav10 {
//     @include rounded(0, 0, 0, 0);
// }
// .nav10::before {
//     content: none;
// }
// .nav-level-2 {
//     @include shadow-remove;
//     background: $primary-darker;
//     border: none;
//     padding: 0 0 0.6em 1em;
// }
// .nav-level-2 a {
//     background: $primary-darker;
//     font-size: 18px;
//     border-color: $txt-on-primary;
//     border-top-width: 0;
//     color: $secondary;
//     padding: 5px 8px 4px 4px;
// }
// .nav-level-2 a:hover {
//     background: #fff;
//     color: $txt-on-primary;
// }

.global-search {
    @include global-search(150px);
    padding: 32px 3px 6px 0;
    color: #ccc;
}

/* end header */

/* homepage */

.mini-search {
    @include background-responsive(
        "img/bg.jpg",
        $image-height-mobile: 520,
        $image-height-desktop: 462
    );
    min-height: 462px;
    background: no-repeat 0 0;
    background-size: cover;
    background-position: center top;
    padding: 5% 12px;
    &::before {
        content: none;
    }
    @include media("<=page-width") {
        background-position-x: right;
    }
}
.ms10 {
    font-weight: 300;
    font-size: 2em;
}
.ms11 input.sb1 {
    border-radius: 3px;
    font-size: 21px;
}
label.custom-checkbox-label:before {
    border-color: $secondary;
}
.ms12 {
    border-radius: 3px;
}
.ms12:nth-child(4) {
    display: none;
}
.ms261 {
    color: lighten($primary, 40);
}

// .area {
//     background-color: $medium;
//     a {
//         color: #8c8c8c;
//     }
// }
.explained img {
    border-width: 0px;
}
// .properties {
//     background-color: $secondary;
//     a {
//         color: $txt-on-secondary;
//     }
// }
// .prop10 {
//     color: #aaa;
// }

// .pbi2 .pbi22,
// .current-entries li,
// .pbr22 {
//     background-color: #000;
//     color: #fff;
//     a,
//     .ec-tagline {
//         color: #fff;
//     }
// }
// .pbi22,
// .current-entries {
//     .ec-town::before {
//         content: "";
//         background-color: #fff;
//         height: 3px;
//         width: 35px;
//         display: block;
//         margin-bottom: 5px;
//     }
// }
// .mini-gallery12::before {
//     content: none;
// }
// .current-entries .ec-name {
//     font-size: 13px;
//     font-weight: normal;
//     padding: 10px;
//     min-height: 2em;
//     &:hover {
//         color: $secondary;
//     }
// }
// .current-entries .ec-town {
//     margin: 6px;
// }
.content-page.home-page p,
.content-page.home-page li {
    font-size: 0.9em;
}
.home-page .blog-teaser-list {
    background-color: #efefef;
}
.blgt1 {
    background-color: #fff;
    border-radius: 0px;
}
.blgt81 {
    background-color: #fff;
    border-radius: 0px;
    box-sizing: border-box;
    width: 100%;
}
.blgt85 {
    border-radius: 0px;
}

.content-page {
    .cb51,
    .cb52,
    .cb53 {
        p {
            font-size: 0.9em;
            line-height: 1.6em;
        }
    }
}

.cb-sides-content-1 {
    font-size: 13px;
}
.cb-sides-content-2 {
    font-weight: 500;
}
.cb-sides-content-32 {
    font-weight: normal;
}
.content-page .cb-sides-content-0 p {
    font-size: 0.9em;
    line-height: 1.4;
}

.blgt34 {
    line-height: 1.2;
}
.content-page.home-page .blgt51 {
    line-height: 1.4;
}
.blgt81:hover {
    text-shadow: none;
}

.search-fullwidth .sp1642::before {
    border-color: $tertiary-darker;
    color: $tertiary-darker;
}
.search-fullwidth .sp1642:hover::before {
    background-color: $tertiary;
    color: #fff;
    box-shadow: none;
}
.search-fullwidth .sfu-list .criteria-group:hover {
    border-color: $negative;
}

/* end homepage */

/* search page */
.sf6 .sf61 {
    background-color: $secondary;
    color: $txt-on-secondary;
    text-shadow: none;
    border-color: darken($secondary, 10);
}
.sf6 .sf61:hover {
    text-shadow: none;
}
.search-fullwidth .sf62 {
    color: #ddd;
}

.sf61-34, .epf61-34 { 
    @extend .icon-Filter_Wolidays;
}
.sf61-5, .epf61-5 { 
    @extend .icon-Filter_Hotelerie;
}
.sf61-35, .epf61-35 { 
    @extend .icon-Filter_Hochzeitslocation;
}
.sf61-37, .epf61-37 { 
    @extend .icon-Filter_Hochzeitslocation-Ausstattung;
}

.sf85 {
    background-color: $tertiary-darker;
    color: $txt-on-tertiary;
}

.search-fullwidth .sfu-list {
    label,
    strong,
    .criteria-group > .checkbox,
    .criteria-group > .mc32 {
        color: #706900;
        font-weight: 300;
    }
}

@include hochzeit-map-marker;
.map-marker {
    width: 37px;
    height: 52px;
}
.map-marker-static {
    top: 50%;
    left: 50%;
    margin-left: -32px;
    margin-top: -50px;
}

.sp19 {
    background: #fff;
    color: $primary;
    border: solid 1px $primary;
    &:hover {
        background: $primary;
        color: #fff;
    }
}

.sr022,
.sr023 {
    color: $secondary;
}

/* map marker icon font-size reapplied dut to IE */

.ls10 {
    .ls11,
    .ls12,
    .ls13 {
        border-color: $secondary;
        color: $secondary;
    }
}

.sp1 {
    clear: both;
}
.sp11,
.sp110,
.sp11:last-child {
    background: #fff;
    border-width: 0px;
}

.sp19 {
    bottom: 0px;
    right: 0px;
    &:hover {
        color: $secondary-darker;
    }
}

@include search-result-details-over-image;
.sp1503 {
    border-radius: 0; 
}

.sp1501 {
    color: #fff;
    .sp21 {
        background-color: $secondary-darker;
    }
}
.sp1200,
.sp1220,
.sp190 {
    background-color: $secondary-darker;
}
.sp1201,
.sp1220 h4,
.sp1220 a,
.sp1200 a,
.sp190 p,
.sp190 a {
    color: #fff; 
}


.sp1500::before {
    background-color: hsla(0, 0%, 0%, 0.75);
}
.sp131,
.sp132 {
    display: none;
}
.sp15 {
    font-size: 1.5em;
    font-weight: bold;
}
.sp150 {
    text-transform: uppercase;
    font-weight: 500;
}
.sp151 {
    font-size: 1.2em;
    @include media("<=page-width"){
        font-size: 1em;
    }
    .rating-display {
        color: #1b1b1b;
    }
    .rating-actual {
        color: $primary;
    }
}
.rating-count {
    font-size: 0.5em;
}
.sp17 {
    font-size: 1.2em;
}
.premium-booked::before {
    content: none;
}
.sp19 {
    color: $secondary;
    border-color: $secondary;
}

.srp1 {
    a {
        background: #fff;
        border-color: $secondary;
        border-width: 2px;
        color: $secondary;
        font-size: 1.1em;
    }
    li:first-child a,
    li:last-child a {
        border-radius: 0px;
    }
    .page-next,
    .page-previous {
        a {
            background: $secondary;
            color: $txt-on-secondary;
            padding: 0.35em 0.4em 0.65em;
        }
    }
    li.page-previous a {
        @extend .icon-chevron-thin-left;
    }
    li.page-next {
        border-left-width: 0;
    }
    li.page-next a {
        @extend .icon-chevron-thin-right;
    }
}

// @supports (display: grid) {
//     .sp1 {
//         display: grid;
//         gap: 12px;
//         margin-right: 12px;
//     }
//     .search-results-full-width .sp1 {
//         margin-right: 0;
//     }
//     .search-fullwidth .sp11 {
//         margin: 0;
//         min-width: auto;
//     }
// }

// @include media(">=600px") {
//     .sp1 {
//         grid-template-columns: 1fr 1fr;
//     }
// }
// @include media(">=960px", "<1160px") {
//     .sp1 {
//         grid-template-columns: repeat(6, 1fr);
//     }
//     .sp11 {
//         grid-column: span 2;
//     }
//     .sp11.premium-booked {
//         grid-column: span 3;
//     }
// }
// @include media(">=1160px", "<=1560px") {
//     .sp1 {
//         grid-template-columns: repeat(2, 1fr); 
//     }
//     .sp11.premium-booked {
//         grid-column: span 2;
//     } 
// }
// @include media(">1560px", "<1600px") {
//     .sp1 {
//         grid-template-columns: repeat(4, 1fr);
//     }
//     .sp11.premium-booked {
//         grid-column: span 2;
//     }
// }
// @include media(">=1160px", "<1600px") {
//     .search-results-full-width .sp1 {
//         grid-template-columns: repeat(4, 1fr);
//     }
//     .search-results-full-width .sp11.premium-booked {
//         grid-column: span 2;
//     }
// }
// @include media(">=1600px", "<=1985px") {
//     .sp1 {
//         grid-template-columns: repeat(4, 1fr);
//     }
//     .search-results-full-width .sp1 {
//         grid-template-columns: repeat(6, 1fr);
//     }
//     .sp11.premium-booked {
//         grid-column: span 2;
//     }
//     .search-results-full-width .sp11.premium-booked {
//         grid-column: span 2;
//     }
// }
// @include media(">1985px") {
//     .sp1 {
//         grid-template-columns: repeat(6, 1fr);
//     }
//     .sp11.premium-booked {
//         grid-column: span 2;
//     }
// }
// @include media(">2100px") {
//     .search-results-full-width .sp1 {
//         grid-template-columns: repeat(8, 1fr);
//     }
// }

.slider--right-side .slider-track-high {
    background: $tertiary;
}
.slider-selection {
    background: $tertiary;
}
.slider-handle {
    background: darken($tertiary, 15%);
}
.slider .tooltip-inner {
    background: darken($tertiary, 15%);
}
.tooltip.top .tooltip-arrow {
    border-top-color: darken($tertiary, 15%);
}

.stacked-entries,
.mappopup {
    .sp1 {
        display: flex;
        margin-right: 0; 
    }
    .sp11 {
        flex-basis: auto;
        margin-bottom: 1em;
        max-width: 317px; 
    }
    .sp13 img {
        height: auto;
    }
    .sp15000 {
        position: static;
        height: auto;
        overflow: auto;
        transform: translateY(0%);
    }
    .sp1500 {
        position: static;
        transform: translate(0, 0);
        padding-bottom: 0.5em;
    }
    .sp1500::before {
        content: none;
    }
    .sp1501 {
        color: $txt-color;
        height: auto;
        overflow: auto;
    }
}

@mixin Zimmerkategorie_All {
    @extend .icon-star5;
    margin-right: 2px;
    font-size: 16px;
    line-height: normal;
    &:before {
        color: darken(#ffd700, 5);
        overflow: hidden;
        position: relative;
        top: 4px;
    }
    &:after {
        font-weight: bold;
        font-style: normal;
        position: relative;
        top: 1px;
    }
}

.Klassifizierung_1,
.Klassifizierung_2,
.Klassifizierung_3,
.Klassifizierung_4,
.Klassifizierung_5,
.Klassifizierung_6,
.Klassifizierung_7,
.Klassifizierung_8 {
    @include Zimmerkategorie_All;
    & + .search-selected-category-text {
        @extend %hide-text;
    }
}
$singleStarWidth: 16px;
.Klassifizierung_1:before {
    width: $singleStarWidth * 1;
}
.Klassifizierung_2:before {
    width: $singleStarWidth * 2;
}
.Klassifizierung_3:before {
    width: $singleStarWidth * 3;
}

.Klassifizierung_4:before {
    width: $singleStarWidth * 3;
}
.Klassifizierung_4:after {
    content: "S";
}

.Klassifizierung_5:before {
    width: calc(#{$singleStarWidth} * 4 - 1px);
}

.Klassifizierung_6:before {
    width: calc(#{$singleStarWidth} * 4 - 1px);
}
.Klassifizierung_6:after {
    content: "S";
}

.Klassifizierung_7:before {
    width: $singleStarWidth * 5;
}

.Klassifizierung_8:before {
    width: $singleStarWidth * 5;
}
.Klassifizierung_8:after {
    content: "S";
}

.Hotelkategorie_1,
.Hotelkategorie_2,
.Hotelkategorie_3,
.Hotelkategorie_4,
.Hotelkategorie_5 {
    @include Zimmerkategorie_All;
    & + .search-selected-category-text {
        @extend %hide-text;
    }
}

.Hotelkategorie_1:before {
    width: $singleStarWidth * 3;
}
.Hotelkategorie_2:before {
    width: calc(#{$singleStarWidth} * 4 - 1px);
}

.Hotelkategorie_3:before {
    width: calc(#{$singleStarWidth} * 4 - 1px);
}
.Hotelkategorie_3:after {
    content: "S";
}

.Hotelkategorie_4:before {
    width: $singleStarWidth * 5;
}

.Hotelkategorie_5:before {
    width: $singleStarWidth * 5;
}
.Hotelkategorie_5:after {
    content: "S";
}

/* end search page */

/* blog */

.blg7 {
    top: -14px;
    right: 0;
}

/* end blog */
// .ep22 a {
//     background-color: #fff;
//     font-weight: 500;
// }
// .ep22 li.active {
//     top: 4px;
// }
// .ep22 li.active a,
// .ep22 li.active:hover a {
//     background-color: #efefef;
//     @include media(">page-width") {
//         border-top: 1px solid #1b1b1b;
//     }
// }

// .ep225 a {
//     background-color: $tertiary;
// }
// .ep81 {
//     background-color: $tertiary;
//     border-color: darken($tertiary, 15%);
// }
// .ep32 {
//     flex-direction: column;
//     flex-wrap: nowrap;
//     font-size: 1.5em;
//     li {
//         @include primary-font;
//         padding-bottom: 0;
//     }
// }
// .ep320 {
//     background-color: $primary;
//     padding-bottom: 12px;
//     padding-left: 24px;
// }
// .ep332:last-child {
//     background: #5d5d5d;
//     width: 100%;
//     text-align: center;
//     margin: 0 -12px;
//     padding-top: 12px;
//     .ep331 {
//         color: #fff;
//     }
// }
// li.ep35:first-child {
//     margin-right: 0;
// }

// .ep952,
// .ep332 {
//     @include media(">480px") {
//         width: calc(48% - 11px);
//     }
//     .no-info {
//         font-weight: 100;
//     }
// }
// .ep9521 {
//     display: inline-block;
// }
// strong.sp201,
// .sp202 {
//     @include media(">680px") {
//         float: left;
//         width: calc(50% - 6px);
//     }
//     @include media("<=680px") {
//         display: block;
//     }
// }
// .sp2021 {
//     border: 1px solid #1b1b1b;
//     padding: 0.2em 0.5em 0.3em;
//     @include media("<=360px") {
//         display: inline-block;
//     }
// }

.design-guide {
    border-bottom: solid 1px #ccc;
}

blockquote {
    font-weight: 400;
    color: #777;
}

footer {
    border: none;
    width: 100%;
    background: $secondary-darker;
}

.footer-top {
    @include footer-top("", $primary, $txt-on-primary);
    padding: 2em;
    h3 {
        font-weight: bold;
    }
    h3,
    h4 {
        line-height: 1.3em;
        padding: 0;
    }
}
.footer-top12 {
    background-color: $primary;
    border: 2px solid #1b1b1b;
    color: $txt-on-primary;
    font-weight: bold;
    font-size: 18px;
    padding: 0.3em 1em 0.2em;
    text-shadow: none;
    &::before {
        content: none;
    }
}

.logo-footer {
    padding: 16px;
    display: block;
    &:hover { 
        @include rounded(5px, 5px, 5px, 5px);
        background: $primary;
    }
}

.footer {
    @extend .center;
    padding-top: 4em;
    padding-bottom: 13px;
}
.foot-nav {
    width: 33%;
}
.foot-nav a {
    color: #8c8c8c;
    padding: 1px;
    &:hover {
        color: $txt-on-primary;
    }
}
.vn00 {
    width: 100%;
    text-align: center;
}

/*  entry page*/
// .ep12 {
//     display: flex;
//     flex-direction: column;
// }
// .ep21 {
//     order: 1;
// }
// .ep121 {
//     order: 2;
//     float: left;
//     font-weight: 500;
//     text-transform: uppercase;
// }
// .ep1211 {
//     order: 4;
// }
// .ep122 {
//     order: 3;
// }
// .ep22 {
//     margin-left: 0;
// }

// .cp30 {
//     font-size: 1em;
//     letter-spacing: 0;
//     line-height: 1.35;
//     &:before,
//     &:after {
//         display: none;
//     }
// }

// .ep95 {
//     @include primary-font;
//     font-size: 1.2em;
// }

// .entry-page {
//     width: 100%;
// }
// .ep10 {
//     width: 100%;
//     &:hover {
//         border-color: $neutral-darker;
//     }
//     &.ep10-map {
//         max-height: 200px;
//         overflow: hidden;
//     }
// }
// .ep23 {
//     background-color: #efefef;
//     border-width: 0;
//     padding-left: calc((100% - #{$page-width}) / 2);
//     padding-right: calc((100% - #{$page-width}) / 2);
//     &::before,
//     &::after {
//         background: transparent;
//     }
// }
// .ep310,
// .ep80 {
//     font-size: 21px;
//     font-weight: bold;
// }
// .ep310 {
//     font-weight: 400;
// }
// .ep31 {
//     background-color: #e1dfda;
//     border: 1px solid $s-dark;
//     color: #343434;
// }
// .ep35 {
//     color: #343434;
//     font-size: 24px;
//     & > strong,
//     .sp201 {
//         font-weight: 100;
//     }
//     & > span {
//         font-weight: bold;
//     }
// }
// .ep511 {
//     background-color: $secondary;
//     color: #efefef;
// }
// .ep81 {
//     text-shadow: none;
// }
// .ep84 {
//     margin-top: 0.5em;
// }
// .ep831,
// .ep841 {
//     background-color: $secondary;
//     color: #efefef;
//     padding: 0.3em 5px;
// }
// .ep852,
// .ep89 {
//     color: #806600;
// }
// .ep9 {
//     border-width: 0;
//     top: 32px;
//     h3 {
//         font-size: 1.7em;
//     }
// }
// .ep92 {
//     background-color: #efefef;
//     color: #676767;
//     padding: 11px 1em;
// }
// .cp30 {
//     font-style: normal;
// }

// .premium-block-entry-page,
// .question-and-answer {
//     @include center;
//     position: relative;
// }

// .sp21 {
//     min-width: 250px;
// }
// .no-info.sp201 {
//     font-weight: 100;
// }

// @include media(">page-width") {
//     .ep0 {
//         border-width: 0px;
//         background-color: transparent;
//         margin: 0 auto;
//         width: $page-width;
//         position: relative;
//         padding-top: 0;
//     }
//     .ep00 {
//         right: 12px;
//     }
//     .ep1 {
//         background-color: transparent;
//     }
//     .ep100 {
//         width: $page-width;
//     }
//     .ep12 {
//         margin-top: 1em;
//         margin-left: 0;
//         margin-bottom: 40px;
//         width: 100%;
//     }
//     .ep1211 {
//         margin-bottom: 0;
//         padding: 0;
//     }
//     .ep122 {
//         float: left;
//     }
//     .ep20 {
//         border-top: 1px solid #1b1b1b;
//     }
//     .ep21 {
//         float: right;
//         margin-top: 0.6em;
//     }
//     .ep22 a {
//         border-radius: 0px;
//         border-color: $s-dark;
//     }
//     .epi1 {
//         margin: 0;
//     }
//     .ep9 {
//         margin: 0 auto;
//         width: $page-width;
//     }
// }
// .epi20 {
//     margin: 0 8px 8px 0;
// }
// .epi31,
// .epi39 {
//     &:before {
//         opacity: 0.6;
//     }
//     &:hover:before {
//         opacity: 1;
//     }
// }
// .epc10 {
//     .rateit-selected,
//     .rateit-hover {
//         color: $positive;
//     }
// }
// .rating-display {
//     @extend .icon-stern5_leer;
// }
// .rating-actual {
//     @extend .icon-stern5_voll;
//     color: #fff;
// }
// .entry-page {
//     .rating-display,
//     .rating-actual {
//         color: #1b1b1b;
//     }
// }

// .epf1 {
//     @extend .center;
// }

.cb-list-table {
    font-weight: 300;
    font-size: 1.5em;
    a {
        border-bottom-color: $secondary;
        padding: 8px 40px 6px;
    }
    strong {
        font-weight: 300; 
    }
}

/* end layout */
/* end overwrites */

/* responsive design */
@import "respond";
.header-main {
    @include media("<=page-width") {
        background: #fff;
    }
}
/* below 768 */
@media only screen and (max-width: 769px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.2em;
    }
}
/* below 380 */
@media only screen and (max-width: 380px) {
    .ms10 {
        font-size: 1em;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";
