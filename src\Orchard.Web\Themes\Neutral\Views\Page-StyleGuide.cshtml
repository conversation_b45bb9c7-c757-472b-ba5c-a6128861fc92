﻿@using Teamaton.Discoverize.Internationalization.Resources
<div class="design-guide">
    <h2>SVGs</h2>
    <h3>Badges</h3>
    <div class="dg321">
        <i></i>
    </div>
    <div class="dg322">
        <i></i>
    </div>
    <div class="dg323 badge-inactive">
        <i></i>
    </div>
    <hr />
    <div class="dg324">
        <i></i>
    </div>
    <div class="dg325 badge-inactive">
        <i></i>
    </div>
    <div class="dg326">
        <i></i>
    </div>
    <hr />
    <div class="dg327 badge-inactive">
        <i></i>
    </div>
    <div class="dg3271">
        <i></i>
    </div>
    <div class="dg328">
        <i></i>
    </div>
    <div class="dg329">
        <i></i>
    </div>
    <hr />
    <div class="dg331">
        <i></i>
    </div>
    <div class="dg332">
        <i></i>
    </div>
    <hr />
    <div class="dg333">
        <i></i>
    </div>
    <div class="dg334">
        <i></i>
    </div>
    <hr />
    <div class="dg335">
        <i></i>
    </div>
    <div class="dg336">
        <i></i>
    </div>
</div>
<div class="design-guide">
    <div class="box1" style="margin-top:24px;   ">
        <div class="map-button-stack"><span>12</span></div>
    </div>
    <div class="box1">
        <h1>Design and Style Guide</h1>
        <p class="txt-l">here you can find all design elements and components that you need for discoverize - styled to the currently selected theme</p>
        <p class="txt-l">here you can find all design elements and components that you need for discasdfoverize - styled to the currently selected theme here you can find all design elements and components that you need for discasdfoverize - styled to the currently selected theme</p>
    </div>
    <div class="box1of2">
        <h2>header 2</h2>
        <p class="txt-l">here you can find all design elements </p>
        <p class="txt-l">here you can find all design elements and components that you need for discasdfoverize - styled to the currently selected theme here you can find all design elements and components that you need for discasdfoverize - styled to the currently selected theme</p>
    </div>
    <div class="box1of2">
        <h3>header 3</h3>
        <p class="txt-l">here you can find all design elements and components that you need for discasdfoverize - styled to the currently selected theme here you can find all design elements and components that you need for discasdfoverize - styled to the currently selected theme</p>
        <p class="txt-l">here you can find all design elements and components that you need for discoverize - styled to the currently selected theme</p>
    </div>

    <div class="box1of3">
        <h2>headers</h2>
        <h1>Header h1</h1>
        <h2>Header h2</h2>
        <h3>Header h3</h3>
        <h4>Header h4</h4>
        <h5>Header h5</h5>
        <h6>Header h6</h6>
        <h2>lists</h2>
    </div>
    <div class="box1of3">
        <h2>text elements</h2>
        <p>normal text</p>
        <p><a href="#">link within text</a></p>
        <blockquote>blockquote text</blockquote>
        <p><span>inline text</span> within text</p>
        <p><strong>emphasized element</strong></p>
        <p><em>italisized text</em></p>
        <p class="info">info text</p>
        <p class="txt-l">large text</p>
        <p class="txt-xl">extra large text</p>
    </div>
    <div class="box1of3">
        <h2>list</h2>
        <ul>
            <li>unordered list</li>
            <li>with elements</li>
            <li>and last element</li>
        </ul>
        <ol>
            <li>ordered list</li>
            <li>with elements</li>
            <li>and last element</li>
        </ol>
    </div>
</div>

<div class="design-guide">
    <div class="box1of3">
        <h2>Colors</h2>
        <ul class="dgcl">
            <li class="dgcl11">primary</li>
            <li class="dgcl12">secondary</li>
            <li class="dgcl13">neutral</li>
            <li class="dgcl14">txt-bg</li>
            <li class="dgcl15">txt-cl</li>
            <li class="dgcl16">txt-light</li>
            <li class="dgcl17">neutral darker</li>
        </ul>
    </div>
    <div class="box1of3">
        <h2>Buttons</h2>
        <p><button class="button-primary">Primary Button</button></p>
        <p><button class="button-secondary">Secondary Button</button></p>
        <p><button class="button-neutral">Neutral Button</button></p>                
        <p><button class="button-disabled">disabled button</button></p>
        <p><button class="button-primary button-l">large button</button></p>
        <p><button class="button-primary button-s">small button</button></p>        
    </div>
    <div class="box1of3">
        <h2>Icons</h2>
        <ul>
            <li><i class="icon-envelope"></i> icon email</li>
            <li><i class="icon-star"></i> icon star</li>
            <li><i class="icon-edit"></i> icon edit</li>
            <li><i class="icon-picture"></i> icon picture</li>
        </ul>
    </div>
</div>

<div class="design-guide">
    <div class="box1of2">
        <h2>Tabs</h2>
        <ul class="tabs">
            <li><a href="#">Übersicht</a></li>
            <li class="active"><a href="#">Lage</a></li>
            <li><a href="#">Fotos (32)</a></li>
        </ul>
        <div class="tab-content">
            <h3>tab content</h3>
        </div>
        <h2>Vertical Tabs</h2>
        <div class="af1">
            <ul class="tabs-vertical">
                <li class="af21"><a href="#tabs-0" data-toggle="tab">Liegeplatz (18)</a></li>
                <li class="af21 active"><a href="#tabs-1" data-toggle="tab">Bootservice (18)</a></li>
                <li class="af21"><a href="#tabs-2" data-toggle="tab">Nautik (8)</a></li>
                <li class="af21"><a href="#tabs-3" data-toggle="tab">Sanitär (8)</a></li>
                <li class="af21"><a href="#tabs-4" data-toggle="tab">Versorgung (18)</a></li>
                <li class="af21"><a href="#tabs-5" data-toggle="tab">Freizeit (18)</a></li>
                <li class="af21"><a href="#tabs-6" data-toggle="tab">Charter (4)</a></li>
            </ul>
            <div data-group-id="#group2" class="tabs-vertical-content active" id="tabs-2">
                <h4>vertical tab content</h4>
                
            </div>
        </div>
    </div>

    <div class="box1of2">
        <h2>Pager</h2>
        <p>inprogress</p>
    </div>
</div>

<div class="design-guide">
    <h2>Popups</h2>
    <div class="modal-large" style="display:block;">
        <h3>Modal Popup</h3>
         <a class="i-close-popup">            
             <span>
                 @RsCommon.Close(TR)
             </span>        
        </a>
        <p>Creating great responsive experiences requires a hell of a lot more than media queries. If you think creating squishy layouts is all this responsive thing is about, you’re missing the point. The fact is we need to deliver a solid user experience to a growing number of web-enabled devices, and creating entirely separate device experiences simply isn’t scalable in the long run. Creating adaptive experiences is a smarter way forward, but that doesn’t mean this approach isn’t without its challenges.</p>
    </div>
    <div class="btn-group" data-toggle="buttons-checkbox">
        <a class="btn collapse-data-btn" data-toggle="collapse" href="#details">Show details</a>
    </div>
    <div id="details" class="collapse">
        <p>Details details details details details details details details...</p>
    </div>
</div>

<div class="design-guide">
    <h2>Map Popup</h2>
    <div class="mappopup" style="display:block; position:static; float:left;">        
        <a href="/knuller/eintragsname" class="sp13">
            <img alt="Marina: kein Bild vorhanden" src="/themes/neutral/styles/img/default-image-1200.jpg?w=100&amp;h=100&amp;mode=crop">    
        </a>
        <div data-map-marker="{id: 24, point: {lat: 44.44440, lng: 13.00000}}" data-id="24" class="sp14">
            <h2 class="sp15"><a href="/knuller/eintragsname" class="jq-link-entry-page">Eintragsname</a></h2>
            <p class="sp16"><strong>Ort:</strong> , Estland</p>
            <p class="sp16" data-property="AmFlussKanal">
                <strong title="am Fluss/Kanal keine Angabe" class="no-info">am Fluss/Kanal</strong>
            </p>
            <p class="sp16" data-property="Vergleichspreis">
                <strong title="Vergleichspreis keine Angabe" class="no-info">Vergleichspreis</strong>
                <i title="täglicher Liegeplatz im Wasser für Schiffe bis 10 m Länge" class="info-i"></i>
           </p>
        </div>
    </div>    
    <div class="mappopup" style="display:block; position:static; float:left; margin-left:2em;">        
        <a href="/knuller/eintragsname" class="sp13">
            <img alt="Marina: kein Bild vorhanden" src="/themes/neutral/styles/img/default-image-1200.jpg?w=100&amp;h=100&amp;mode=crop">    
        </a>
        <div data-map-marker="{id: 24, point: {lat: 44.44440, lng: 13.00000}}" data-id="24" class="sp14">
            <h2 class="sp15"><a href="/knuller/eintragsname" class="jq-link-entry-page">Very very long and descriptive Eintragsname</a></h2>
            <p class="sp16"><strong>Ort:</strong> , Estland</p>
            <p class="sp16" data-property="AmFlussKanal">
                <strong title="am Fluss/Kanal keine Angabe" class="no-info">am Fluss/Kanal</strong>
            </p>
            <p class="sp16" data-property="Vergleichspreis">
                <strong title="Vergleichspreis keine Angabe" class="no-info">Vergleichspreis</strong>
                <i title="täglicher Liegeplatz im Wasser für Schiffe bis 10 m Länge" class="info-i"></i>
           </p>
        </div>
    </div>    
</div>

<div class="design-guide">
    <div class="columnize-3">
        <h2>Columns</h2>
        <p>Over the past few years we’ve seen an explosion of web-enabled devices with varying resolutions, capabilities, form factors, pixel densities, interaction methods and more. This onslaught of connected devices is just the beginning, and we’re bound to see people accessing the web from a greater number of devices in the coming years. It’s futile to create a dedicated web experience for every single device class out there, and the need to create a smart, flexible, adaptable web experiences is becoming more apparent every day. Responsive/adaptive/multi-device web design (whatever you want to call it) is here to stay. Just because responsive design is becoming necessary doesn’t mean it’s easy. We’re tasked with creating experiences that are simultaneously aware and agnostic of device context. We’re challenged to make interfaces that scale from itty bitty screens all the way up to massive cinema displays. Oh, and everything needs to be lightning fast, too.</p>
    </div>
</div>

<div class="design-guide">
    <div class="box1">
        <form class="epc10">
            <fieldset>
                <legend>Form</legend>
                <div class="control-group">
                    <label class="control-label">Label</label>
                    <div class="controls">
                        <textarea class="epc12" rows="6" placeholder="Placeholder Text"></textarea>
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label">Label</label>
                    <div class="controls">
                        <input type="text" placeholder="Placeholder Text">
                    </div>
                </div>
                <div class="control-group">
                    <div class="controls">
                        <button type="submit" class="btn btn-success">Submit</button>
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
</div>

<div class="design-guide">
    <div class="box1">
        <form class="epc10">
            <fieldset>
                <legend>Search Filter Form</legend>
                <h3>availability yes/no</h3>
                <label class="checkbox">
                    <input type="checkbox"> Wlan
                </label>
                <h3>number - inbetween - no segments</h3>
                <div class="control-group">
                    <label class="control-label">Preis</label>
                    <div class="controls">
                        <input type="text" class="xs" placeholder="0">
                        <span>bis</span>
                        <input type="text" class="xs" placeholder="0">
                        <span>EUR</span>
                    </div>
                </div>
                <h3>number - inbetween - with segments - with checkboxes</h3>
                <div class="control-group">
                    <label class="control-label">Preis</label>
                    <div class="controls">
                        <input type="checkbox" /> <span>0-100 EUR</span> <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>100-200 EUR</span>  <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>200-500 EUR</span> <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>500-1000 EUR</span> <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>über 1000 EUR</span> <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="text" class="xs" placeholder="0">
                        <span>bis</span>
                        <input type="text" class="xs" placeholder="0">
                        <span>EUR</span>
                    </div>
                </div>
                <h3>number - inbetween - with segments - with dropdown</h3>
                <div class="control-group">
                    <label class="control-label">Preis</label>
                    <div class="controls">
                        <select class="input-medium">
                            <option>show all</option>
                            <option>0-10 EUR (125)</option>
                            <option>10-20 EUR (142)</option>
                            <option>20-50 EUR (102)</option>
                            <option>50-100 EUR (112)</option>
                            <option>über 100 EUR (121)</option>
                        </select>
                    </div>
                    <div class="controls">
                        <input type="text" class="xs" placeholder="0">
                        <span>bis</span>
                        <input type="text" class="xs" placeholder="0">
                        <span>EUR</span>
                    </div>
                </div>
                <h3>number - more or equal - no segments</h3>
                <div class="control-group">
                    <label class="control-label">Durchfahrtshöhe</label>
                    <div class="controls">
                        <span>mind.</span>
                        <input type="text" class="xs" placeholder="1">
                        <span>m</span>
                    </div>
                </div>
                <h3>number - more or equal - with segments</h3>
                 <div class="control-group">
                    <label class="control-label">Durchfahrtshöhe</label>
                    <div class="controls">
                        <input type="checkbox" /> <span>mind. 5m</span> <span>(120)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>mind. 10m</span>  <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>unbegrenzt</span> <span>(1)</span>
                    </div>
                    <div class="controls">
                        <span>mind. </span>
                        <input type="text" class="xs" placeholder="0">
                        <span>m</span>
                    </div>
                </div>

                <h3>number - less or equal - no segments</h3>
                <div class="control-group">
                    <label class="control-label">Stadt</label>
                    <div class="controls">
                        <span>max.</span>
                        <input type="text" class="xs" placeholder="1">
                        <span>km</span>
                    </div>
                </div>
                <h3>number - less or equal - with segments</h3>
                 <div class="control-group">
                    <label class="control-label">Stadt</label>
                    <div class="controls">
                        <input type="checkbox" /> <span>direkt</span> <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>max. 5km</span>  <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>max. 10km</span> <span>(12)</span>
                    </div>
                    <div class="controls">
                        <span>max.</span>
                        <input type="text" class="xs" placeholder="0">
                        <span>km</span>
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label">Stadt</label>
                    <div class="controls">
                        <select class="input-medium">
                            <option>max. 5 km</option>
                            <option>max. 10 km</option>
                            <option>max. 15 km</option>
                            <option>max. 25 km</option>
                        </select>
                        <input type="text" class="xs" />
                        <span>t</span>
                    </div>
                </div>
                <div class="control-group">
                    <div class="controls">
                        <button type="submit" class="btn btn-success">Submit</button>
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
    <div class="box1">
        <form class="epc10">
            <fieldset>
                <legend>Mini-Search Form</legend>
                <h3>availability yes/no</h3>
                <label class="checkbox">
                    <input type="checkbox"> Wlan
                </label>
                <h3>number - inbetween - no segments</h3>
                <div class="control-group">
                    <label><a class="jq-toggle sc-open" data-target="#price">Preis <i class="icon icon-chevron-thin-down"></i></a></label>
                    <div class="hide sc-popup" id="price">
                        <input type="text" class="xs" placeholder="0">
                        <span>bis</span>
                        <input type="text" class="xs" placeholder="0">
                        <span>EUR</span>
                    </div>
                </div>
                <h3>number - inbetween - with segments - with checkboxes</h3>
                <div class="control-group">
                    <label><a class="jq-toggle sc-open" data-target="#price-check">Preis <i class="icon icon-chevron-thin-down"></i></a></label>
                    <div class="hide sc-popup" id="price-check">
                        <div>
                            <input type="checkbox" /> <span>0-100 EUR</span> <span>(12)</span>
                        </div>
                        <div>
                            <input type="checkbox" /> <span>100-200 EUR</span>  <span>(12)</span>
                        </div>
                        <div>
                            <input type="checkbox" /> <span>200-500 EUR</span> <span>(12)</span>
                        </div>
                        <div>
                            <input type="checkbox" /> <span>500-1000 EUR</span> <span>(12)</span>
                        </div>
                        <div>
                            <input type="checkbox" /> <span>über 1000 EUR</span> <span>(12)</span>
                        </div>
                        <div>
                            <input type="text" class="xs" placeholder="0">
                            <span>bis</span>
                            <input type="text" class="xs" placeholder="0">
                            <span>EUR</span>
                        </div>
                    </div>
                </div>
                <h3>number - inbetween - with segments - with dropdown</h3>
                <div class="control-group">
                    <label class="control-label">Preis</label>
                    <div class="controls">
                        <select class="input-medium">
                            <option>show all</option>
                            <option>0-10 EUR (125)</option>
                            <option>10-20 EUR (142)</option>
                            <option>20-50 EUR (102)</option>
                            <option>50-100 EUR (112)</option>
                            <option>über 100 EUR (121)</option>
                        </select>
                    </div>
                    <div class="controls">
                        <input type="text" class="xs" placeholder="0">
                        <span>bis</span>
                        <input type="text" class="xs" placeholder="0">
                        <span>EUR</span>
                    </div>
                </div>
                <h3>number - more or equal - no segments</h3>
                <div class="control-group">
                    <label class="control-label">Durchfahrtshöhe</label>
                    <div class="controls">
                        <span>mind.</span>
                        <input type="text" class="xs" placeholder="1">
                        <span>m</span>
                    </div>
                </div>
                <h3>number - more or equal - with segments</h3>
                 <div class="control-group">
                    <label class="control-label">Durchfahrtshöhe</label>
                    <div class="controls">
                        <input type="checkbox" /> <span>mind. 5m</span> <span>(120)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>mind. 10m</span>  <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>unbegrenzt</span> <span>(1)</span>
                    </div>
                    <div class="controls">
                        <span>mind. </span>
                        <input type="text" class="xs" placeholder="0">
                        <span>m</span>
                    </div>
                </div>

                <h3>number - less or equal - no segments</h3>
                <div class="control-group">
                    <label class="control-label">Stadt</label>
                    <div class="controls">
                        <span>max.</span>
                        <input type="text" class="xs" placeholder="1">
                        <span>km</span>
                    </div>
                </div>
                <h3>number - less or equal - with segments</h3>
                 <div class="control-group">
                    <label class="control-label">Stadt</label>
                    <div class="controls">
                        <input type="checkbox" /> <span>direkt</span> <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>max. 5km</span>  <span>(12)</span>
                    </div>
                    <div class="controls">
                        <input type="checkbox" /> <span>max. 10km</span> <span>(12)</span>
                    </div>
                    <div class="controls">
                        <span>max.</span>
                        <input type="text" class="xs" placeholder="0">
                        <span>km</span>
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label">Stadt</label>
                    <div class="controls">
                        <select class="input-medium">
                            <option>max. 5 km</option>
                            <option>max. 10 km</option>
                            <option>max. 15 km</option>
                            <option>max. 25 km</option>
                        </select>
                        <input type="text" class="xs" />
                        <span>t</span>
                    </div>
                </div>
                <div class="control-group">
                    <div class="controls">
                        <button type="submit" class="btn btn-success">Submit</button>
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
</div>

<div class="design-guide">
    <h2 class="ep91">@("Statische Eigenschaften (Designstudien) als Vergleich noch drin gelassen")</h2>
    <h3 class="ep92">Availability</h3>
    <ul class="ep95">
        <li class="no"><i class="icon-minus-sign"></i>Casino <i class="icon-info-sign"></i></li>
        <li><i class="icon-ok-sign"></i>Fitness</li>
        <li><i class="icon-ok-sign"></i>Flughafen-Shuttle</li>
        <li class="no"><i class="icon-minus-sign"></i>Golf</li>
        <li class="no"><i class="icon-minus-sign"></i>Internet</li>
        <li class="no"><i class="icon-minus-sign"></i>Parkplätze</li>
        <li><i class="icon-ok-sign"></i>Restaurant</li>
        <li><i class="icon-ok-sign"></i>Spa</li>
        <li><i class="icon-question-sign" title="keine Angabe"></i>Whirlpool</li>
    </ul>
    <h3 class="ep92">Text</h3>
    <ul class="ep95">
        <li><strong>Allgemeine Geschäftsbedingungen</strong>: <span>Subject to your compliance with these Conditions of Use and your payment of any applicable fees, IMDb or its content providers grants you a limited, non-exclusive, non-transferable, non-sublicenseable license to access and make personal and non-commercial use of the IMDb Services, including digital content available through the IMDb Services, and not to download (other than page caching) or modify this site, or any portion of it, except with express written consent of IMDb.</span></li>
        <li><strong>Anreisebestimmungen</strong>: <span>Personalausweis und Reisebestätigung bereithalten</span></li>
        <li><strong>Beschreibung kleine Suite</strong>: <span>Kempinski Hotel Bristol Berlin dieses Hotel liegt in Berlin, Charlottenburg-Wilmersdorf, in der Nähe der folgenden Sehenswürdigkeiten: Theater des Westens, Reichstagsgebäude und Brandenburger Tor. Weitere Sehenswürdigkeiten sind: Checkpoint Charlie Museum und Kaiser-Wilhelm-Gedächtnis-Kirche. Zum gastronomischen Angebot im Kempinski Hotel Bristol Berlin zählen 3 Restaurants.</span></li>
        <li><strong>Webseite</strong>: <span>nicht vorhanden</span></li>
        <li><strong>Fax</strong>: <span>keine Angabe</span></li>
    </ul>
    <h3 class="ep92">Number</h3>
    <ul class="ep95">
        <li><strong>Tankstelle:</strong> <span>vor Ort</span></li>
        <li><strong>Durchfahrtshöhe</strong>: <span>unbeschränkt</span></li>
        <li><strong>Preis für @Html.PremiumName() Liegeplatz</strong>: <span>€239</span></li>
        <li><strong>maximale Bootslänge</strong>: <span>50m</span></li>
        <li><strong>Anzahl Gästeliegeplätze</strong>: <span>355</span></li>
        <li><strong>Durchfahrtshöhe</strong>: <span>unbeschränkt</span></li>
    </ul>
    <h3 class="ep92">Date and Time</h3>
    <ul class="ep95">
        <li><strong>Geöffnet</strong>: <span>von 14.2. bis 29.10.</span></li>
        <li><strong>Öffnungszeiten unter der Woche</strong>: <span>10:00 Uhr bis 20:00 Uhr</span></li>
        <li><strong>Öffnungszeiten Wochenende</strong>: <span>15:00 Uhr bis 20:00 Uhr</span></li>
    </ul>
    <h3 class="ep92">Choice</h3>
    <ul class="ep95">
        <li><strong>Unterstützte Hersteller:</strong> <span>VW, Mercedes, Audi</span></li>
        <li><strong>Uferseite</strong>: <span>Rechte Seite</span></li>
    </ul>
    <p><i class="question"></i> icon question</p>
    <p><i class="info-i"></i> icon info</p>
    <p><i class="minus"></i> icon minus</p>
    <p><i class="check"></i> icon check</p>
    <p><i class="close"></i> icon close</p>

    <h3>availability</h3>
    <p><strong title="vorhanden" class="available">vorhanden</strong></p>
    <p><strong title="nicht vorhanden" class="not-available">nicht vorhanden</strong></p>
    <p><strong title="keine Angabe" class="no-info">keine Angabe</strong></p>

    <p><strong title="vorhanden" class="available">vorhanden explained</strong> <i class="info-i"></i></p>
    <p><strong title="nicht vorhanden" class="not-available">nicht vorhanden explained</strong> <i class="info-i"></i></p>
    <p><strong title="keine Angabe" class="no-info">keine Angabe explained</strong> <i class="info-i"></i></p>

    <h3>number, text</h3>
    <p><strong title="Zahl vorhanden" class="available">Zahl:</strong> <span>12</span></p>
    <p><strong title="Zahl nicht vorhanden" class="not-available" data-nosnippet aria-hidden="true">Zahl</strong></p>
    <p><strong title="Zahl keine Angabe" class="no-info">Zahl</strong></p>

    <p><strong title="vorhanden" class="available">Zahl:</strong> <span>12</span> <i class="info-i" title="info text"></i> </p>
    <p><strong title="nicht vorhanden" class="not-available">Zahl nicht vorhanden</strong> <i class="info-i"></i></p>
    <p><strong title="keine Angabe" class="no-info">Zahl keine Angabe</strong> <i class="info-i"></i></p>
</div>

<div class="design-guide">
    <h2>Boxes - in progress</h2>
    <div class="box1of3" style="background:#ddd;">
        <h3>box 1 of 3</h3>
    </div>
    <div class="box1of3" style="background:#ddd;">
        <h3>box 1 of 3</h3>
    </div>
    <div class="box1of3" style="background:#ddd;">
        <h3>box 1 of 3</h3>
    </div>
    <div class="box2of3" style="background:#ddd;">
        <h3>box 2 of 3</h3>
    </div>
    <div class="box1of3" style="background:#ddd;">
        <h3>box 1 of 3</h3>
    </div>
    <div class="box1of2" style="background:#ddd;">
        <h3>box 1 of 2</h3>
    </div>
    <div class="box1of2" style="background:#ddd;">
        <h3>box 1 of 2</h3>
    </div>
    <div class="box1of4" style="background:#ddd;">
        <h3>box 1 of 4</h3>
    </div>
    <div class="box1of4" style="background:#ddd;">
        <h3>box 1 of 4</h3>
    </div>
    <div class="box1of4" style="background:#ddd;">
        <h3>box 1 of 4</h3>
    </div>
    <div class="box1of4" style="background:#ddd;">
        <h3>box 1 of 4</h3>
    </div>
    <div class="box1of2" style="background:#ddd;">
        <h3>box 1 of 2</h3>
    </div>
    <div class="box1of4" style="background:#ddd;">
        <h3>box 1 of 4</h3>
    </div>
    <div class="box1of4" style="background:#ddd;">
        <h3>box 1 of 4</h3>
    </div>
    <div class="box3of4" style="background:#ddd;">
        <h3>box 1 of 4</h3>
    </div>
    <div class="box1of4" style="background:#ddd;">
        <h3>box 1 of 4</h3>
    </div>
</div>

@using (Script.Foot()) {
    <!--disabled for edit mode
    <script type="text/javascript">
        $(function () {
            var $container = $('.design-guide');
            $container.find('*').each(function (i, el) {
                var $el = $(el);
                var cssclass = $el.attr('class');
                if (cssclass && cssclass != '') {
                    $el.attr('title', $el.prop('tagName').toLowerCase() + '.' + cssclass).tooltip({
                        delay: 500, /* how long the tooltip should show after moving the mouse away from the trigger element */
                        position: 'top center',
                        offset: [50, 0]
                    });
                }
            });
        })
    </script>
    -->
}
    <script type="text/javascript">
        $(function () {
            $(".jq-toggle").on("click", function () {
                $($(this).data("target")).slideToggle();
            })
        });
        $('.columnize-3').columnize({ columns: 3 });
    </script>
