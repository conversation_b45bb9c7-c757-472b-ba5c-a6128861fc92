﻿/* steuerberater theme for discoverize.com portal - created 21.06.18 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */
@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";

@import "4_layouts/_old-search-layout-tiny-images";
$cb-bg-hp: rgba(255, 255, 255, 1);

/* end import */

/* overwrites */

/* graphics */

body {
    background: none;
}
body {
    @include secondary-font; 
}

/* end graphics */

/* global */

h1,
h2,
h3,
h4,
h5,
h6 {
    @include primary-font;
    color: $primary;
}
.button-primary,
.primary-action,
.button-secondary,
.secondary-action {
    @include remove-text-background-offset;
    &:hover {
        @include remove-text-background-offset;
    }
}
.sf632,
.sf72 {
    @include hide-text;
}

body .blog-teaser-list {
    .blgt85 {
        @extend .button-primary; 
    }
}

/* end global */

/* layout */

.search-page {
    margin-top: 0;
    padding-top: 0;
}

/* end layout */

/* header */

.header-top {
    background: darken($primary-darkest, 7%);
}
.tagline {
    @include primary-font;
    color: #fff;
    padding: 0;
    float: left;
    font-size: 0.85em;
}
.mini-nav {
    a,
    a:hover {
        color: #fff;
        border-right: none;
    }
    a:hover {
        background: $primary;
    }
    span {
        color: #eee;
    }
    .mn11 {
        border-right: none;
    }
}

.header-main {
    background: $primary;
}
.logo {
    padding-top: 10px;
    padding-bottom: 6px;
    float: left;
    margin-right: 12px;
    img {
        max-height: 50px;
    }
}

// .nav {
//     @include border-box;
//     margin: 0;
//     position: relative;
//     z-index: 3;
// }

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 23px 12px 19px 12px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav a {
//     @include border-box;
//     @include primary-font;
//     border: none !important;
//     background: none;
//     color: #fff;
//     display: block;
//     padding: 23px 12px 19px 12px; 
// }
// .nav .current {
//     background: $primary-darker;
//     color: $txt-on-primary;
// }

// .nav > li:hover > a {
//     background: $primary-darker;
// }

// a.nav01 {
//     @extend .icon-home;
// }
// .nav .nav19,
// .nav .nav10 {
//     @include rounded(0, 0, 0, 0);
// }

// .nav-level-2 {
//     @include shadow-remove;
//     left: auto;
//     right: 0;
//     background: none;
//     border: none;
// } 
// .nav-level-2 a {
//     background: transparentize($primary-darker, 0.1);
//     color: #fff;
// }
// .nav-level-2 a:hover {
//     background: $primary-darker;
// }

.global-search {
    @include global-search(180px);
    padding: 17px 3px 6px 0;
    color: #ccc;
}

/* end header */

/* homepage */

.mini-search {
    @include background-responsive("img/bg.jpg");
    @include background-responsive(
        "img/bg.jpg",
        $image-height-mobile: 710,
        $image-height-desktop: 800
    );
    min-height: 800px;
    background: no-repeat 0 0;
    background-size: cover;
    background-position: initial;
}
.mini-search:before {
    background: rgba(0, 0, 0, 0.3);
}
.mini-search-inside {
    padding-bottom: 8em;
}
.ms25 {
    @extend .button-secondary;
}
.ms261 {
    @extend .button-primary;
}
.ms10 {
    color: #fff;
    font-size: 1.5em;
}

.mini-gallery12 {
    @extend .icon-search-plus;
}
.area10 {
    @extend .icon-map-marker;
}
.blgt0 {
    @extend .icon-comment-exclamation;
}
/* end homepage */

/* search page */

.sf61-8, .epf61-8 { 
    @extend .icon-handshake;
}
.sf61-14, .epf61-14 { 
    @extend .icon-door-open;
}
.sf61-5, .epf61-5 { 
    @extend .icon-ma-stats;
}
.sf61-11, .epf61-11 { 
    @extend .icon-globe;
}
.sf61-7, .epf61-7 { 
    @extend .icon-comments;
}
.sf61-6, .epf61-6 { 
    @extend .icon-info-sign;
}

.map-button {
    color: #fff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5), 0 0 1px rgba(0, 0, 0, 0.5);
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-steuerberater-logo;
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-steuerberater-logo;
        &:before {
            left: 0px;
            top: 3px;
            font-size: 23px;
        }
    }
}
.map-marker-premium span.map-marker-icon:before {
    left: 2px;
    top: 5px;
    font-size: 28px;
}
.map-marker-static span.map-marker-icon,
.map-marker-premium.map-marker-primary span.map-marker-icon {
    &:before {
        left: -1px;
        top: 3px;
        font-size: 23px;
    }
}

.sp110 {
    @include linear-gradient(
        transparentize(lighten($primary, 50), 0.9),
        transparentize(lighten($primary, 45), 0),
        0%,
        66%
    );
}
.sp19 {
    background: #fff;
    color: $primary;
    border: solid 1px $primary;
    &:hover {
        background: $primary;
        color: #fff;
    }
}

/* map marker icon font-size reapplied dut to IE */

// .map-marker span.map-marker-icon::before, .map-marker-static span.map-marker-icon::before {font-size: 28px}

/* end search page */

/* blog */

.blg7 {
    top: -14px;
    right: 0;
}

/* end blog */

.design-guide {
    border-bottom: solid 1px #ccc;
}

blockquote {
    font-weight: 400;
    color: #777;
}

footer {
    border: none;
    width: 100%;
    background: #161616;
}

.footer-top {
    @include footer-top(".icon-steuerberater-map-marker", $primary-darkest);
}

.logo-footer {
    padding: 16px;
    display: block;
    &:hover {
        @include rounded(5px, 5px, 5px, 5px);
        background: $primary;
    }
}
.footer {
    a {
        border-bottom-width: 0px;
    }
}

a.social-media-footer-icons {
    display: inline;
}
.foot-logo {
    display: block;
    padding: 1em 0 2em 0;
    .mini-tagline {
        width: auto; 
        text-align: center;
    }
}
.footer .foot-nav {
    width: 33.333%;
    margin-bottom: 2em;
    a {
        color: #fff;
        padding: 4px 0;
    }
}
.footer-mobile-login {
    border-top-width: 0px;
}

/* end layout */
/* end overwrites */

/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: $page-width) {
    .global-search {
        padding-top: 6px;
    }
}
/* below 768 */
@media only screen and (max-width: 769px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 639px) {
    .footer .foot-nav {
        width: 100%;
    }
}
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.2em;
    }
}
/* below 380 */
@media only screen and (max-width: 380px) {
    .ms10 {
        font-size: 1em;
    }
}
/* below 320 */
@media only screen and (max-width: 319px) {
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";
