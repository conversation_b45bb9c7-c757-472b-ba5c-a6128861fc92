﻿/* trainingsland theme for discoverize.com portal - created 6.05.16 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";

/* end import */

/* overwrites */

/* graphics */

body {
    position: relative;
    background: #ffffff;
    font-family: trebuchet ms, sans-serif; 
}

//Cookie consent link to Datenschutzerklärung
.cc12 {
    color: $primary-lighter;
}
/* end graphics */

/* type */

h1,
h2,
h3,
h4,
h5,
h6 {
    color: $primary;
}

/* end type */

/* layout */

.header-top {
    background: #fff;
}
.tagline {
    text-align: center;
    color: #666;
    padding: 0;
    font-size: 0.955em;
    float: left;
    clear: left;
}

.header-main {
    @include shadow-no-top-s-light;
    background: $tertiary;
    .home & {
        @include shadow-remove;
        @include media(">page-width") {
            background: transparent;
        }
    }
}

.logo {
    margin-bottom: 16px;
    margin-top: 16px;
    width: 350px;
}

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 13px 12px 8px 12px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav {
//     background: none;
//     a {
//         @include primary-font;
//         background: none;
//         color: #fff;
//         font-size: 1.1em;
//         padding: 13px 12px 8px 12px;
//         font-weight: bold;
//     }
//     a:hover,
//     a.current {
//         background: darken($tertiary, 10);
//         color: $txt-on-tertiary;
//     }
// }
// .nav01 {
//     @extend .icon-home;
// }

.global-search {
    @include global-search(120px);
    padding: 6px 3px 6px 0;
    color: #ccc;
}

@include mini-search-full-page(#fff, "", (#000, 0.3));

.search-main {
    background: none;
}
.mini-search-entry-types-tabs {
    height: 52px;
}
.mini-search-entry-type {
    padding-bottom: 8px;
}
.ms10 {
    color: $primary;
    font-size: 64px;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}
.ms23 {
    color: $primary;
    font-weight: 600;
    margin-top: 1.5em;
}
.ms25 {
    background-color: #fff;
    color: $primary;
    text-shadow: none;
    &:hover {
        background: $primary;
        color: $txt-on-primary;
    }
}

.ms261 {
    color: $primary;
    font-size: 20px;
}

/* home page */

.cb-trl-town-list {
    @extend .cb-picture-list-4;
    .cb-picture-item {
        background: $tertiary;
        &:after,
        &:hover:after {
            background: rgba(0, 0, 0, 0);
        }
        &:hover {
            background: darken($tertiary, 10);
        }
        img {
            background: none;
            padding-top: 24px;
            width: auto;
            display: inline-block;
        }
    }
    .cb-picture-content {
        @include remove-text-background-offset;
        display: block;
        position: static;
        font-size: 1.35em;
        padding: 12px 0;
        font-style: italic;
        strong {
            font-weight: bold;
        }
    }
}

.cb-trl-tag-list {
    @extend .cb-tag-list;
    a,
    a:hover {
        padding: 8px;
        color: $txt-on-secondary;
        font-style: italic;
    }
    a:hover {
        background: darken($secondary, 10);
        text-decoration: none;
    }
    img {
        @include shadow-remove;
        background: none;
        border: none;
        width: auto;
        display: inline-block;
        margin-right: 8px;
    }
    strong,
    .cb-tag-amount {
        top: -15px;
        position: relative;
    }
}

.cb-trl-3-step {
    text-align: center;
    div.box-left-33 {
        padding: 16px 8px 12px 8px;
        position: relative;
        margin: 0;
        width: 33.3333333%;
        &:after {
            top: 50%;
            margin-top: -48px;
        }
        @include media("<=600px") {
            width: 100%;
            margin: 0 auto;
            float: none;
            padding-top: 60px;
            &:after {
                left: 50%;
                bottom: -96px;
                top: auto;
                margin-left: -48px;
            }
            i {
                font-size: 3em;
            }
        }
    }
    h3,
    h4,
    h5,
    i {
        color: #fff;
        font-style: italic;
    }
    h4 {
        font-size: 0.9em;
    }
    i {
        display: inline-block;
        font-size: 6em;
    }
}
.cb-trl-step-1 {
    background: $primary;
    &:after {
        @include triangle-r(48px, $primary);
    }
    @include media("<=600px") {
        padding-top: 16px !important;
        &:after {
            @include triangle-b(48px, $primary);
        }
    }
    i {
        @extend .icon-search;
    }
}
.cb-trl-step-2 {
    background: $secondary;
    &:after {
        @include triangle-r(48px, $secondary);
    }
    @include media("<=600px") {
        &:after {
            @include triangle-b(48px, $secondary);
        }
    }
    i {
        @extend .icon-equalizer;
    }
}
.cb-trl-step-3 {
    background: $tertiary;
    i {
        @extend .icon-kettle-bell;
    }
}

/* end home page */

/* search page */
.premium-booked {
    /*@include stripes-close(transparentize($primary, 0.75));*/
}

.sp150 {
    font-size: 1.05em;
}
.sf61 {
    font-size: 0.9em;
}

.sf61-8, .epf61-8 { 
    @extend .icon-home;
}
.sf61-9, .epf61-9 { 
    @extend .icon-cutlery;
}
.sf61-10, .epf61-10 { 
    @extend .icon-hotel;
}
.sf61-11, .epf61-11 { 
    @extend .icon-steamroom;
}

.sf61-12, .epf61-12 { 
    @extend .icon-massage;
}
.sf61-13, .epf61-13 { 
    @extend .icon-aroma-therapy;
}
.sf61-14, .epf61-14 { 
    @extend .icon-leistungen_white;
}
.sf61-15, .epf61-15 { 
    @extend .icon-swimming-pool;
}
.sf61-16, .epf61-16 { 
    @extend .icon-location-arrow;
}

.map-marker {
    @include map-marker(30px, #fff, $primary);
}
.map-marker-primary,
.map-marker-active {
    @include map-marker(30px, darken($primary, 20), #fff, darken($primary, 30));
}
.map-marker-premium {
    @include map-marker(38px, $primary, $txt-on-primary, #fff, 2px);
    &.map-marker-active {
        @include map-marker(
            38px,
            darken($primary, 12),
            $txt-on-primary,
            darken($primary, 30),
            2px
        );
    }
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-flag;
    &:before {
        font-size: 14px;
        left: 1px;
        top: 5px;
    }
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-flag;
        &:before {
            font-size: 19px;
            left: 2px;
            top: 6px;
        }
    }
}
.map-marker-premium span.map-marker-icon {
    &:before {
        left: 4px;
        top: 7px;
        font-size: 24px;
    }
}


/* end search page */

/* entry page */
/* auszeichnung eintragsseite */
.epea1 {
    position: absolute;
    right: 8px;
    bottom: 8px;
}
.epea13 {
    display: none;
}
/* end entry page */

/* kategorie icons */
/* preisniveau */

// €
.Preisniveau_1 {
    @include category-euros(1);
}
// €€
.Preisniveau_2 {
    @include category-euros(2);
}
// €€€
.Preisniveau_3 {
    @include category-euros(3);
}
// €€€€
.Preisniveau_4 {
    @include category-euros(4);
}
.Preisniveau_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}
.sf711:not([for^="Probetraining_"]) .search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}
.sf711[for^="Probetraining_"] {
    .sf720 {
        align-items: center;
    }
    .search-selected-category-text {
        font-size: 1rem;
    }
}
/* end kategorie icons */

.design-guide {
    border-bottom: solid 1px #ccc;
}

footer {
    @extend %shadow-top;
    background: $primary;
    padding-top: 0;
}

.footer-top {
    @include footer-top(".icon-flag", $bg-color: $tertiary, $font-color: #fff);
    h3 {
        font-size: 3em;
        text-transform: uppercase;
        font-weight: 800;
        font-style: italic;
    }
    h4 {
        font-size: 1.6em;
        font-weight: 400;
    }
    .footer-top12 {
        @extend .button--l, .icon-arrow-right;
        margin-top: 2em;
        margin-bottom: 0.8em;
        font-size: 1.8em;
        font-weight: 800;
        letter-spacing: 1px;
    }
}

.footer-logo {
    padding: 30px 20px 40px;
}
.foot-logo {
    @include group;
    display: block;
    float: none;
    margin-bottom: 1em;
    .tagline {
        width: 100%;
        color: #ccc;
    }
    .logo-name {
        color: #ccc;
    }
}
.foot-nav {
    width: 20%;
    a {
        color: #fff;
    }
}

.pbi12 {
    font-size: 3em;
    &::before {
        color: $primary;
    }
    @include media("<=page-width") {
        color: $grey;
    }
}
.pbi2 .ec-name {
    font-weight: bold;
}
.blgt0 {
    padding-bottom: 0.2em;
    &::before {
        color: $primary;
    }
    @include media(">page-width") {
        font-size: 2.7em;
    }
}
.blgt1 {
    header {
        font-size: 16px;
    }
}
.blgt33 a,
.blgt34 {
    color: $secondary-darker;
    font-weight: 700;
}
.blgt33::before {
    color: $primary;
}
.blgt34 {
    font-size: 1.2em;
}
.blgt85 {
    font-weight: 700;
}

.area10,
.prop10 {
    font-size: 2em;
}

/* end layout */
@media screen and (min-width: $page-width) {
    .sticky-header {
        background-color: $tertiary;
        .header-main-inside {
            width: 100%;
        }
    }
}

/* end overwrites */

/* responsive design */
@import "respond";
/* below page-width */
@media only screen and (max-width: $page-width) {
    .premium-block {
        background: #fff;
    }
    .main-wrap {
        background-image: none;
    }
}
@media only screen and (max-width: 960px) {
    .nav {
        & > li {
            width: auto;
        }
        & > li:last-child a {
            border-right: none;
        }
        a {
            color: $link-color;
        }
    }
}
/* below 768 */
@media only screen and (max-width: 767px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.1em;
        padding: 0;
    }
    .mini-search legend {
        margin-bottom: 0;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

// Cookies
.cc_dialog {
    @include cookies(
        $type-of-graphic: logo,
        $graphic: 'https://trainingsland.de/themes/trainingsland/styles/img/logo-trainingsland-(hash3752449211).png',
        $theme: colorful,
    );
}