/* variables for skigebiete - moved 23.11.2016 - by andrej telle - discoverize.com */

/* variables */
$page-width: 1161px;
$mobile-ui: $page-width;
$mini-search-theme-color: rgba(255, 255, 255, 0.7);
$mini-search-theme-txt-color: #f8a500;

$button-options: (
    "border": false,
    "rounded-corners": true,
    "rounded-corners-value": 5px 5px 5px 5px,
    "gradient-background": false,
    "text-shadow": false,
    "shadow": false,
);

/* colors */
$primary: #f8a500;
$primary-darker: #004883;
$primary-lighter: #ffbc4b;
$primary-bright: #faa619;
$txt-on-primary: #333;
$txt-shadow-on-primary: rgba(0, 0, 0, 0.45);

$secondary: $primary;
$secondary-darker: #004883;
$secondary-lighter: lighten($secondary, 5%);
$txt-on-secondary: #fff;
$txt-shadow-on-secondary: rgba(0, 0, 0, 0.45);

$link-color: darken($primary, 10);
$sticky-nav-link-color: darken($primary, 10%);

$grey: #111212;
$bg-area: #d9eaf9;
$bg-properties: #acd2ee;

$nav-link-padding: 12px 12px !default;
$nav-font-size: 1em !default;

$nav-color: $primary;
$nav-txt-color: $txt-on-primary;

$nav-color-current: lighten($primary,5%);
$nav-txt-color-current: $txt-on-primary;

$flat-nav-bg: #fff;
$flat-nav-txt: $link-color;

$flat-nav-bg-current: $primary;
$flat-nav-txt-current: $txt-on-primary;


/* end colors */


/* typography */
$footer-bg: $secondary-darker;
$footer-links-color: $primary;
$footer-headers-color: $primary;
$footer-links-hover-color: $primary;

$footer-top-bg-color: $txt-on-primary;

$footer-custom-1-color: $primary;

$footer-custom-2-color: $primary;

/* Import Google Web Fonts*/
/* rajdhani-500 - latin */
@font-face { font-display: fallback; 
    font-family: "Rajdhani";
    font-style: normal;
    font-weight: 500;
    src: url("./fonts/rajdhani-v9-latin-500.eot"); /* IE9 Compat Modes */
    src: local("Rajdhani Medium"), local("Rajdhani-Medium"),
        url("./fonts/rajdhani-v9-latin-500.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/rajdhani-v9-latin-500.woff2") format("woff2"),
        /* Super Modern Browsers */ url("./fonts/rajdhani-v9-latin-500.woff")
            format("woff"),
        /* Modern Browsers */ url("./fonts/rajdhani-v9-latin-500.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/rajdhani-v9-latin-500.svg#Rajdhani") format("svg"); /* Legacy iOS */
}
/* rajdhani-600 - latin */
@font-face { font-display: fallback; 
    font-family: "Rajdhani";
    font-style: normal;
    font-weight: 600;
    src: url("./fonts/rajdhani-v9-latin-600.eot"); /* IE9 Compat Modes */
    src: local("Rajdhani SemiBold"), local("Rajdhani-SemiBold"),
        url("./fonts/rajdhani-v9-latin-600.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/rajdhani-v9-latin-600.woff2") format("woff2"),
        /* Super Modern Browsers */ url("./fonts/rajdhani-v9-latin-600.woff")
            format("woff"),
        /* Modern Browsers */ url("./fonts/rajdhani-v9-latin-600.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/rajdhani-v9-latin-600.svg#Rajdhani") format("svg"); /* Legacy iOS */
}
@mixin primary-font {
    font-family: "Rajdhani", sans-serif;
    font-weight: 500;
}

/* end typography */

/* end variables */
