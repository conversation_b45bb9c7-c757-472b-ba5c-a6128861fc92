﻿@using Teamaton.Discoverize.ContentBlocks
@{
    Html.Title($"{Html.PortalName()} {Html.ContentBlockTitle("Datenschutzerklärung")}");
}

@*for other consent managers PO needs to implement its own button in contentblock*@
@if (Html.ShowDiscoverizeConsentPopup()) {
    <button class="open-cookie-consent-tool button-primary" type="button">Cookie-Einstellungen anpassen</button>
}

@* we cannot use constants for content block names here, the strings are currently used
   for content block names by the content blocks controller *@
@Html.ContentBlock("Datenschutzerklärung")