/* css for responsive design - media queries for discoverize/marinas - created by and<PERSON>j telle - discoverize.com */

/* for stock android on rating form  via browser sniffing */

.modal-android {
    .modal {
        position: absolute;
        overflow: visible;
    }
    .modal-open {
        overflow: visible;
    }
}

/* and for stock android */

/* above 1320 for additional block */

$additional-block-page-width: $page-width + ($additional-block-width * 2) + 60px;

@media only screen and (min-width: ($additional-block-page-width)) {
    .additional-block, .home-page .additional-block {
        display: block;
    }
}

/* end above 1320 */

@mixin responsive-daily-opening-hours-table {
    display: block;
    thead,
    tbody,
    th,
    tr,
    td,
    td span { 
        display: block;
        
    }
    thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }
    tbody > tr:last-child td {
        border-bottom: 1px solid $neutral;
        &:last-child {
            border-bottom-width: 0px;
        }
    }
    td, th {
        min-height: 36px;
    }
    td {
        border: none;
        position: relative;
        padding: 0 0 0 100px;
    }
    td span, td time {
        display: block;
        padding: 6px 12px 6px;
    }

    td:before {
        background-color: rgba(0, 0, 0, 0.05);
        font-weight: bold;
        color: darken($txt-color, 5);
        position: absolute;
        bottom: 0;
        top: 0;
        left: 0;
        width: 76px;
        padding: 6px 12px 6px;
        white-space: nowrap;
    }
    td:nth-of-type(1):before {
        content: "Montag";
    }
    td:nth-of-type(2):before {
        content: "Dienstag";
    }
    td:nth-of-type(3):before {
        content: "Mittwoch";
    }
    td:nth-of-type(4):before {
        content: "Donnerstag";
    }
    td:nth-of-type(5):before {
        content: "Freitag";
    }
    td:nth-of-type(6):before {
        content: "Samstag";
    }
    td:nth-of-type(7):before {
        content: "Sonntag";
    }
    td:nth-of-type(8):before {
        content: "Feiertage";
    }
}

/* style daily opening hours */ 
@media only screen and (max-width: 960px) and (min-width: 770px) {
    .do20 {
        @include responsive-daily-opening-hours-table;
    }
}

/* below page-width */

@include media("<=page-width") {
    .ep330 .do20 {
        @include responsive-daily-opening-hours-table;
    }
    /* new responsive header */

    .header-top-inside {
        background: darken($primary, 10);
        padding: 1px 8px;
    }
    .tagline {
        color: $txt-on-primary;
        float: left;
        width: auto;
        margin: 2px 8px 0 0;
    }
    .mini-nav {
        span,
        a,
        a:hover {
            color: $txt-on-primary;
        }
    }

    // show mobile nav login
    .nav .mmn02 {
        &:hover a {
            background: none;
        }
        a {
            width: 100%;
        }
        a:hover {
            background: $primary-darker;
        }
    }
    .footer-mobile-login {
        display: block;
    }

    html.no-scroll {
        overflow: hidden;
    }
    
    @include mobile-nav;

    /* end new responsive header */
    .content-page img {
        max-width: 100%;
    }
    .content-page .cb-picture-item img {
        max-width: auto;
        width: 100%;
    }
    .brand {
        display: none;
    }

    h1 {
        font-size: 2.3em;
    }

    /* Detailseite */
    .ep1 {
        margin: 0;
        width: 100%;
    }
    .ep001 {
        display: none;
    }
    .entry-page {
        margin-top: 0;
    }

    /* Premium Block */
    .current-entries {
        @include justify-content(center);
    }
}

@media only screen and (max-width: $mobile-ui) {
    .main {
        @include rounded-remove;
        border: 0;
        padding: 0px;
    }
    .search-page {
        margin-top: 0;
        padding: 0 8px 16px 8px;
    }
    .search-main {
        display: flex;
        flex-direction: column;
        padding: 0;
    }
    .sp02 {
        display: none !important;
    }

    .sf8 {
        display: none !important;
    }
    .sf3,
    .sf5,
    .sf6,
    .group-event {
        display: none;
    }

    /* all filters mobile popup */
    .af0 {
        padding: 0;
        bottom: 65px;
    }
    .af7 {
        display: none;
    }

    /* layout switch */
    .ls1,
    .sp5 {
        margin-bottom: 0px;
    }

    /* search map */
    .map-open,
    .map-open.no-sticky {
        display: block;
        bottom: 52px;
        left: 0;
        right: 0;
        top: 60px;
        position: fixed;
        width: 100%;
        z-index: 20;
    }
    .map-act-as-filter {
        display: none !important;
    }

    /* search results */
    .sr0 {
        background: none;
        border: 0px;
    }
    .sr02 {
        display: none;
    }
    .srp1 {
        margin-bottom: 1em;
    }

    /* MISC */
    .modal-dialog {
        margin-top: 2.5%;
        max-width: 95%;
        width: auto;
    }

    /* Loading overlay: span whole viewport in mobile UI */
    .loading.fullscreen-in-mobile {
        position: fixed;
        top: 0 !important;
        left: 0 !important;
        bottom: 0 !important;
        right: 0 !important;
        height: 100% !important;
        width: 100% !important;
    }
}

@media only screen and (max-width: 860px) {
    .epc1,
    .epc2 {
        width: 100%;
    }

    .box-left-50 {
        float: none;
        width: 100%;
        margin-left: auto !important;
        margin-right: auto !important;
    }
}

/* below 768px */
@media only screen and (max-width: 768px) {
    h1 {
        font-size: 2em;
    }
    h2 {
        font-size: 1.4em;
    }

    // global
    .foot-nav {
        width: 50%;
    }

    .cb-picture-list-3 {
        @include picture-list(2, solid 1px $default-border-color, block, 1em, 8px);
    }
    .cb-picture-list-4 {
        @include picture-list(2, solid 1px $default-border-color, block, 1em, 8px);
        strong {
            font-size: 1em;
        }
    }
    .cb-picture-list-5 {
        @include picture-list(2, solid 1px $default-border-color, block, 1em, 8px);
        strong {
            font-size: 1em;
        }
    }
    .cb-people-list {
        @include people-list(2);
    }

    /* Startseite */
    .start-left,
    .start-right {
        @include bx(100%);
    }
    .mini-search {
        padding: 40px 12px;
    }

    /* Detailseite */

    .ep3 {
        width: 100%;
    }
    .ep30 {
        max-height: none;
        overflow: visible;
        margin-bottom: 2em;
    }

    .ep5,
    .ep6,
    .ep7,
    .ep8 {
        @include bx(50%);
    }
    .ep6 {
        clear: none;
    }
    .ep7 {
        clear: left;
    }
    .ep89 {
        max-width: 300px;
    }

    .em31 {
        height: 200px;
    }

    .ep33 {
        top: 2px;
    }

    .epi33 {
        margin: 0;
    }

    .do20 {
        @include responsive-daily-opening-hours-table;
    }

    // blog

    // blog teaser
    .blgt32 {
        font-size: 1.3em;
    }


    /* forms */
    .form-horizontal .form-actions {
        padding-left: 0;
    }
    .upuf400 .form-actions {
        padding-left: 208px;
    }
    .validation-success,
    .validation-error {
        .controls::before {
            left: calc(100% - 1.3em);
        } 
    }
    .validation-success .vi01 {
        top: 3px;
    }
    .controls-no-label {
        margin-left: 0;
    }

    .sp93,
    .sp94 {
        a {
            @include break-this;
        }
    }

   
}

@media only screen and (max-width: 639px) {
    .bx33,
    .bx66 {
        width: 100%;
    }
    .bw3 {
        @include remove-fluid;
        padding-right: 0;
        width: 100%;
    }
    .bw24 .tt-dropdown-menu {
        min-width: unset;
    }
    .bw4 {
        @include remove-fixed;
    }

    .bx75,
    .bx50,
    .bx25,
    .bx20 {
        @include border-box;
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }

    .e-map,
    .e-filter {
        border-right: none;
        padding-right: 0;
        padding-left: 0;
    }

    .cb-tag-list {
        @include tagcloud($primary, 0.9em);
    }
    .cb-picture-list-2,
    .cb-picture-list-3,
    .cb-picture-list-4 {
        @include picture-list(2, solid 1px $default-border-color, block, 0.8em, 8px);
        strong {
            font-size: 1em;
        }
    }
    .cb-people-list {
        @include people-list(2, 12px, 1px, 0.8em);
    }
}

@media only screen and (max-width: 570px) {
    /* forms */
    .form-horizontal .control-label {
        float: none;
    }
    .form-horizontal .controls {
        margin-left: 0;
        margin-top: 0.2em;
    }
    .vi01 {
        top: 35px;
    }
    .validation-success .vi01 {
        top: 30px;
    }
    /* user picture upload form */
    .upuf400 .form-actions {
        padding-left: 0px;
    }
    .upuf1:before {
        left: 50px;
    }
    .upuf1:after {
        left: 49px;
    }
}

/* below 480px */
@media only screen and (max-width: 480px) {
    // font sizes
    .welcome,
    .explained,
    .benefit {
        h1 {
            font-size: 1.6em;
        }
        h2 {
            font-size: 1.3em;
        }
    }
    h3 {
        font-size: 1.2em;
    }
    h4 {
        font-size: 1.1em;
    }
    h5,
    h6 {
        font-size: 1em;
    }

    /* global */

    .list31,
    .list32,
    .list33,
    .bx33 {
        width: 100%;
    }
    .list21,
    .list22 {
        width: 100%;
    }
    .foot-nav {
        width: 100%;
    }

    .cb-tag-list {
        @include tagcloud($primary, 0.9em);
    }
    .cb-picture-list-2,
    .cb-picture-list-3,
    .cb-picture-list-4 {
        @include picture-list(2, solid 1px $default-border-color, block, 0.8em, 8px);
        strong {
            font-size: 1em;
        }
    }
    .cb-people-list {
        @include people-list(2, 12px, 1px, 0.8em);
    }

    /* Startseite */
    .sp8 {
        @include break-this;
    }

    /* Detailseite */
    .detail-entry .main {
        padding-left: 0;
        padding-right: 0;
    }
    .ep100 {
        max-width: 100%;
        width: auto;
    }
    .ep21 {
        margin-left: 0;
    }
    .ep3,
    .ep4,
    .ep5,
    .ep6,
    .ep7,
    .ep8,
    .epl4,
    .epl3,
    .epc1,
    .epc2 {
        width: 100%;
        margin-bottom: 12px;
    }
    .epi31,
    .epi39 {
        width: 6%;
    }

    .ep71 img {
        width: 100%;
    }

    .ep952.embedded-media {
        width: 100%;
        margin: 0;
    }

    // user picture upload form
    .filepond--description {
        width: 100%;
    }
    .filepond--item {
        height: auto !important;
    }
    .filepond--file {
        height: 100px;
        margin-bottom: 0.5em;
    }
    .upuf02,
    .filepond--description {
        float: left;
        margin-left: 0px;
        width: 100%;
    }
    .upuf021,
    .upuf022 {
        display: inline;
    }
    .upuf023 {
        float: right;
    }

    // embed media iframes
    .em3 {
        width: 100%;
        margin: 0;
    }

    /* Suchseite */
    .ddbox {
        @include border-box;
        width: auto;
        min-width: auto;
    }
    .sf5 {
        margin-right: 5px;
    }
    .sp15 {
        font-size: 1.1em;
    }
    .tt-dropdown-menu {
        min-width: 295px;
    }
    .af-collapsible {
        .sf710 {
            border-bottom: 0px;
        }
    }
    .criteria-group.multiple-choice-inline {
        border-bottom-width: 1px;
    }

    // Fragen und Antworten
    .qaa {
        padding-left: 5px;
        padding-right: 5px;
    }
    .qaa21 {
        flex-direction: column;
    }
    .qaa211 {
        width: 100%;
        margin-right: 0;
    }
    .qaa32,
    .qaa342 {
        @include remove-callout-triangle;
    }
    .qaa33,
    .qaa343 {
        padding-left: 45px;
    }
    .qaa342 {
        @include break-this;
        margin-left: 24px;
        width: 93%;
    }
    .qaa344 {
        display: inline-block;
    }

    /* content pages */
    .box-left-20,
    .box-left-33 {
        float: none;
        width: 100%;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    .box-left-20 {
        max-width: 192px;
    }
}

/* below 380px */
@media only screen and (max-width: 380px) {
    .cb-tag-list {
        @include tagcloud($primary, 0.8em);
    }
    .cb-picture-list-2,
    .cb-picture-list-3 {
        @include picture-list(1, solid 1px $default-border-color, block, 1em, 8px);
    }
    .cb-picture-list-4 {
        @include picture-list(1, solid 1px $default-border-color, block, 0.75em, 8px);
        strong {
            font-size: 1em;
        }
    }
    .cb-people-list {
        @include people-list(1, 12px, 1px, 0.9em);
    }

    // compound list
    .cl1 {
        width: 100%;
        max-width: 350px;
    }
}

/* below 320px */
@media only screen and (max-width: 320px) {
    .tooltip,
    .modal,
    .mcbox {
        max-width: 312px;
    }
    // Detailseite
    // embed media
    .em31 {
        height: 169px;
    }
    .af5 .mc-column-3 {
        @include css-column(1, 12px);
    }
}

/* 2x */
@media only screen and (-webkit-min-device-pixel-ratio: 1.5),
    only screen and (-o-min-device-pixel-ratio: 3/2),
    only screen and (min-device-pixel-ratio: 1.5) {
    /* work in progress */
}

