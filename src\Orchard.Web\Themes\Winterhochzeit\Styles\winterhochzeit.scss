/* hochzeitslocations theme for discoverize.com portal - created 4.5.2015 - author: and<PERSON>j telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";

/* end import */

/* overwrites */

/* graphics */

body {
    background: url(img/pattern_holi.png) repeat 0 0; 
}

// .bg-full {min-height: 100%; min-width: 1900px; width: 100%; height: auto; position: fixed; top: 0; right: 0; z-index:-1;}

/* end graphics */

// kategorie icons

.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}

/* euro with icons */

/* Preisniveau Kategorie */
// €
.Preisniveau_1 {
    @include category-euros(1);
}
// €€
.Preisniveau_2 {
    @include category-euros(2);
}
// €€€
.Preisniveau_3 {
    @include category-euros(3);
}
// €€€€
.Preisniveau_4 {
    @include category-euros(4);
}

// Eintragsseite
.ep1 {
    .Preisniveau_1,
    .Preisniveau_2,
    .Preisniveau_3,
    .Preisniveau_4 {
        position: relative;
        top: 2px;
    }
}

.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}

// end kategorie icons

/* typo */

h1,
h2 {
    line-height: 1.2;
}
h3 {
    line-height: 1.3;
}

/* end typo */

/* layout */

/* global */

.content-page,
.zone-content {
    @extend .center;
    @include group;
    background: #fff;
    padding: 12px;
    margin-top: 3em;
    margin-bottom: 3em;
    min-height: 400px;
}

.zone-messages {
    @extend .center;
}

.additional-block {
    left: 50%;
    margin-left: 500px;
    top: -12px;
}

.hl-space-all {
    padding: 40px;
}
.hl-center {
    text-align: center;
}

.header {
    background: #fff;
}

html .header-top {
    @include group;
    background: $quaternary;
    width: 100%;
    a {
        color: $txt-on-quaternary;

        &:hover {
            color: $quaternary;
            background-color: $txt-on-quaternary;
        }
    }
}

.hti2 {
    margin-left: 5px;
}

.mini-nav {
    color: #fff;
    a,
    a:hover {
        color: darken($primary, 20);
        padding-top: 1px;
        display: inline-block;
    }
}

.logo {
    padding: 12px 0;
}

.tagline {
    text-transform: uppercase;
    color: #fff;
    padding: 0;
    width: auto;
    float: left;
    font-size: 0.9em;
    margin-right: 32px;
    a,
    a:hover {
        color: #fff;
        display: inline-block;
        padding-top: 3px;
    }
}

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 31px 12px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav {
//     a {
//         background: none;
//         color: $primary;
//         padding: 31px 12px;
//     }
//     a.current {
//         background: lighten($primary, 5);
//         color: #fff;
//     }
// }
// .nav-level-2 {
//     background: #fff;
//     border: none;
//     font-size: 1em;
//     a {
//         border: none;
//     }
// }

.premium-block {
    width: 100%;
    @extend %rounded-remove;
    background: #fff;
    padding-left: 0;
    padding-right: 0;
    border: none;
    margin: 3em 0 0 0;
}
.premium-block-inside {
    @extend .center;
}

.design-guide {
    border-bottom: solid 1px #ccc;
}

.cb-people-list-2 {
    @include people-list(2);
    margin: 0 0 2em 0;
}

footer {
    background: #242424;
}

.footer-top{
    padding: 24px;
    background: #ffffff2f;
}

.footer {
    width: 100%;
    padding-top: 0px;
}
.foot-header {
    @extend %shadow-top;
    width: 100%;
    background: $quaternary;
    text-transform: capitalize;
    position: relative;
    z-index: 1;
    font-size: 1.5em;
    a {
        color: $txt-on-quaternary;
    }
}
.foot-nav-wrapper {
    @extend .center;
    @include group;
    padding: 32px 0 48px 0;
}
.foot-nav {
    a {
        color: #9b9b9b;
    }
    a:hover {
        color: #242424;
    }
}
.foot-logo {
    display: inline-block;
    .mini-tagline {
        margin-right: -32px;
        text-align: center;
    }
}

.pbi2 {
    margin-bottom: 0;
}

/* homepage  */

body
.mini-search-entry-types-tabs{
    & > li > a{
        color: #404040;
    }
}

.home-page {
    display: flex;
    flex-direction: column;
    width: 100%;
    background: none;
    padding: 0;
    background: none;
    margin-top: 0;
    margin-bottom: 0;
    .additional-block {
        display: none;
    }
}

/* hack for safari 5-6 */
_::-moz-svg-foreign-content,
:root .home-page {
    display: block;
}

.mini-search {
    &:before {
        display: none;
    }
    height: 450px;
    @include background-responsive(
        "img/header.jpg",
        $image-height-mobile: 730,
        $image-height-desktop: 450
    );
    min-height: 450px;
}

.ms10 {
    color: $link-color;
}

.ms23 {
    color: $link-color;
} 

.welcome {
    @include flex-order(2);
    @include flex;
    padding: 0;
    margin-bottom: 0;
    width: 100%;
}
.welcome-inside {
    margin-bottom: -7px;
    margin-left:0; 
    margin-right:0;
    width: 100%;
}

@mixin tagcloud(
    $bg: $quaternary-darker,
    $size: 1em,
    $text: $txt-on-primary,
    $amount: 18,
    $direction: lighten,
    $difference: 2%
) {
    text-align: center;
    font-size: $size;
    a {
        @include primary-font;
        @include background-shades($bg, $amount, $direction, $difference);
        color: $text;
        padding: 3px 6px 2px 6px;
        display: inline-block;
        margin: 0 4px 8px 4px;
        text-align: center;
        font-size: 1em;
    }
    a:hover {
        background: darken($bg, 10);
        color: lighten($text, 20);
    }
    strong {
        font-size: 1.6em;
        position: relative;
        top: 2px;
        font-weight: normal;
        margin: 0 3px;
    }
    .cb-tag-amount {
        font-style: italic;
    }
}

.hl-tag-list {
    @extend .center;
    background: #fff;
    @include tagcloud;
    padding: 32px 0;
}

.hl-picture-gallery {
    @include group;
    position: relative;
    background: rgba(255, 255, 255, 0.9);
    .hl-pg11 img {
        @extend %shadow-none;
        @extend %rounded-remove;
        border: none;
        padding: 0;
        margin-bottom: 0;
        width: 100%;
        display: block;
    }
    .hl-pg14 {
        display: none;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.7);
        color: #fff;
        @extend .icon-map-marker;
        padding: 0.5 1em;
        line-height: 1.3;
    }
    .hl-pg14:before {
        float: left;
        font-size: 2em;
        margin: 0.1em 0 1em 0.5em;
    }
    strong,
    span {
        display: block;
    }
}

.hl-pg11 {
    width: (100%/4);
    position: relative;
    display: block;
    float: left;
    &:hover .hl-pg14 {
        display: block;
    }
}
@media only screen and (max-width: 480px) {
    .hl-pg11 {
        width: (100%/2);
        display: block;
    }
    .hl-pg11:nth-child(n + 5) {
        display: none;
    }
}
@media only screen and (min-width: 481px) and (max-width: 640px) {
    .hl-pg11 {
        width: (100%/3);
        display: block;
    }
    .hl-pg11:nth-child(n + 7) {
        display: none;
    }
}
@media only screen and (min-width: 641px) and (max-width: $page-width) {
    .hl-pg11 {
        width: (100%/4);
        display: block;
    }
    .hl-pg11:nth-child(n + 9) {
        display: none;
    }
}
@media only screen and (min-width: 961px) and (max-width: 1200px) {
    .hl-pg11 {
        width: (100%/5);
        display: block;
    }
    .hl-pg11:nth-child(n + 11) {
        display: none;
    }
}
@media only screen and (min-width: 1201px) and (max-width: 1440px) {
    .hl-pg11 {
        width: (100%/6);
        display: block;
    }
    .hl-pg11:nth-child(n + 13) {
        display: none;
    }
}
@media only screen and (min-width: 1441px) and (max-width: 1680px) {
    .hl-pg11 {
        width: (100%/7);
        display: block;
    }
    .hl-pg11:nth-child(n + 15) {
        display: none;
    }
}
@media only screen and (min-width: 1681px) {
    .hl-pg11 {
        width: (100%/8);
        display: block;
    }
    .hl-pg9 {
        display: none;
    }
}

.hl-pg9 {
    text-align: center;
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    bottom: -24px;
}
.hl-pg99 {
    @include link;
    @extend .icon-chevron-thin-down;
    display: inline-block;
    &:hover {
        background: none;
        color: darken($primary, 12);
    }
}
.hl-pg99.hl-pg-open {
    @extend .icon-chevron-thin-up;
}

.explained {
    @include flex;
    @include flex-order(3);
    @extend .center;
    width: $page-width;
    background: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    padding-bottom: 2em;
}
.explained-inside {
    @include group;
}
.hl-explained-1 {
    @extend .bx66;
    text-align: left;
    padding-top: 1em;
}
.hl-explained-2 {
    @extend .bx33;
    background: $quaternary;
    color: #fff;
    padding-top: 1em;
    img {
        display: inline-block;
    }
}
.hl-explained-button {
    @include rounded;
    background: #fff;
    width: 50%;
    color: $quaternary;
    padding: 12px 24px 9px 24px;
    margin-bottom: 2em;
    display: inline-block;
    &:hover {
        background: lighten($primary, 10);
    }
}

.benefit {
    @include flex;
    @include flex-order(4);
    @extend .center;
    @include group;
    width: $page-width;
    padding: 0;
    position: relative;
}
.benefit:after {
    background: url(img/verzierung.png) 50% 0 no-repeat;
    width: 100%;
    height: 40px;
    position: absolute;
    bottom: -24.5px;
    left: 0;
    content: "";
}
.benefit-standout {
    @extend %rounded-remove;
    @include group;
    border: none;
    padding: 0;
    background: transparentize($quaternary, 0.1);
    color: $txt-on-quaternary;
    position: relative;
    padding: 0 12px 1em 12px;
    a {
        &:hover {
            color: $quaternary;
        }
    }
}
.benefit-standout:before {
    background: url(img/verzierung.png) 50% 0 no-repeat;
    width: 100%;
    height: 40px;
    position: absolute;
    top: -14.5px;
    left: 0;
    content: "";
}

.hl-benefit-1 {
    @extend .bx50;
}
.hl-benefit-2 {
    @extend .bx50;
}

.mini-gallery {
    @include flex;
    @include flex-order(5);
    @extend .center;
    background: #fff;
}

.content-block-5 {
    @include flex;
    @include flex-order(6);
    @extend .center;
    background: #fff;
}
.cb51,
.cb52,
.cb53 {
    border: none;
    padding-left: 25px;
    padding-right: 25px;
    margin-bottom: 16px;
    h3 {
        padding-bottom: 0;
    }
}
.cb510,
.cb520,
.cb530 {
    text-align: center;
    img {
        display: inline-block;
    }
}

.home-page .blog-teaser-list {
    @include flex;
    @include flex-order(7);
    @extend .center;
    background: #fff;
    padding: 12px;
    margin-top: 3em;
    margin-bottom: 3em;
}

// .area {
//     @extend %rounded-remove;
//     @include border-box;
//     background: $quaternary-lighter;
//     width: 100%;
//     border: none;
//     padding-top: 1em;
// }
// .area-inside {
//     @extend .center;
//     a {
//         color: $txt-on-quaternary;
//     }
// }
// .area10 {
//     @extend .icon-map-marker;
// }
// .area10,
// .area12 {
//     color: $txt-on-quaternary;
// }
// .properties {
//     @extend %rounded-remove;
//     @include border-box;
//     background: $quaternary;
//     width: 100%;
//     margin-top: 0;
//     border: none;
//     padding-top: 1em;
// }
// .properties-inside {
//     @extend .center;
//     a {
//         color: $txt-on-quaternary;
//     }
// }
.properties .prop10 {
    @extend .icon-gittip;
}
.properties .prop11 a {
    @extend .icon-gittip;
}
// .prop10 {
//     color: $txt-on-quaternary;
// }

/* end homepage */

/* search page */
/* FULL WIDTH SEARCH PAGE */

.search-page {
    width: 100%;
    background: none;
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
    //   .additional-block {
    //     top: 3em;
    //   }
}
#search-main {
    @include media(">mobile-ui"){
        background: #fff;
        margin-top: 3em;
        margin-bottom: 3em;
    }
}


.sp0 {
    line-height: 1.6;
}

.search-content-block {
    @extend .center;
}

.sp8 {
    @extend %rounded-remove;
    background: rgba(255, 255, 255, 0.9);
    border: none;
}

.sp7 {
    @include border-box;
    background: $quaternary-lighter;
    width: 100%;
    border: none;
    padding-top: 1em;
    a {
        color: $txt-on-quaternary;
    }
}
.sp7-inside {
    @extend .center;
}
.sp71 {
    @extend .icon-map-marker;
    color: $txt-on-quaternary;
}
.sp91 {
    @include border-box;
    background: $quaternary;
    width: 100%;
    margin-top: 0;
    border: none;
    padding-top: 1em;
    a {
        @extend .icon-gittip;
        color: $txt-on-quaternary;
    }
}
.sp91-inside {
    @extend .center;
}
.sp92 {
    @extend .icon-gittip;
    color: $txt-on-quaternary;
}

.search-area-property-links,
.area,
.properties {
    a:hover {
        color: $quaternary;
        background: $txt-on-quaternary;
    }
}

.sf61-1, .epf61-1 { 
    @extend .icon-info-sign;
}
.sf61-2, .epf61-2 { 
    @extend .icon-magic;
}
.sf61-3, .epf61-3 { 
    @extend .icon-map-marker;
}
.sf61-4, .epf61-4 { 
    @extend .icon-child;
}
.sf61-5, .epf61-5 { 
    @extend .icon-home;
}
.sf61-6, .epf61-6 { 
    @extend .icon-fire;
}

@include hochzeit-map-marker;

.map-button-cluster-active {
    @include linear-gradient(lighten($primary, 12), lighten($primary, 2));
}

.map-marker-static {
    top: 50%;
    left: 50%;
    margin-left: -32px;
    margin-top: -50px;
}

.af8 {
    color: $primary;
    &:hover {
        color: darken($primary, 10);
    }
}

.sp110 {
    @include linear-gradient(
        transparentize(lighten($primary, 50), 0.9),
        transparentize(lighten($primary, 26), 0),
        0%,
        66%
    );
}

// Auszeichnung
/*.spea1 {
    position: absolute;
    right: 0;
    top: 0px;
}*/
.tile-view .spea1,
.search-results.search-results-full-width .spea1 {
    top: 0;
}
.spea12 {
    background: $primary;
    @include rounded(0, 0, 0, 8px);
    width: 40px;
    height: 40px;
    &:hover {
        background: darken($primary, 10);
    }
}
/*.spea13 {
    background: url(img/badge_icon.png) no-repeat -3px 1px;
    width: 40px;
    height: 40px;
    &:before {
        display: none;
    }
}*/
.spea2 {
    width: 400px;
}
/*.spea21:before {
    @include rounded(8px, 8px, 8px, 8px);
    content: "";
    background: $primary url(img/badge_icon.png);
    width: 40px;
    height: 40px;
}
*/
/* auszeichnung eintragsseite */
/*.epea1 {
    position: absolute;
    right: 5px;
    top: 5px;
}
.epea12 {
    background: $primary;
    @include rounded(0, 0, 0, 8px);
    width: 40px;
    height: 40px;
    &:hover {
        background: darken($primary, 10);
    }
}
.epea13 {
    background: url(img/badge_icon.png) no-repeat -3px 1px;
    width: 40px;
    height: 40px;
    &:before {
        display: none;
    }
}
.epea2 {
    width: 400px;
}
.epea21 {
    padding-top: 0;
}
.epea21:before {
    @include rounded(8px, 8px, 8px, 8px);
    content: "";
    background: $primary url(img/badge_icon.png);
    width: 40px;
    height: 40px;
}*/

// .epea1 {position:absolute; right:5px; bottom:5px;}
// .epea12 {@include rounded(8px, 0, 0, 0); width:60px; height:60px; background:rgba(255,255,255,0.7); display:block; text-align:center;
// 	&:hover {background:rgba(255,255,255,0.85);}
// 	}
// .epea13 {@extend .icon-auszeichnung-default; font-size:3em; color:$primary; display:inline-block; margin-top:-2px; margin-left:0.15em;}
// .epea2 {@extend .tooltip-large;}
// .epea21 {@extend .icon-auszeichnung-default;
// 	&:before {color:$primary; font-size:1.5em; position:relative; top:6px;}
// 	}
// .epea24 {@extend .icon-share;}

/* kategorie icons */

/* hotel kategorie */
// 1
.Klassifizierung_1 {
    @include category-stars(1);
}
// 2
.Klassifizierung_2 {
    @include category-stars(2);
}
// 3
.Klassifizierung_3 {
    @include category-stars(3);
}
// 4
.Klassifizierung_5 {
    @include category-stars(4);
}
// 5
.Klassifizierung_7 {
    @include category-stars(5);
}
// 3s
.Klassifizierung_4 {
    @include category-stars(3);
    &:after {
        content: "S";
    }
}
// 4s
.Klassifizierung_6 {
    @include category-stars(4);
    &:after {
        content: "S";
    }
}
// 5s
.Klassifizierung_8 {
    @include category-stars(5);
    &:after {
        content: "S";
    }
}
.Klassifizierung_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}

/* end kategorie icons */

.search-blogpost-links {
    @extend .center;
    background: rgba(255, 255, 255, 0.9);
    padding: 12px;
    margin-top: 3em;
    margin-bottom: 3em;
}
.blog-teaser-list {
    margin-top: 1em;
    margin-bottom: 1em;
}

/* end search page */

/* entry page */

.entry-page {
    width: 100%;
    background: none;
    padding: 0;
    margin-bottom: 0;
    .additional-block {
        top: -12px;
    }
    @include media("<=page-width") {
        margin-top: 0;
    }
}
.ep1 {
    @extend .center;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 3em;
}
.ep23 {
    border-bottom: 0;
}

// Anzeige Galerie Buttons
.epi31,
.epi39 {
    &:before {
        font-size: 3.2em;
        margin-top: -20px;
    }
    &:after {
        content: "";
        background: rgba(0, 0, 0, 0.75);
        width: 100px;
        height: 75px;
        position: absolute;
        top: 50%;
        margin-top: -35px;
        z-index: 10;
    }
}
.epi31 {
    &:before {
        left: 20px;
    }
    &:after {
        left: 1px;
    }
}
.epi39 {
    &:before {
        right: 20px;
    }
    &:after {
        right: 1px;
    }
}

.ep81 {
    @include button(#d9540c, #fff, rgba(0, 0, 0, 0.4), 1em);
    padding: 0.3em 0.35em 0.2em 0.35em;
    font-size: 1.2em;
    margin-right: 0;
}

.ep92 {
    background: $primary;
    color: $txt-on-primary;
    padding-left: 12px;
}
.ep96,
.ep98 {
    background: rgba(255, 255, 255, 0.9);
}

/* end entry page */

/* content page */

.box-left-20 {
    min-width: 160px;
}
.content-management-guide .box-left-20 {
    min-width: 0;
}

/* end content page */

/* end layout */

/* end overwrites */

/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: $page-width) {
    body {
        background: #fff;
    }
    .header-main-inside {
        background: #fff;
    }
    .welcome,
    .explained,
    .search-page,
    .ep1,
    .entry-page,
    .content-page,
    .benefit {
        padding-left: 0;
        padding-right: 0;
    }
    .hl-explained-1,
    .hl-explained-2 {
        width: 100%;
        margin-top: 1em;
    }
}
/* below 768 */
@media only screen and (max-width: 767px) {
    .mini-search-inside {
        width: 100%;
    }
    .mini-search {
        height: auto;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 0.8em;
    }
    .ms11 input.sb1 {
        height: 40px;
        font-size: 1.2em !important;
    }
    .ms25 {
        font-size: 1.2em;
    }

    .hl-benefit-1,
    .hl-benefit-2 {
        width: 100%;
    }

    .sp0 {
        font-size: 1.8em;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

// Facebook button in top nav
.fb-like {
    @include fb-buttons($width: 84px);
}
