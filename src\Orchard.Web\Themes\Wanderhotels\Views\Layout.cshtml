﻿@{
    ViewBag.HideContactInMiniNav = true;
    SetMeta("google-site-verification", "AnUMSAueKQG-FwUvAA-dS1dZx12GnbskAHxsGXfxfGo");
    if (string.IsNullOrWhiteSpace(Html.Title().ToString())) {
        Html.Title(Html.PortalName());
    }
    Style.Include("wanderhotels.css");
    Script.Require("mobile-menu").AtFoot();
    Script.Require("jQuery_Collapser").AtFoot();
    Script.Require("full-page-mini-search").AtFoot();
}
<header class="header">
    <div class="header-top">
        <div class="header-top-inside">
            <h2 class="tagline">@Html.Partial("_Tagline")</h2>
            <ul class="mini-nav">@Display.MiniNav()</ul>
        </div>
    </div>
    <div class="header-main jq-header-bar">
        <nav class="header-main-inside">
            <a href="/" class="logo">
                @Display._LogoResponsiveImage(Src: "/themes/wanderhotels/styles/img/wanderhotel-logo.svg")
            </a>
            @Html.Partial("_HeaderNavigation")
        </nav>
    </div>
</header>

@Html.Partial("_MainWrap")

@Html.Partial("_Footer")
