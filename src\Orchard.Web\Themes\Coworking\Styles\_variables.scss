/* variables for coworking - moved 23.11.2016 - by andrej telle - discoverize.com */
/* variables */

$page-width: 1161px;
$mobile-ui: $page-width;
$mini-search-theme-color: rgba(233, 233, 233, 0.6);

/* colors */
$primary: darken(#f8d069, 4);
$primary-darker: darken($primary, 5%);
$primary-lighter: lighten($primary, 5%);
$txt-on-primary: #444;
$txt-shadow-on-primary: rgba(255, 255, 255, 0.75);

$secondary: darken(#65b9d9, 4);
$secondary-darker: darken($secondary, 5%);
$secondary-lighter: lighten($secondary, 5%);
$txt-on-secondary: #fff;
$txt-shadow-on-secondary: rgba(0, 0, 0, 0.5);

$tertiary: #ff7f00;
$txt-on-tertiary: #fff;

$link-color: #333;
$sticky-nav-link-color: darken($primary, 20%);

$grey: #333333;

$nav-link-padding: 12px 12px;
$nav-font-size: 1em;

$nav-color: rgba(255,255,255,.9);
$nav-txt-color: $grey;

$flat-nav-bg-current: $primary;
$flat-nav-txt-current: $txt-on-primary !default;

$nav-level-2-txt-color: $grey;

$nav-color-current: $primary;

$flat-nav-bg: #fff;
$flat-nav-txt: $txt-on-primary;

$nav-collapser-color: $grey;


/* end colors */

// Pills
$pills-settings--primary-color: #333;
$pills-settings--secondary-color: #333;

/* typography */
/* typography */
$footer-bg: #303441;
$footer-links-color: #fff;

$footer-top-bg-color: $txt-on-primary;

$footer-custom-1-color: #fff;

$footer-custom-2-color: #fff;

$footer-headers-color: $primary;

@font-face { font-display: fallback; 
    font-family: "Museo";
    font-weight: 300;
    src: url("fonts/Museo300-Regular.eot");
    src: url("fonts/Museo300-Regular.woff2") format("woff2"),
        url("fonts/Museo300-Regular.woff") format("woff"),
        url("fonts/Museo300-Regular.ttf") format("truetype"),
        url("fonts/Museo300-Regular.eot?#iefix") format("embedded-opentype");
}

@font-face { font-display: fallback; 
    font-family: "Museo";
    font-weight: 500;
    src: url("fonts/Museo500-Regular.eot");
    src: url("fonts/Museo500-Regular.woff") format("woff"),
        url("fonts/Museo500-Regular.woff2") format("woff2"),
        url("fonts/Museo500-Regular.eot?#iefix") format("embedded-opentype"),
        url("fonts/Museo500-Regular.ttf") format("truetype");
}

@mixin primary-font {
    font-family: "Museo";
    font-weight: 300;
}
.button-primary,
.button-secondary,
.button-warning,
.button-neutral {
    font-weight: bold;
}

/* end typography */

/* end variables */
