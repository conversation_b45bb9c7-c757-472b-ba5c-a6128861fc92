/* tabs */
%tabs {
    display: flex;
    list-style: none;
    overflow: visible;
    padding: 0;
    margin-bottom: 0;
}
%tab {
    padding: 0;
}
%tab-link {
    @include primary-font;
    @extend %rounded-t;
    background: $neutral-lighter;
    border: solid 1px $neutral-less-darker;
    border-bottom: none;
    font-size: 1.2em;
    display: block;
    margin: 4px 10px 0 0;
    padding: 10px 8px 4px 8px;
}
%tab-link:hover,
%tab-link:focus {
    background: $primary;
    color: $txt-on-primary;
    text-decoration: none;
}
%tab-link:focus {
    outline: 0;
}
%tab.active {
    position: relative;
    top: 1px;
    z-index: 2;
}
.active %tab-link,
.active %tab-link:hover {
    border-top: solid 4px $primary;
    background: #fff;
    margin-top: 0;
    padding-top: 10px;
    padding-bottom: 5px;
    color: #555;
}

%tab--full-width {
    flex: 1 1 auto;
}
%tab-link--full-width {
    text-align: center;
}
%tab--full-width--last-link {
    margin-right: 0px;
}

.tabs {
    display: flex;
    list-style: none;
    overflow: visible;
    padding: 0;
    margin-bottom: 0;
    gap: 0 10px;

    & > li {
        padding: 0;
    }

    // favourites page
    & > li > a > h2 {
        font-size: 1.2em;
        padding: 0;
    }

    & > li > a, & > li > button {
        @if map-get($tabs-options, "rounded-corners") {
            @include rounded( map-get($tabs-options, "rounded-corners-value")... );
        }

        @include primary-font;
        background: $tabs-background;
        border: solid 1px $tabs-border;
        border-bottom: none;
        font-size: 1.2em;
        display: block;
        margin: 4px 0 0 0;
        padding: 10px 8px 4px 8px;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
        width: 100%;
        box-sizing: border-box;
    }

    & > li:first-child > a, & > li:first-child button {
        margin-left: 0px;
    }

    & > li.active {
        position: relative;
        top: 1px;
    }

    & > li.active > a, & > li.active > button,
    & > li.active:hover > a, & > li.active:hover > button {
        border-top: solid 4px $primary;
        background: #fff;
        margin-top: 0;
        padding-bottom: 5px;
        color: #555;
    }

    & > li > a:hover,
    & > li > a:focus,
    & > li > button:hover,
    & > li > button:focus {
        background: $primary;
        color: $txt-on-primary;
        text-decoration: none;
        & h2 {
            color: $txt-on-primary;
            background: $primary;
        }
    }

    & > li > a:focus, & > li > button:focus {
        outline: 0;
    }
}

.tabs--sm {
    & > li {
        font-size: 12px;
    }

    .entry-types-tabs__show-more-button {
        width: 39px;
    }
}

.tabs--lg {
    & > li {
        font-size: 18px;
        .entry-types-tabs__show-more-button {
            width: 51px;
        }
    }
}

.tabs--full-width {
    & > li {
        flex: 1 1 auto;
    }
    & > li > a, & > li > button {
        text-align: center;
    }
}

.tab-content {
    border: solid 1px darken($tabs-border, 5);
    background: #fff;
    clear: both;
    padding: 8px;
}

@mixin tabs($size: medium) {
    list-style: none;
    overflow: visible;
    @include group;
    padding: 0;
    margin-bottom: 0;

    @if ($size == large or $size == small) {
        @if ($size == small) {
            font-size: 12px;
        }
        @else {
            font-size: 20px;
        }
    }

    li {
        float: left;
        padding: 0;
    }

    a, button {
        @include primary-font;

        @if map-get($tabs-options, "rounded-corners") {
            @include rounded( map-get($tabs-options, "rounded-corners-value")... );
        }

        margin: 4px 10px 0 0;
        padding: 10px 8px 4px 8px;
        font-size: 1.2em;
        background: $neutral-lighter;
        border: solid 1px $neutral-less-darker;
        border-bottom: none;
        display: block;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
    }

    li.active {
        position: relative;
        top: 1px;
        z-index: 2;
    }

    li.active a,
    li.active:hover a,
    li.active button,
    li.active:hover button {
        border-top: solid 4px $primary;
        background: #fff;
        margin-top: 0;
        padding-bottom: 5px;
        color: #555;
    }

    a:hover,
    a:focus,
    button:hover,
    button:focus {
        background: $primary;
        color: $txt-on-primary;
        text-decoration: none;
    }

    a:focus, button:focus {
        outline: 0;
    }
}

/* vertical tabs with jquery ui classes */

.tabs-vertical {
    @include list-clean;
    @include group;
    float: left;
    max-width: 200px;
    margin-bottom: 1em;
}
.tabs-vertical a, .tabs-vertical button {
    padding: 8px;
    margin-left: 8px;
    background: #e5e5e5;
    border: solid 1px $neutral-darker;
    border-right: none;
    border-bottom: none;
    display: block;
}
.tabs-vertical li:last-child a, .tabs-vertical li:last-child button {
    border-bottom: solid 1px $neutral-darker;
}
.tabs-vertical a:hover, .tabs-vertical button:hover{
    color: $txt-on-primary;
    background: $primary;
}
.tabs-vertical .active a, .tabs-vertical .active button,
.tabs-vertical .active a:hover, .tabs-vertical .active button:hover {
    padding-left: 14px;
    right: -1px;
    position: relative;
    z-index: 5;
    margin-left: 0;
    background: #f4f4f4;
    @extend %rounded-l;
    border-bottom: solid 1px $neutral-darker;
    color: #333;
}

.tabs-vertical-content {
    float: left;
    border: solid 1px $neutral-darker;
    background: #f4f4f4;
    padding: 8px;
    display: none;
    min-height: 350px;
    margin-bottom: 1em;
}
.tabs-vertical-content.active {
    display: block;
}

/* tabbed content */
.cb-tabbed-tabs {
    @extend .tabs;
    a, button {
        @include media("<=480px") {
            margin-right: 0px;
            font-size: 1em;
            padding-top: 6px;
            padding-bottom: 3px;
        }
    }
    li.active a, li.active button,
    li.active:hover a, li.active:hover button {
        @include media("<=480px") {
            padding-top: 6px;
            padding-bottom: 4px;
        }
    }
}
.cb-tabbed-nav-full-width {
    .cb-tabbed-tabs {
        @extend .tabs--full-width;
    }
}
.cb-tabbed-pane {
    @extend .tab-content;
    padding: 1em;
}
