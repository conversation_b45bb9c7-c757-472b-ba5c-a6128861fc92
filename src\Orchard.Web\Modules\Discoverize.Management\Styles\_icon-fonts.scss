/* icon fonts - icomoon via icomoon */

@font-face { font-display: fallback; 
    font-family: "icomoon";
    src: url("icon-fonts/fonts/dz-manage.eot");
    src: url("icon-fonts/fonts/dz-manage.eot?#iefix")
            format("embedded-opentype"),
        url("icon-fonts/fonts/dz-manage.ttf") format("truetype"),
        url("icon-fonts/fonts/dz-manage.woff") format("woff"),
        url("icon-fonts/fonts/dz-manage.svg#icomoon") format("svg");
    font-weight: normal;
    font-style: normal;
}

@mixin font-icon {
    font-family: "icomoon";
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    margin-right: 0.3em;
    width: 1em;
    display: inline-block;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-hexagon-badge:before,
.icon-hexagon-badge:after {
    @include font-icon;
    content: "\e63f";
}
%icon-rounded-checkbox-after:after {
    @include font-icon;
    content: "\e910";
}

%icon-resize-vertical-after:after {
    @include font-icon;
    content: "\e63a";
}

// substitute from here
.icon-frown:before {
    @include font-icon;
    content: "\f035";
}
.icon-meh:before {
    @include font-icon;
    content: "\f036";
}
.icon-smile:before {
    @include font-icon;
    content: "\f037";
}
.icon-bars:before {
    @include font-icon;
    content: "\f034";
}
.icon-excel:before {
    @include font-icon;
    content: "\f02f";
}
.icon-powerpoint:before {
    @include font-icon;
    content: "\f030";
}
.icon-pdf:before {
    @include font-icon;
    content: "\f031";
}
.icon-blank-doc:before {
    @include font-icon;
    content: "\f032";
}
.icon-word-doc:before {
    @include font-icon;
    content: "\f033";
}
.icon-th-large:before {
    @include font-icon;
    content: "\f02e";
}
.icon-remove-thin:before,
%icon-remove-thin-after:after {
    @include font-icon;
    content: "\f02d";
}
.icon-light-checkmark:before,
%icon-light-checkmark-after:after {
    @include font-icon;
    content: "\f02c";
}
.icon-tag:before {
    @include font-icon;
    content: "\f02b";
}
.icon-heart-full:before {
    @include font-icon;
    content: "\f004";
}
.icon-eye:before {
    @include font-icon;
    content: "\f06e";
}
.icon-bar-chart:before {
    @include font-icon;
    content: "\f080";
}
.icon-bar-chart-o:before {
    @include font-icon;
    content: "\f080";
}
.icon-phone-square:before {
    @include font-icon;
    content: "\f098";
}
.icon-smile-o:before {
    @include font-icon;
    content: "\f118";
}
.icon-line-chart:before {
    @include font-icon;
    content: "\f201";
}
.icon-arrow-left:before {
    @include font-icon;
    content: "\f060";
}
.icon-star5:before {
    @include font-icon;
    content: "\e639";
}
.icon-arrow-right:before {
    @include font-icon;
    content: "\e640";
}
.icon-arrow-up:before {
    @include font-icon;
    content: "\f062";
}
.icon-arrow-down:before {
    @include font-icon;
    content: "\f063";
}
.icon-list-ol:before {
    @include font-icon;
    content: "\f0cb";
}
.icon-long-arrow-down:before {
    @include font-icon;
    content: "\f175";
}
.icon-long-arrow-up:before {
    @include font-icon;
    content: "\f176";
}
.icon-align-justify:before {
    @include font-icon;
    content: "\f039";
}
.icon-floppy-o:before {
    @include font-icon;
    content: "\f0c7";
}
.icon-hexagon:before {
    @include font-icon;
    content: "\e63f";
}
.icon-search:before {
    @include font-icon;
    content: "\e609";
}
.icon-envelope:before {
    @include font-icon;
    content: "\e601";
}
.icon-star:before {
    @include font-icon;
    content: "\e635";
}
.icon-star-empty:before {
    @include font-icon;
    content: "\e636";
}
.icon-user:before {
    @include font-icon;
    content: "\e600";
}
.icon-th-list:before {
    @include font-icon;
    content: "\e602";
}
.icon-ok:before {
    @include font-icon;
    content: "\e603";
}
.icon-remove:before,
%icon-remove-after:after {
    @include font-icon;
    content: "\e604";
}
.icon-off:before {
    @include font-icon;
    content: "\e605";
}
.icon-cog:before {
    @include font-icon;
    content: "\e606";
}
.icon-trash:before {
    @include font-icon;
    content: "\e607";
}
.icon-home:before {
    @include font-icon;
    content: "\e608";
}
.icon-file:before {
    @include font-icon;
    content: "\e622";
}
.icon-download-alt:before {
    @include font-icon;
    content: "\e60c";
}
.icon-download:before {
    @include font-icon;
    content: "\e626";
}
.icon-upload:before {
    @include font-icon;
    content: "\e625";
}
.icon-inbox:before {
    @include font-icon;
    content: "\e637";
}
.icon-lock:before {
    @include font-icon;
    content: "\e60b";
}
.icon-flag:before {
    @include font-icon;
    content: "\e60a";
}
.icon-tags:before {
    @include font-icon;
    content: "\e60d";
}
.icon-bookmark:before {
    @include font-icon;
    content: "\e60e";
}
.icon-camera:before {
    @include font-icon;
    content: "\e60f";
}
.icon-list:before {
    @include font-icon;
    content: "\e618";
}
.icon-pencil:before {
    @include font-icon;
    content: "\e610";
}
.icon-map-marker:before {
    @include font-icon;
    content: "\e611";
}
.icon-tint:before {
    @include font-icon;
    content: "\e612";
}
.icon-edit:before {
    @include font-icon;
    content: "\e613";
}
.icon-share:before {
    @include font-icon;
    content: "\e627";
}
.icon-chevron-thin-left:before {
    @include font-icon;
    content: "\e614";
}
.icon-chevron-thin-right:before {
    @include font-icon;
    content: "\e615";
}
.icon-plus-sign:before {
    @include font-icon;
    content: "\e61d";
}
.icon-minus-sign:before {
    @include font-icon;
    content: "\e61e";
}
.icon-remove-sign:before {
    @include font-icon;
    content: "\e61b";
}
%icon-ok-sign-after:after {
    @include font-icon;
    content: "\e602";
}
.icon-ok-sign:before {
    @include font-icon;
    content: "\e61f";
}
.icon-question-sign:before {
    @include font-icon;
    content: "\e621";
}
.icon-info-sign:before {
    @include font-icon;
    content: "\e620";
}
.icon-remove-circle:before {
    @include font-icon;
    content: "\e61c";
}
.icon-plus:before {
    @include font-icon;
    content: "\e62c";
}
.icon-minus:before {
    @include font-icon;
    content: "\e62d";
}
%icon-exclamation-sign-after:after {
    @include font-icon;
    content: "\e607";
}
.icon-exclamation-sign:before {
    @include font-icon;
    content: "\e62e";
}
.icon-comment:before {
    @include font-icon;
    content: "\e62a";
}
.icon-chevron-thin-up:before {
    @include font-icon;
    content: "\e616";
}
.icon-chevron-thin-down:before,
.icon-chevron-thin-down__after::after {
    @include font-icon;
    content: "\e617";
}
.icon-folder-close:before {
    @include font-icon;
    content: "\e631";
}
.icon-folder-open:before {
    @include font-icon;
    content: "\e632";
}
.icon-resize-vertical:before {
    @include font-icon;
    content: "\e63a";
}
.icon-upload-alt:before {
    @include font-icon;
    content: "\e630";
}
.icon-bullhorn:before {
    @include font-icon;
    content: "\e623";
}
.icon-globe:before {
    @include font-icon;
    content: "\e619";
}
.icon-wrench:before {
    @include font-icon;
    content: "\e61a";
}
.icon-fullscreen:before {
    @include font-icon;
    content: "\e62f";
}
.icon-caret-down:before {
    @include font-icon;
    content: "\e63b";
}
.icon-caret-up:before {
    @include font-icon;
    content: "\e63c";
}
.icon-envelope-alt:before {
    @include font-icon;
    content: "\e628";
}
.icon-undo:before {
    @include font-icon;
    content: "\e633";
}
.icon-lightbulb:before {
    @include font-icon;
    content: "\e634";
}
.icon-location-arrow:before {
    @include font-icon;
    content: "\e624";
}
.icon-file2:before {
    @include font-icon;
    content: "\e629";
}
.icon-stats-dots:before {
    @include font-icon;
    content: "\e900";
}
.icon-arrow-up-right2:before {
    @include font-icon;
    content: "\e901";
}
.icon-arrow-right2:before {
    @include font-icon;
    content: "\e902";
}
.icon-user2:before {
    @include font-icon;
    content: "\e638";
}
.icon-rocket:before {
    @include font-icon;
    content: "\e62b";
}
.icon-directions-ferry:before {
    @include font-icon;
    content: "\e63d";
}
.icon-hotel:before {
    @include font-icon;
    content: "\e63e";
}
