﻿@{
    ViewBag.HideContactInMiniNav = true;
    if (string.IsNullOrWhiteSpace(Html.Title().ToString())) {
        Html.Title(Html.PortalName());
    }
    Style.Include("superiorhotels.css");
    Script.Require("mobile-menu").AtFoot();
    Script.Require("jQuery_Collapser").AtFoot();
    ViewBag.HideFacebookLikeButton = true;
}

<header class="header">
    <div class="header-top">
        <div class="header-top-inside">
            <ul class="mini-nav">@Display.MiniNav()</ul>
        </div>
    </div>
    <div class="header-main jq-header-bar">
        <nav class="header-main-inside">
            <a href="/" class="logo">
                @Display._LogoResponsiveImage(Src: "/themes/superiorhotels/styles/img/superiorhotels-logo.svg")
                <h2 class="tagline">@Html.Partial("_Tagline")</h2>
            </a>
            @Html.Partial("_HeaderNavigation")
        </nav>
    </div>
</header>

@Html.Partial("_MainWrap")

@Html.Partial("_Footer")

<script>
    var images = [
        '@Url.Hashed("~/Themes/superiorhotels/Styles/img/hero-das-edelweiss.jpg")',
        '@Url.Hashed("~/Themes/superiorhotels/Styles/img/hero-jagdhof.jpg")',
    ];
    var x = Math.floor(images.length * Math.random());
    $('.home-page .mini-search').css('background-image', 'url("' + images[x] + '")');
</script>
