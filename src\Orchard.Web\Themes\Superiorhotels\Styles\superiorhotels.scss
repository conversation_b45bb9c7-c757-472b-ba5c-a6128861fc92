﻿/* superiorhotels theme for discoverize.com portal - created 20.1.2019 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "default-variables";
@import "variables";
@import "4_layouts/_thematica-global-template-style-variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
@import "4_layouts/_thematica-global-template-style";

/* end import */

/* overwrites */
/* graphics */

body {
    @include primary-font;
    background: #fff;
    position: relative; 
}
.button-primary {
    background-image: unset;
    background-color: $primary;
    height: auto;
    line-height: 1.7em;
    padding-bottom: 0;
    &:hover {
        background-color: $primary-darker;
    }
}
.button-warning,
.button-secondary {
    background-image: unset;
}
.button-warning {
    text-shadow: 0px 0px 0px $secondary;
}

.area10, .prop10, .area12 {
    color: #fff;
}

/* end graphics */

/* layout */
.home-page {
    background: #262626;
}
.header-top {
    background: #090909;
}
@include header-logo-above-nav($nav-color: #191919);
.logo {
    margin-bottom: 1em;
    margin-top: 1em;
    width: 214px;
}
.logo-graphic {
    width: 214px;
    @include media("<=mobile-ui") {
        height: 50px;
    }
}

.mini-nav {
    display: flex;
    @include justify-content(center);
    float: none;
    a,
    span {
        color: #ccc;
    }
}
.mn13 {
    display: none;
}
.header-main {
    // @include shadow;
    @include radial-gradient(
        $ellipse-position: 50% 50%,
        $start-color: #292929,
        $start-position: 0%,
        $end-color: #121212,
        $end-position: 100%
    );
}

.tagline {
    color: $primary-lighter;
    float: none;
    margin-right: 0px;
    height: 18px; 
    overflow: hidden;
}

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 13px 12px 8px 12px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav {
//     @include media(">page-width") {
//         display: flex;
//         @include justify-content(center);
//     }
//     a {
//         @include primary-font;
//         font-size: 1.2em;
//         background: transparent;
//         padding: 13px 12px 8px 12px;
//     }
//     a:hover {
//         background-color: $primary-lighter;
//     }
//     a.current {
//         background: transparent;
//         color: $primary-lighter;
//     }
// }
// .nav9 {
//     @include media(">page-width") {
//         margin-right: 12px;
//     }
// }
// .nav01 {
//     @extend .icon-home;
// }
.navm1,
.navm2,
.mhs0 .sb2 {
    border-radius: 0;
    background-image: none;
}

.global-search {
    @include global-search(
        260px,
        $text-color: #999,
        $background-color: #3b3b3b!important
    );
    padding: 6px 3px 6px 0;
    .sb1 {
        border-radius: 0px;
    }
    .sb2 {
        @extend %shadow-remove;
        background-color: #262626;
        background-image: none;
        border: 0px;
        border-radius: 0px;
        color: #999;
    }
}

.mini-search {
    margin-bottom: 80px;
    &:before {
        background: rgba(0, 0, 0, 0);
    }
    &:after {
        background: url(img/signee.png);
        content: "";
        width: 112px;
        height: 138px;
        position: absolute;
        bottom: -75px;
        left: calc(50% - 56px);
    }
}
.mini-search-inside {
    padding-bottom: 60px;
    @include media("ie-11", "<page-width") {
        padding-bottom: 60px;
    }
}
.ms10 {
    color: #fff;
    font-size: 50px;
    text-transform: uppercase;
    &:before {
        content: none;
    }
}
.ms23 {
    font-weight: 700;
    &:hover {
        background-color: $primary-darker;
    }
}

.ms261 {
    color: #fff;
    font-size: 20px;
}
.ms11 input.sb1 {
    border-radius: 0px;
}
.ms12 {
    > label,
    > span {
        color: #fff;
        @include text-background-offset(#666);
    }
}

/* search page */
#search-main {
    margin-bottom: 2em;
} 

.sfu-list {
    max-width: calc(100% - 110px);
}


.premium-booked {
    /*@include stripes-close(transparentize($primary, 0.1));*/
    .sp150 {
        color: $primary-darker;
    }
    .sp155 {
        color: #333;
    }
}

.sf61 {
    font-size: 0.9em;
}
.sf61-4, .epf61-4 { 
    @extend .icon-info-sign;
}
.sf61-6, .epf61-6 { 
    @extend .icon-cutlery;
}
.sf61-7, .epf61-7 { 
    @extend .icon-wellness_white;
}
.sf61-8, .epf61-8 { 
    @extend .icon-hotel;
}
.sf61-5, .epf61-5 { 
    @extend .icon-home;
}
.sf61-9, .epf61-9 { 
    @extend .icon-bicycle;
}
.sf61-10, .epf61-10 { 
    @extend .icon-map-marker;
}

.map-marker {
    @include map-marker(30px, #fff, $primary-lighter, $primary);
}
.map-marker-primary,
.map-marker-active,
.map-marker-static {
    @include map-marker(
        30px,
        $primary-darker,
        $txt-on-primary,
        darken($primary, 8)
    );
}
.map-marker-premium {
    @include map-marker(38px, $primary, $txt-on-primary, #fff, 2px);
    &.map-marker-active {
        @include map-marker(
            38px,
            darken($primary, 12),
            $txt-on-primary,
            darken($primary, 30),
            2px
        );
    }
}
.selected:not(.map-marker-non-hit) {
    background: #fff;
    color: $primary-lighter;
    border-color: darken($primary, 12);
}
.map-marker-stack.selected span {
    color: $primary;
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-shield;
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-shield;
        &:before {
            left: .4px;
            top: 5px;
            font-size: 22px;
        }
    }
}
.map-marker-premium {
    &.selected span {
        color: $primary;
    }
    span.map-marker-icon:before {
        left: 1px;
        top: 6px;
        font-size: 28px;
    }
}

.map-marker-static {
    left: 50%;
    margin-left: -15px;
    margin-top: -36px;
    top: 50%;
}

.mc320 {
    padding-top: 7px;
    padding-bottom: 3px;
}
.af-tabs .af2 a {
    text-transform: initial;
    font-size: 1.2em;
}

/* end search page */

/* entry page */

/* auszeichnung eintragsseite */
.epea1 {
    position: absolute;
    right: 8px;
    bottom: 8px;
}
.epea12,
.epea12:hover {
    width: 52px;
    height: 52px;
}
.epea13 {
    display: none;
}
.text.ep952 > span {
    display: block;
    margin-top: 0.5em;
}

.static-content, .content-page 
{
    p, li {
        text-transform: initial;
    }
}

/* end entry page */

/* kategorie icons */

/* hotel kategorie */
// 4s
.Klassifizierung_1 {
    @include category-stars(4);
    &:after {
        content: "S";
    }
}
// 5
.Klassifizierung_2 {
    @include category-stars(5);
}
// 5s
.Klassifizierung_3 {
    @include category-stars(5);
    &:after {
        content: "S";
    }
}
.Klassifizierung_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

/* preisniveau */

// €
.Preisniveau_1 {
    @include category-euros(1);
}
// €€
.Preisniveau_2 {
    @include category-euros(2);
}
// €€€
.Preisniveau_3 {
    @include category-euros(3);
}
// €€€€
.Preisniveau_4 {
    @include category-euros(4);
}
.Preisniveau_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}

// .sp110 {@include linear-gradient(transparentize(lighten($primary,50), .9), transparentize(lighten($primary,59), 0), 0%, 66%);}

/* end kategorie icons */

.design-guide {
    border-bottom: solid 1px #ccc;
}
.home-page {
    .mini-gallery,
    .welcome-inside,
    .explained-inside,
    .blog-teaser-list,
    .content-block-5-inside,
    .benefit-inside,
    .benefit-standout {
        background: #262626;
    }
    .blgt0 {
        color: $txt-on-primary;
    }
}
.benefit-standout {
    h2 {
        color: $primary-lighter;
    }
    p {
        color: $txt-on-secondary;
    }
}
.content-block-5 {
    h3 {
        color: $primary-lighter;
        font-size: 2em;
    }
    p {
        color: $txt-on-secondary;
    }
    img,
    img:hover {
        background: none;
        border: 0px;
    }
}
.mini-gallery12 {
    color: $primary-lighter;
}
.welcome-inside:after {
    background: url(img/divider.png) no-repeat;
    display: block;
    height: 69px;
    padding: 4em 0;
    background-position: 50% 50%;
}
.welcome {
    h1 {
        color: $primary-lighter;
    }
    h2 {
        color: #fff;
    }
}
.explained-inside:after {
    background: url(img/divider.png) no-repeat;
    display: block;
    height: 90px;
    background-position: 50% 100%;
}
.e-map,
.e-filter {
    border: 1px $primary solid;
    width: calc(50% - 45px);
    padding: 0 0 12px;
    h2,
    p {
        color: $primary;
    }
}

// .blgt0 {
//     font-size: 3em;
//     text-transform: uppercase;
//     @include media("<=480px") {
//         font-size: 2em;
//     }
// }
// .blgt1 {
//     background-color: #e1dcd1;
//     &:hover {
//         background: lighten(#e1dcd1, 10);
//     }
//     header {
//         font-size: 16px;
//     }
// }
// .blgt33 a,
// .blgt34 {
//     color: $primary-darker;
//     font-weight: 700;
// }
// .blgt85 {
//     color: $primary-lighter;
//     border-color: #6c6e70;
//     &:hover {
//         background-color: $primary;
//     }
// }

footer {
    background: $secondary; 
    padding-top: 0;
}

.footer-top {
    border-radius: 0 !important; 
    background: url(img/calltoaction-bg.jpg) no-repeat;
    background-size: cover;
    padding: 4.5em 1em 4em;
    position: relative;
    z-index: 1;
    h3,
    h4 {
        color: #fff;
        padding: 0;
        max-width: 800px;
        margin: 0 auto;
    }
    h3 {
        font-size: 3em;
        padding-top: 0;
        text-transform: uppercase;
        @include media("<=480px") {
            font-size: 2em;
        }
    }
    h4 {
        font-size: 2.2em;
        @include media("<=480px") {
            font-size: 1.2em;
        }
    }
}

.footer-top12 {
    @extend .button-secondary, .icon-arrow-right;
    @include linear-gradient($secondary, $secondary);
    font-size: 1.2em;
    max-width: 450px;
    width: 80%;
    @include media("<=480px") {
        font-size: 1em;
    }
}

.footer-logo {
    padding: 30px 0 40px;
}

.blg4 p {
    @include secondary-font;
}

/* end layout */
@media screen and (min-width: $page-width) {
    .sticky-header {
        .header-main-inside {
            width: $page-width;
            margin: 0 auto;
        }
    }
}

/* end overwrites */

/* responsive design */
@import "respond";
/* below page-width */
@media only screen and (max-width: $page-width) {
    .premium-block {
        background: #fff;
    }
    .main-wrap {
        background-image: none;
    }
}
/* below 768 */
@media only screen and (max-width: 767px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
    .do20 td {
        padding-left: 130px;
        &:before {
            width: 106px;
        }
    }
}
/* below 650 */
@media only screen and (max-width: 650px) {
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.1em;
        padding: 0;
    }
    .mini-search legend {
        margin-bottom: 0;
    }
}
/* below 320 */
@media only screen and (max-width: 319px) {
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";
.mini-search-inside {
    @include media("ie-11", "<page-width") {
        padding-bottom: 60px;
    }
}

// Cookies
.cc_dialog {
    .cc_dialog_text {
        padding-bottom: 10px;
    }
}

// Cookies
.cc_dialog {
    @include cookies(
        $type-of-graphic: logo,
        $graphic: 'img/logo.png'
    );
}