/* variables for wanderhotels - 1.4.2021 - by andrej telle - discoverize.com */

/* variables */
$page-width: 1161px;
$mobile-ui: $page-width;
$mini-search-theme: "white";
$single-line-mini-search: true;
$mini-search-full-height: true;

$button-options: (
    "border": false,
    "rounded-corners": true,
    "rounded-corners-value": 5px 5px 5px 5px,
    "gradient-background": false, 
    "text-shadow": false,
    "shadow": false,
);

/* colors */
$primary: #50a3d0;
$primary-darker: darken($primary, 5%);
$primary-darkest: darken($primary, 15%);
$primary-lighter: lighten($primary, 15%);
$txt-on-primary: #fff;
$txt-shadow-on-primary: rgba(0, 0, 0, 0.5);

$medium: #999;
$medium-lighter: #bababa;
$medium-lightest: #e1e1e1;

$secondary: #004a80;
$secondary-darker: darken($secondary, 5%);
$secondary-lighter: lighten($secondary, 5%);
$txt-on-secondary: #fff;
$txt-shadow-on-secondary: rgba(000, 000, 000, 0.5);
$secondary-transparent: rgba(84, 115, 141, 0.8);

$tertiary: #f2efe9;
$txt-on-tertiary: #555;

$quaternary: #5DE6AE;
$txt-on-quaternary: #fff;

$premium-color-search-result-bg: darken($primary,12);

// Used for text
$dark: #1d1d1b;
$grey: #9d9c9c;
$grey-warm: #edede4;
$txt-on-grey: #fff;

$link-color: darken($primary, 0);

$nav-color: $grey;
$nav-txt-color: $txt-on-grey;

$nav-link-padding: 20px 12px ;
$nav-font-size: 1em ;
$nav-text-transform: uppercase;

$flat-nav-bg: #fff;
$flat-nav-txt: $primary;

$flat-nav-bg-current: $primary;
$flat-nav-txt-current: $txt-on-primary ;


/* end colors */

/* Header parts heights */
$header-main-h: 66px;
$header-sub-h: 24px;

/* Screens */
$screen-xs: 767px;
$screen-sm: 991px;

/* typography */
$footer-bg: $secondary;
$footer-links-color: $txt-on-secondary;
$footer-headers-color: $primary;

$footer-top-bg-color: $txt-on-primary;

$footer-custom-1-color: $txt-on-secondary;

$footer-custom-2-color: $txt-on-secondary;

/* Import Google Web Fonts*/
/* montserrat-regular - latin */
@font-face { font-display: fallback; 
    font-family: "Montserrat";
    font-style: normal;
    font-weight: 400;
    src: url("./fonts/montserrat-v15-latin-regular.eot"); /* IE9 Compat Modes */
    src: local("Montserrat Regular"), local("Montserrat-Regular"),
        url("./fonts/montserrat-v15-latin-regular.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/montserrat-v15-latin-regular.woff2")
            format("woff2"),
        /* Super Modern Browsers */
            url("./fonts/montserrat-v15-latin-regular.woff") format("woff"),
        /* Modern Browsers */ url("./fonts/montserrat-v15-latin-regular.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/montserrat-v15-latin-regular.svg#Montserrat")
            format("svg"); /* Legacy iOS */
}
/* patrick-hand-sc-regular - latin */
@font-face {
    font-family: 'Patrick Hand SC';
    font-style: normal;
    font-weight: 400;
    src: url('./fonts/patrick-hand-sc-v8-latin-regular.eot'); /* IE9 Compat Modes */
    src: local(''),
         url('./fonts/patrick-hand-sc-v8-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('./fonts/patrick-hand-sc-v8-latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
         url('./fonts/patrick-hand-sc-v8-latin-regular.woff') format('woff'), /* Modern Browsers */
         url('./fonts/patrick-hand-sc-v8-latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
         url('./fonts/patrick-hand-sc-v8-latin-regular.svg#PatrickHandSC') format('svg'); /* Legacy iOS */
  }
@mixin secondary-font {
    font-family: "Patrick Hand SC", sans-serif;
    font-weight: 400;
    font-size: 2em;
}
@mixin primary-font {
    font-family: "Montserrat", sans-serif;
    font-weight: 400;
}

/* end variables */
