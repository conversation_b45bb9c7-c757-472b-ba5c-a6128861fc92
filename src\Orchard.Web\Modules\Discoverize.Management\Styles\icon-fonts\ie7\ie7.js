/* To avoid CSS expressions while still supporting IE 7 and IE 6, use this script */
/* The script tag referencing this file must be placed before the ending body tag. */

/* Use conditional comments in order to target IE 7 and older:
	<!--[if lt IE 8]><!-->
	<script src="ie7/ie7.js"></script>
	<!--<![endif]-->
*/

(function() {
	function addIcon(el, entity) {
		var html = el.innerHTML;
		el.innerHTML = '<span style="font-family: \'dz-manage\'">' + entity + '</span>' + html;
	}
	var icons = {
		'icon-tag': '&#xf02b;',
		'icon-frown': '&#xf035;',
		'icon-meh': '&#xf036;',
		'icon-smile': '&#xf037;',
		'icon-bars': '&#xf034;',
		'icon-excel': '&#xf02f;',
		'icon-powerpoint': '&#xf030;',
		'icon-pdf': '&#xf031;',
		'icon-blank-doc': '&#xf032;',
		'icon-word-doc': '&#xf033;',
		'icon-remove-thin': '&#xf02d;',
		'icon-light-checkmark': '&#xf02c;',
		'icon-heart': '&#xf004;',
		'icon-eye': '&#xf06e;',
		'icon-bar-chart': '&#xf080;',
		'icon-bar-chart-o': '&#xf080;',
		'icon-phone-square': '&#xf098;',
		'icon-smile-o': '&#xf118;',
		'icon-line-chart': '&#xf201;',
		'icon-arrow-left': '&#xf060;',
		'icon-star5': '&#xe639;',
		'icon-arrow-right': '&#xe640;',
		'icon-arrow-up': '&#xf062;',
		'icon-arrow-down': '&#xf063;',
		'icon-list-ol': '&#xf0cb;',
		'icon-long-arrow-down': '&#xf175;',
		'icon-long-arrow-up': '&#xf176;',
		'icon-align-justify': '&#xf039;',
		'icon-floppy-o': '&#xf0c7;',
		'icon-hexagon': '&#xe63f;',
		'icon-search': '&#xe609;',
		'icon-envelope': '&#xe601;',
		'icon-star': '&#xe635;',
		'icon-star-empty': '&#xe636;',
		'icon-user': '&#xe600;',
		'icon-th-list': '&#xe602;',
		'icon-ok': '&#xe603;',
		'icon-remove': '&#xe604;',
		'icon-off': '&#xe605;',
		'icon-cog': '&#xe606;',
		'icon-trash': '&#xe607;',
		'icon-home': '&#xe608;',
		'icon-file': '&#xe622;',
		'icon-download-alt': '&#xe60c;',
		'icon-download': '&#xe626;',
		'icon-upload': '&#xe625;',
		'icon-inbox': '&#xe637;',
		'icon-lock': '&#xe60b;',
		'icon-flag': '&#xe60a;',
		'icon-tags': '&#xe60d;',
		'icon-bookmark': '&#xe60e;',
		'icon-camera': '&#xe60f;',
		'icon-list': '&#xe618;',
		'icon-pencil': '&#xe610;',
		'icon-map-marker': '&#xe611;',
		'icon-tint': '&#xe612;',
		'icon-edit': '&#xe613;',
		'icon-share': '&#xe627;',
		'icon-chevron-thin-left': '&#xe614;',
		'icon-chevron-thin-right': '&#xe615;',
		'icon-plus-sign': '&#xe61d;',
		'icon-minus-sign': '&#xe61e;',
		'icon-remove-sign': '&#xe61b;',
		'icon-ok-sign': '&#xe61f;',
		'icon-question-sign': '&#xe621;',
		'icon-info-sign': '&#xe620;',
		'icon-remove-circle': '&#xe61c;',
		'icon-plus': '&#xe62c;',
		'icon-minus': '&#xe62d;',
		'icon-exclamation-sign': '&#xe62e;',
		'icon-comment': '&#xe62a;',
		'icon-chevron-thin-up': '&#xe616;',
		'icon-chevron-thin-down': '&#xe617;',
		'icon-folder-close': '&#xe631;',
		'icon-folder-open': '&#xe632;',
		'icon-resize-vertical': '&#xe63a;',
		'icon-upload-alt': '&#xe630;',
		'icon-bullhorn': '&#xe623;',
		'icon-globe': '&#xe619;',
		'icon-wrench': '&#xe61a;',
		'icon-fullscreen': '&#xe62f;',
		'icon-caret-down': '&#xe63b;',
		'icon-caret-up': '&#xe63c;',
		'icon-envelope-alt': '&#xe628;',
		'icon-undo': '&#xe633;',
		'icon-lightbulb': '&#xe634;',
		'icon-location-arrow': '&#xe624;',
		'icon-file2': '&#xe629;',
		'icon-th-large': '&#xf02e;',
		'icon-stats-dots': '&#xe900;',
		'icon-arrow-up-right2': '&#xe901;',
		'icon-arrow-right2': '&#xe902;',
		'icon-user2': '&#xe638;',
		'icon-rocket': '&#xe62b;',
		'icon-directions-ferry': '&#xe63d;',
		'icon-hotel': '&#xe63e;',
		'0': 0
		},
		els = document.getElementsByTagName('*'),
		i, c, el;
	for (i = 0; ; i += 1) {
		el = els[i];
		if(!el) {
			break;
		}
		c = el.className;
		c = c.match(/icon-[^\s'"]+/);
		if (c && icons[c[0]]) {
			addIcon(el, icons[c[0]]);
		}
	}
}());
