/* global colors for all discoverize themes - created by andrej telle 11.2.2015 */

// override dark mode theme settings on devices
:root {
    color-scheme: light only;
}

$body-bg: #fff !default;

$primary: #0088cc !default;
$primary-darker: darken($primary, 5%) !default;
$primary-lighter: lighten($primary, 5%) !default;
$txt-on-primary: #fff !default;
$txt-shadow-on-primary: none !default;

$secondary: #0bd9ae !default;
$secondary-darker: darken($secondary, 5%) !default;
$secondary-lighter: lighten($secondary, 5%) !default;
$txt-on-secondary: #fff !default;
$txt-shadow-on-secondary: none !default;

$tertiary: #cb157d !default;
$tertiary-darker: darken($tertiary, 5%) !default;
$tertiary-lighter: lighten($tertiary, 5%) !default;
$txt-on-tertiary: #fff !default;
$txt-shadow-on-tertiary: none !default;

$quaternary: #f1b144 !default;
$txt-on-quaternary: #333 !default;

$quinary: #666 !default;
$txt-on-quinary: #fff !default;

$senary: #65308f !default;
$txt-on-senary: #fff !default;
    
$quaternary: #65308f !default;
$txt-on-quaternary: #fff !default;

$neutral: #dedbd7 !default;
$neutral-lighter: lighten($neutral, 3) !default;
$neutral-light-2: lighten($neutral, 6) !default;
$neutral-light: lighten($neutral, 9) !default;
$neutral-background: lighten($neutral, 12) !default;
 
$neutral-less-darker: darken($neutral, 5);
$neutral-darker: darken($neutral, 10);
$neutral-dark: darken($neutral, 20);
$neutral-darkest: darken($neutral, 30);
$neutral-white: #fefefe;
$neutral-on-neutral-light: #fcfcfc; 
$neutral-bg: #f5f5f2; 

$txt-on-neutral: #333; 
$txt-shadow-on-neutral: rgba(255, 255, 255, 1);

$linen: #F7EFE0;
$txt-on-linen: #555;

$txt-bg: #fff;
$txt-color: #555;
$txt-color-light: #828282;
$txt-color-lighter: #aaa;
$txt-light: #fff;



$positive: #23b128;
$negative: #f6b83f;
$txt-on-negative: darken($negative, 42);
$txt-shadow-on-negative: rgba(255, 255, 255, 0.6);
$error: #ca3838;

$ampel-green: #5F9617;
$ampel-yellow: #f8a825;
$ampel-red: #eb4913; 

// $pills-settings--primary: (
//     "color": $primary,
//     "background-color": $neutral-light,
//     "border": 1px solid #c1c1c1,
//     "border-radius": 24px,
// );

// $pills-settings--secondary: (
//     "color": $primary,
//     "background-color": $neutral-light, 
//     "border": 1px solid #c1c1c1,
//     "border-radius": 12px,
// );

$pills-settings--primary-font-size: inherit !default;
$pills-settings--secondary-font-size: inherit !default;
$action-panel-button-start-color: $primary ;
$action-panel-button-end-color: $primary;



$green: #5bb75b;
$blue: #49afcd;
$yellow: #f5e000;
$orange: #f5a100;
$red: #e74c3c;

$bg-content-blocks: #fff;

$bg-blog-slider-article: #f9f9f9;

$link-color: $primary; 
$txt-on-link: $txt-on-primary;
$link-color-in-content: $link-color;
$circle-counter-bg: $red;
$circle-counter-color: #fff;

$bg-grey-transparent: rgba(0, 0, 0, 1);

$crumbtrail-bg: $neutral-light;
$crumbtrail-color: $link-color;

$cmg-neutral: #fff;
$cmg-black: #404040;
$cmg-grey-darker: #d4d4d4;
$cmg-grey-lighter: #f9f9f9;
$cmg-grey: #e6e6e6;

$transparent: rgba(0, 0, 0, 0);
$transparent-bg-light: rgba(255, 255, 255, 0.8);
$transparent-bg-dark: rgba(0, 0, 0, 0.5);

$img-bg-color: #fff;
$rating-stars-bg: #ddd;

$nav-search-auto-width: true !default;

$camping-neutral: #999;

$bg-search-filters: #FBF7F4;
$bg-search-results: $neutral-light;
$bg-search-results-mobile: $neutral-light;

$border-criteria-group: $neutral;

$border-sp-results: $neutral;

$bg-wrapper: $neutral-background;
$border-wrapper: $neutral;
$wrapper-padding: 12px;



// buttons
$border-button-light: $neutral;

// entry page header section

$ep-rating-box-bg: $neutral-background;
$ep-rating-box-border: $neutral;

// q and q
$qaa-border: $neutral;
$qaa-bg: $neutral-on-neutral-light;

// more links
$ep-more-links-bg: #fff;
$ep-more-links-border: $neutral;



// should be used for mini-search bg and tabs bg in hero
$mini-search-bg: rgba(255,255,255,0.5) !default; 
$mini-search-theme-color: rgba(255, 255, 255, 0.7) !default;
$mini-search-theme-txt-color: rgb(27, 27, 27) !default;
$mini-search-unactive-tab-alpha-reduction: .2 !default;
$mini-search-full-height: false;
$mini-search-headline-color: #fff;
$single-line-mini-search: false;

/* default variables for all discoverize themes - created 1.1.2018 by andrej telle */

$global-border-radius: 8px;

$fibonacci-ratio: 1.618;

$page-width: 1161px;
$additional-block-width: 160px;
$mobile-ui: 1161px;

$content-padding: 12px;
$content-margin-top: 6px;

$interactive-elements-rounded-corners: 24px !default;

$non-buttons-rounded-corners: 5px;
$rounded-corner-small: 8px;
$rounded-corner-large: 24px;
$rounded-corner-large-2: 5em;
$rounded-corner-large-3: 100px;
$rounded-corner-xl: 200px;
$default-border-color: #ddd;

$rounded-corners-text-inputs: 8px;

$fav-rounded-corners: 8px;

// remove todo
$sp-sidebar-adblock-width: 162px;

// todo remove
$search-results-width: 359px;
 
$search-result-padding-left: 8px; 
$search-result-padding-right: 8px;
$search-result-padding-top: 8px;
$search-result-padding-bottom: 14px;

$entry-page-mobile-padding-horizontal: 8px;

// top filters border
$search-top-filters-border-color: #c1c1c1;

$form-fields-rounded-corners: true;

$button-options: (
    "border": true,
    "rounded-corners": true,
    "rounded-corners-value": 0.3em 0.3em 0.3em 0.3em,
    "gradient-background": true,
    "text-shadow": true,
    "shadow": true,
);

$tabs-options: (
    "rounded-corners": map-get($button-options, "rounded-corners"),
    "rounded-corners-value": map-get($button-options, "rounded-corners-value")
);

$bg-area: #f0f0f0;
$bg-nearby: #ebebeb;
$bg-properties: #e9e9e9;
$bg-pre-footer: darken($bg-properties, 3);



$search-page--full-width: false;

$full-width-header: false;

$grey: #333333;
$txt-on-grey: #fff;
$grey-lighter: lighten($grey, 40%);
$grey-middle: lighten($grey, 20%);
$grey-darker: darken($grey, 10%);

/* cards */
$card-shadow: none;
$card-border: solid 1px $default-border-color;
$card-rounded-corner: $non-buttons-rounded-corners;
$card-bg: #f9f9f9;
$card-content-padding: 12px;
$card-text-color: $txt-color;

$text-font-size: 1.05em !default;

@mixin primary-font {
    font-family: "open sans";
    font-weight: 400;
}
@mixin secondary-font {
    font-family: "muli";
    font-weight: 400;
}

$flat-nav-bg: #fff;
$flat-nav-txt: #333;


// SP Layout vars

// when adding breakpoints, make sure to check in loops below, where desktop starts, mobile ends
$search-results-layout-settings: (
    // mobile
    // sub map
    "bp1": (
        // key : value
        "start-breakpoint": 0,  
        "end-breakpoint": 320, 
        "img-size": 320, 
        "search-results-per-line": 1,
        "search-results-per-line-with-ad-slot": 1,
        "search-results-per-line-with-map": 1,
        "search-results-per-line-with-map-and-ad-slot": 1
    ),
    "bp2": (
        "start-breakpoint": 321,  
        "end-breakpoint": 360, 
        "img-size": 360, 
        "search-results-per-line": 1,
        "search-results-per-line-with-ad-slot": 1,
        "search-results-per-line-with-map": 1,
        "search-results-per-line-with-map-and-ad-slot": 1
    ),
    "bp3": (
        "start-breakpoint": 361,  
        "end-breakpoint": 375, 
        "img-size": 375, 
        "search-results-per-line": 1,
        "search-results-per-line-with-ad-slot": 1,
        "search-results-per-line-with-map": 1,
        "search-results-per-line-with-map-and-ad-slot": 1
    ),
    "bp4": (
        "start-breakpoint": 376,  
        "end-breakpoint": 550, 
        "img-size": 414, 
        "search-results-per-line": 1,
        "search-results-per-line-with-ad-slot": 1,
        "search-results-per-line-with-map": 1,
        "search-results-per-line-with-map-and-ad-slot": 1
    ),
    "bp5": (
        "start-breakpoint": 551,  
        "end-breakpoint": 605, 
        "img-size": 320, 
        "search-results-per-line": 2,
        "search-results-per-line-with-ad-slot": 2,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp6": (
        "start-breakpoint": 606,  
        "end-breakpoint": 768, 
        "img-size": 360, 
        "search-results-per-line": 2,
        "search-results-per-line-with-ad-slot": 2,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp7": (
        "start-breakpoint": 606,  
        "end-breakpoint": 768, 
        "img-size": 360, 
        "search-results-per-line": 2,
        "search-results-per-line-with-ad-slot": 2,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp8": (
        "start-breakpoint": 769,  
        "end-breakpoint": 800, 
        "img-size": 375, 
        "search-results-per-line": 2,
        "search-results-per-line-with-ad-slot": 2,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp9": (
        "start-breakpoint": 801,  
        "end-breakpoint": 834, 
        "img-size": 414, 
        "search-results-per-line": 2,
        "search-results-per-line-with-ad-slot": 2,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp10": (
        "start-breakpoint":835,  
        "end-breakpoint": 1024, 
        "img-size": 320, 
        "search-results-per-line": 3,
        "search-results-per-line-with-ad-slot": 3,
        "search-results-per-line-with-map": 3,
        "search-results-per-line-with-map-and-ad-slot": 3
    ),
    "bp11": (
        "start-breakpoint": 1025,  
        "end-breakpoint": 1161,   
        "img-size": 375, 
        "search-results-per-line": 3,
        "search-results-per-line-with-ad-slot": 3,
        "search-results-per-line-with-map": 3,
        "search-results-per-line-with-map-and-ad-slot": 3
    ),
    // desktop
    "bp12": (
        "start-breakpoint": 1162,  
        "end-breakpoint": 1280, 
        "img-size": 320, 
        "search-results-per-line": 4,
        "search-results-per-line-with-ad-slot": 3,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 1
    ),
    "bp13": (
        "start-breakpoint": 1281,  
        "end-breakpoint": 1366, 
        "img-size": 360, 
        "search-results-per-line": 4,
        "search-results-per-line-with-ad-slot": 3,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp14": (
        "start-breakpoint": 1367,  
        "end-breakpoint": 1440, 
        "img-size": 360, 
        "search-results-per-line": 4,
        "search-results-per-line-with-ad-slot": 4,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp15": (
        "start-breakpoint": 1441,  
        "end-breakpoint": 1536, 
        "img-size": 360, 
        "search-results-per-line": 4,
        "search-results-per-line-with-ad-slot": 4,
        "search-results-per-line-with-map": 2,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp16": (
        "start-breakpoint": 1537,  
        "end-breakpoint": 1600, 
        "img-size": 360, 
        "search-results-per-line": 4,
        "search-results-per-line-with-ad-slot": 4,
        "search-results-per-line-with-map": 3,
        "search-results-per-line-with-map-and-ad-slot": 2
    ),
    "bp17": (
        "start-breakpoint": 1601,  
        "end-breakpoint": 1800, 
        "img-size": 360, 
        "search-results-per-line": 4,
        "search-results-per-line-with-ad-slot": 4,
        "search-results-per-line-with-map": 3,
        "search-results-per-line-with-map-and-ad-slot": 3 
    ),
    "bp18": (
        "start-breakpoint": 1801,  
        "end-breakpoint": 1920, 
        "img-size": 375, 
        "search-results-per-line": 5,
        "search-results-per-line-with-ad-slot": 4,
        "search-results-per-line-with-map": 3,
        "search-results-per-line-with-map-and-ad-slot": 3
    ),
    "bp19": (
        "start-breakpoint": 1921,  
        "end-breakpoint": 2048, 
        "img-size": 375, 
        "search-results-per-line": 5,
        "search-results-per-line-with-ad-slot": 5,
        "search-results-per-line-with-map": 4,
        "search-results-per-line-with-map-and-ad-slot": 3
    ),
    "bp20": (
        "start-breakpoint": 2049,  
        "end-breakpoint": 2560, 
        "img-size": 414, 
        "search-results-per-line": 6,
        "search-results-per-line-with-ad-slot": 6,
        "search-results-per-line-with-map": 3,
        "search-results-per-line-with-map-and-ad-slot": 3
    ),
    "bp21": (
        "start-breakpoint": 2561,  
        "end-breakpoint": 1000000,
        "img-size": 414, 
        "search-results-per-line": 8,
        "search-results-per-line-with-ad-slot": 8,
        "search-results-per-line-with-map": 4,
        "search-results-per-line-with-map-and-ad-slot": 4
    ),
) !default;

$search-result-border: 1px !default;
$search-result-whitespace-x: 12px !default;
$search-result-whitespace-y: 12px !default;
$search-results-list-whitespace-left: 16px !default;
$search-results-list-whitespace-right: 6px !default;
$search-map-whitespace: 10px !default; 
$search-sidebar-adblock-width: 160px !default;