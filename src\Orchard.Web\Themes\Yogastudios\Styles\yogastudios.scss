﻿/* yoga-studios theme for discoverize.com portal - created 16.10.12 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "default-variables";
@import "_variables.scss";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
$cb-bg-hp: rgba(255, 255, 255, 1);
 
/* end import */

/* graphics */

body {
    background: none;
    font-family: trebuchet ms, sans-serif; 
}


.hr-custom {
    background: url(/themes/yogastudios/styles/img/finde-dein-yoga-divider.png) no-repeat center center;
    overflow: hidden;
    height: 70px;
    border: none;
    width: 100%;
    margin-top: 32px;
    margin-bottom: 16px;

}

/* end graphics */

/* layout */

/* end layout

/* header */
.header-top-inside,
.header-main-inside {
    width: 1200px;
    padding-left: 8px;
    padding-right: 8px;
    @include primary-font;
}

.header-top {
    background: #eee;
    color: $primary;
}
.tagline {
    @include primary-font;
    color: $primary;
    line-height: 1.6;
    font-size: 1em;
    margin-top: 1px;
    font-weight: bold;
    padding: 0;
    float: left;
}
.hd15 {
    color: $secondary !important;
}

.header-main {
    background: #f9f9f9;
}

.logo {
    padding: 12px 0 12px;
    margin-right: 12px;
}

.nav a {
    @include primary-font;
    color: $primary-darker;
    font-weight: bold;
    font-size: 1.2em;
}

// .nav .current,
// .nav a:hover {
//     background: $primary;
//     color: $txt-on-primary;
// }
// .nav a.nav31 {
//     background: #ccc;
// }

// .nav-level-2 {
//     background: none;
//     border: none;
// }
// .nav-level-2 a {
//     background: #fff;
//     padding: 12px 8px 10px;
//     text-align: left;
//     border-top: none;
//     border-bottom: $primary 1px solid;
//     width: 330px;
// }
// .nav-level-2 a:hover {
//     background: $primary;
//     color: $txt-on-primary;
// }
// .sub-2-nav {
//     background: #fff;
// }

.global-search {
    @include global-search($width: 140px); 
    margin: 22px 5px 0;
    color: #ccc;
    .sticky-header & {
        margin-top: 0px;
    }
}

.header {
    .navm1,
    .navm2 {
        @include linear-gradient(lighten(#f9f9f9, 5), darken(#f9f9f9, 5));
        border-color: darken(#f9f9f9, 7);
        border-right-color: darken(#f9f9f9, 15);
        border-bottom-color: darken(#f9f9f9, 30);
        color: $primary;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3), 0 0 1px rgba(0, 0, 0, 0.3);
    }
    .navm21 {
        color: $primary;
    }
}

/* end header */

/* homepage */

.mini-search {
    min-height: 600px;
    &:before { 
        display: none;
    }
}
.ms10 {
    color: $txt-on-neutral;
    font-size: 1.1em; 
}
.ms26 {
    text-align: center;
}
.ms261 {
    @extend .button-secondary;
    font-size: 1.1em;
}
.mini-search-entry-type {
    border-radius: 0 0 32px 32px;
}
.mini-search-entry-types-tabs {
    li,
    li.active {
        a {
            color: $txt-on-neutral;
            font-weight: 700;
        }
    }
}
.timely.ai1ec-agenda-widget-view {
    margin: 0 auto;
}
/* end homepage */

/* search page */

.sfu, .sf6 {
    font-size:0.9em;
}
.sfu-list {
    .mc32, .slf2, .slf2 label, .checkbox {
        font-weight: bold !important;
    }
}
.sf6 .sf61 {
    font-weight: bold !important;
}

.sf61-1, .epf61-1 { 
    @extend .icon-leaf;
}
.sf61-2, .epf61-2 { 
    @extend .icon-user;
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-yoga-lotus;
    color: #fff;
    &:before {
        top: 3px;
    }
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-yoga-lotus;
        color: #fff;
        &:before {
            left: 0;
            top: 3px;
        }
    }
}
.map-marker-premium span.map-marker-icon:before {
    color: #fff;
    left: 2px;
    top: 5px;
}

.map-marker-stack span {
    color: #fff;
}
.map-button-cluster {
    @include text-background-offset(rgba(0, 0, 0, 0.2)); 
    color: #fff;
}

.map-button-cluster-active {
    @include linear-gradient(lighten($primary, 12), lighten($primary, 2));
}

.sp110 {
    @include linear-gradient(
        transparentize(lighten($primary, 50), 0.9),
        transparentize(lighten($primary, 45), 0),
        0%,
        66%
    );
}
.sp19 {
    background: #fff;
    color: $primary;
    border: solid 1px $primary;
    &:hover {
        background: $primary;
        color: #fff;
    }
}

/* end search page */

/* entry page */

.ep33 {
    &.list31,
    &.list32,
    &.list33 {
        width: 100%;
    }
    .do20 {
        td,
        th {
            padding: 6px;
        }
    }
}

/* end entry page */

/* footer */
.design-guide {
    border-bottom: solid 1px #ccc;
}
 
blockquote {
    font-weight: 400;
    color: #777;
}

.footer-top{
    @include footer-top(".icon-yoga-lotus", $primary-lighter);
    background: #ffffff13; 
}

footer {
    background: rgba(0, 0, 0, 0.65); 
    border: none;
}
.footer {
    @extend .center;
    position: relative; 
}
.footer .logo {
    display: block;
    float: none;
    margin-bottom: 32px;
}
.footer .logo-name {
    padding-top: 1em;
}
.footer {
    @extend .center;
    .tagline {
        float: none;
        font-size: 2em;
        a {
            color: #fff;
            padding: 1em 0;
            display: block;
        }
    }
}
.foot-logo {
    display: block;
    .mini-tagline {
        width: auto;
        text-align: center;
    }
}
.foot-nav {
    width: 25%;
    @include media("<=640"){
        width: 50%;
    }
    @include media("<=420"){
        width: 100%;
    }
}
.foot-nav a {
    color: #fff;
}

/* end footer

/* responsive design */
@import "respond";
/* below 1200 */
@media only screen and (max-width: 1200px) {
    .header-top-inside,
    .header-main-inside {
        width: auto;
    }
}
/* below 960 */
@media only screen and (max-width: 960px) {
    .header-top-inside {
        background: $grey;
        a {
            color: $primary;
        }
    }
    .nav {
        width: 360px;
        max-width: 360px;
        a {
            color: $primary-darker;
        }
    }
    .nav-level-2 a {
        width: 270px;
    }
    .tagline {
        color: $primary;
    }

    .header-main-inside {
        background: #fff;
    }

    .logo {
        margin-right: 8px;
    }
}
/* below 768 */
@media only screen and (max-width: 767px) {
}
/* below 480 */
@media only screen and (max-width: 479px) {
}
/* below 360 */
@media only screen and (max-width: 360px) {
    .nav {
        font-size: 10px;
        width: 270px;
    }
    .nav-subnav-collapser {
        width: 38px;
        height: 38px;
        span {
            margin-left: 0;
        }
    }
    .nav-level-2 a {
        box-sizing: border-box;
        width: 225px;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

// Cookies
.cc_dialog {
    @include cookies(
        $type-of-graphic: logo,
        $graphic: 'img/logo.png',
    );
}