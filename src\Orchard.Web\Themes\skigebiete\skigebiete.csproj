﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{E6236941-2A84-431D-BCD1-4212F91080F8}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Skigebiete</RootNamespace>
    <AssemblyName>Skigebiete</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkProfile />
    <UseIISExpress>false</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\lib\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.ComponentModel.DataAnnotations">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-500.svg" />
    <Content Include="Styles\fonts\rajdhani-v9-latin-600.svg" />
    <Content Include="Styles\img\calltoaction-bg.jpg" />
    <Content Include="Styles\img\divider-icon.png" />
    <Content Include="Styles\img\divider.png" />
    <Content Include="Styles\img\favicon.ico" />
    <Content Include="Styles\img\interest-bg.jpg" />
    <Content Include="Styles\img\logo.png" />
    <Content Include="Styles\img\logo-bewertungs-widget.png" />
    <Content Include="Styles\img\logo-footer.png" />
    <Content Include="Styles\img\logo-invoice.png" />
    <Content Include="Styles\img\searchfield.jpg" />
    <Content Include="Styles\img\logo-manage.png" />
    <Content Include="Styles\img\default-image-1200.jpg" />
    <Content Include="Styles\img\loading-large.gif" />
    <Content Include="Styles\img\skigebiete-logo.png" />
    <Content Include="Styles\img\skigebiete-logo.svg" />
    <Content Include="Styles\img\thematica-logo.png" />
    <Content Include="Styles\img\transparent-75.png" />
    <Content Include="Styles\img\transparent.png" />
    <Content Include="Styles\portalmanagement-custom.css" />
    <Content Include="Styles\skigebiete.css" />
    <Content Include="Web.config" />
    <Content Include="Views\Web.config" />
    <Content Include="Scripts\Web.config" />
    <Content Include="Styles\Web.config" />
    <Content Include="Theme.txt" />
    <Content Include="Theme.png" />
    <Content Include="Placement.info">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Layout.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ThemeImageSettings.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Orchard\Orchard.Framework.csproj">
      <Project>{2D1D92BB-4555-4CBE-8D0E-63563D6CE4C6}</Project>
      <Name>Orchard.Framework</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\Orchard.Core.csproj">
      <Project>{9916839C-39FC-4CEB-A5AF-89CA7E87119F}</Project>
      <Name>Orchard.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\rating-widget.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_variables.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\skigebiete.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-500.eot" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-500.ttf" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-500.woff" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-500.woff2" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-600.eot" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-600.ttf" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-600.woff" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\rajdhani-v9-latin-600.woff2" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_HeaderNavigationOld.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterNavigationOld.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterTopOld.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterLogoTop.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\MiniNavSocial.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_NavigationLogo.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets. -->
  <Target Name="BeforeBuild" DependsOnTargets="ValidateContentFiles">
  </Target>
  <Target Name="ValidateContentFiles">
    <Error Condition="!Exists(%(Content.FullPath))" Text="Missing Content file [%(Content.FullPath)]" />
  </Target>
  <Target Name="AfterBuild" DependsOnTargets="AfterBuildCompiler">
    <PropertyGroup>
      <AreasManifestDir>$(ProjectDir)\..\Manifests</AreasManifestDir>
    </PropertyGroup>
    <!-- If this is an area child project, uncomment the following line:
    <CreateAreaManifest AreaName="$(AssemblyName)" AreaType="Child" AreaPath="$(ProjectDir)" ManifestPath="$(AreasManifestDir)" ContentFiles="@(Content)" />
    -->
    <!-- If this is an area parent project, uncomment the following lines:
    <CreateAreaManifest AreaName="$(AssemblyName)" AreaType="Parent" AreaPath="$(ProjectDir)" ManifestPath="$(AreasManifestDir)" ContentFiles="@(Content)" />
    <CopyAreaManifests ManifestPath="$(AreasManifestDir)" CrossCopy="false" RenameViews="true" />
    -->
  </Target>
  <Target Name="AfterBuildCompiler" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(ProjectDir)\..\$(ProjectName)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>9534</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>
          </IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>http://orchard.codeplex.com</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
  <Import Project="..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
</Project>