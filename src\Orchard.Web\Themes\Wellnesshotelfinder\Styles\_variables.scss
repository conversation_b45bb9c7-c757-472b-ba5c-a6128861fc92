/* variables for wellnesshotels - moved 23.11.2019 - by andrej telle - discoverize.com */

/* variables */
$page-width: 1161px;
$mobile-ui: $page-width;
$single-line-mini-search: true;
$mini-search-theme-color: rgba(0,0,0,0.5);
$mini-search-theme-txt-color: rgb(255, 255, 255);
$full-width-header: true;
$mini-search-full-height: true;

$button-options: (
    "border": false,
    "rounded-corners": true,
    "rounded-corners-value": 5px 5px 5px 5px,
    "gradient-background": false,
    "text-shadow": false,
    "shadow": false,
);

/* colors */
$primary: #a7388c;
$primary-darker: darken($primary, 5%);
$primary-lighter: lighten($primary, 5%);
$txt-on-primary: #fff;
$txt-shadow-on-primary: rgba(0, 0, 0, 0.45);

$secondary: #b1998b;
$secondary-darker: darken($secondary, 5%);
$secondary-lighter: lighten($secondary, 5%); 
$txt-on-secondary: #fff; 
$txt-shadow-on-secondary: rgba(0, 0, 0, 0.75);

$tertiary: #00a9bd;
$txt-on-tertiary: #fff;

$quaternary: #e63959; 
$txt-on-quaternary: #fff;

$link-color: $primary;

$grey: #111212;
$bg-area: #f4f1ec; 
$bg-properties: #eae3d8; 

$flat-nav-bg: #fff;
$flat-nav-txt: $primary;

$nav-link-padding: 24px 12px; 
$nav-font-size: 1.2em;
$nav-color: transparent;
$nav-txt-color: $primary; 
$nav-color-current: $primary;
$nav-txt-color-current: $txt-on-primary;
$nav-collapser-color: $primary;

$flat-nav-bg-current: $primary;
$flat-nav-txt-current: $txt-on-primary;
 
$ep-nav-active-bg: $primary;
$ep-nav-active-color: $txt-on-primary; 

$txt-highlight: $primary;  

/* end colors */
/* typography */
// Footer
$footer-bg: darken($tertiary, 5);
$footer-links-color: $txt-on-tertiary;
$footer-headers-color: $txt-on-tertiary;
$footer-claim-color: $txt-on-tertiary;

$footer-top-bg-color: darken($secondary-darker, 10);

$footer-custom-1-bg: $footer-bg ;
$footer-custom-1-color: $txt-on-tertiary;

$footer-custom-2-bg: $footer-bg;
$footer-custom-2-color: $txt-on-tertiary;

/* Import Google Web Fonts*/
/* montserrat-regular - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 400;
    src: url('./fonts/montserrat-v29-latin-regular.eot'); /* IE9 Compat Modes */
    src: url('./fonts/montserrat-v29-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('./fonts/montserrat-v29-latin-regular.woff2') format('woff2'), /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
         url('./fonts/montserrat-v29-latin-regular.woff') format('woff'), /* Chrome 5+, Firefox 3.6+, IE 9+, Safari 5.1+, iOS 5+ */
         url('./fonts/montserrat-v29-latin-regular.ttf') format('truetype'), /* Chrome 4+, Firefox 3.5+, IE 9+, Safari 3.1+, iOS 4.2+, Android Browser 2.2+ */
         url('./fonts/montserrat-v29-latin-regular.svg#Montserrat') format('svg'); /* Legacy iOS */
  }
  /* montserrat-600 - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 600;
    src: url('./fonts/montserrat-v29-latin-600.eot'); /* IE9 Compat Modes */
    src: url('./fonts/montserrat-v29-latin-600.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('./fonts/montserrat-v29-latin-600.woff2') format('woff2'), /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
         url('./fonts/montserrat-v29-latin-600.woff') format('woff'), /* Chrome 5+, Firefox 3.6+, IE 9+, Safari 5.1+, iOS 5+ */
         url('./fonts/montserrat-v29-latin-600.ttf') format('truetype'), /* Chrome 4+, Firefox 3.5+, IE 9+, Safari 3.1+, iOS 4.2+, Android Browser 2.2+ */
         url('./fonts/montserrat-v29-latin-600.svg#Montserrat') format('svg'); /* Legacy iOS */
  }

@mixin primary-font {
    font-family: "Montserrat";
    font-weight: 400;
}
@mixin secondary-font {
    font-family: "Montserrat";
    font-weight: 400;
}

/* end typography */

/* end variables */
