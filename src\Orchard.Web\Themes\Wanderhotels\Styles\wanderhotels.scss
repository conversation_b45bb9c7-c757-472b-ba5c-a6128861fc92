﻿/* chaletdorf theme for discoverize.com portal - created 16.10.12 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */
@import "default-variables";
@import "variables";
@import "4_layouts/_thematica-global-template-style-variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
@import "4_layouts/_thematica-global-template-style";
$cb-bg-hp: rgba(255, 255, 255, 1);

/* end import */

/* overwrites */
/* graphics */

body {
    background: none;
    @include primary-font; 
}

/* end graphics */ 

/* layout */

.search-page {
    margin-top: 0;
    padding-top: 0;
}

/* end layout */

/* global */

h1, h2, h3, h4, h5, h6 {
    @include secondary-font;
    color: $primary;
}
/* end global */

/* header */

.logo-graphic {
    width: 289px;
    @include media("<=mobile-ui") {
        height: 50px;
    }
}

@media only screen and (min-width: 1160px) {
    .header-main .logo img {
        position: relative;
        top: -10px;
    }
}

.sticky-header .logo img {
    width: auto;
}

.header, .header-top, .header-main, .header-top-inside {
    background: $grey-warm;
}
.header-top {
    position: relative;
    top: 50px;
    background: none;
}
.header-top-inside {
    padding: 0;
    background: none;
}
.tagline {
    @include primary-font;
    color: $dark;
    padding: 0;
    float: left;
    font-size: 0.85em;
}
.mini-nav {
    text-transform: uppercase;
    @include primary-font;
    font-weight: 600;
    position: relative;
    z-index: 10;
    a {
        text-transform: uppercase;
        color: $secondary;
        border-right: none;
        @include primary-font;
        font-weight: 600;
    }
    a:hover {
        color: $txt-on-primary;
    }
    span {
        color: $primary;
    }
    .mn11 {
        border-right: none;
    }
}
@include header-logo-above-nav($logo-position: left);
.header-main-inside {
    @include justify-content(space-between);
    .logo {
        z-index: 100;
    }
}
@media screen and (min-width: $page-width) {
    .sticky-header {
        .header-search-links {
            display: none;
        }
    }
}

.header-search-links {
    margin-right: calc((100% - #{$page-width}) / 2);
    padding-right: 12px;
}
.search-links-list {
    list-style: none;
    li {
        float: left;
        margin-right: 45px;
    }
    li:last-child {
        margin-right: 0;
    }
    a:hover {
        background-color: transparent;
    }
    img {
        width: 50px;
        height: 50px;
    }
}
.sll-3 {
    margin-right: 0px;
}

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 22px 16px 17px 16px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav {
//     background-color: $grey;
//     a {
//         @include primary-font;
//         border: none !important;
//         background: none;
//         color: $txt-on-grey;
//         display: block;
//         text-transform: uppercase;
//     }
//     .mn4 {
//         padding-top: 6px;
//     }
//     .mn5 {
//         border-bottom-width: 0px;
//     }
//     .mn5,
//     .mn6 {
//         float: left;
//     }
// }
// .nav .current {
//     background: $primary;
//     color: $txt-on-primary;
// }

// .nav > li:hover > a {
//     background: $primary-darker;
// }

// a.nav01 {
//     @extend .icon-home;
// }
// a.nav10 {
//     padding: 22px 16px 17px 16px;
// }
// a.nav01,
// a.nav21,
// a.nav31,
// a.nav41 {
//     @include border-box;
//     padding: 22px 16px 17px 16px;
// }
// .nav .nav19,
// .nav .nav10 {
//     @include rounded(0, 0, 0, 0);
// }

// .nav-level-2 {
//     @include shadow-remove;
//     left: auto;
//     right: 0;
//     background: none;
//     border: none;
// }
// .nav-level-2 a {
//     background: transparentize($primary-darker, 0.1);
//     color: #fff;
// }
// .nav-level-2 a:hover {
//     background: $primary-darker;
//     color: $txt-on-primary;
// }

.global-search {
    @include global-search(265px);
    padding: 17px 3px 6px 0;
    color: #ccc;
    margin-right: 0;
}

body .mini-search {
    padding-bottom: 150px; 
}
.mini-search {
    align-items: flex-end;
    padding: 40px 12px;
    &:before {
        background: none;
    }
}

html
body
.mini-search {
    @include media("<=mobile-ui") {
        height: 600px;
    }
}

.ms10 {
    @include secondary-font;
    @include text-stroke(darken($primary, 20), 1px);
    color: darken($primary,10);
}

.sb2 {
    color: $txt-on-primary;
    background-color: $primary-lighter;
}

/* end header */

/* homepage */

@include mini-search-full-page(#fff, "img/wanderhotels-freunde-berge-natur.jpg", (#fff, 0));

.mini-search-entry-type {
    background: rgba(255, 255, 255, 0.6);
}
.ms23 {
    color: $primary;
}

.welcome-inside {
    text-align: center;
}
.welcome h4 {
    color: $grey;
}

.welcome {
    h1 {
        color: $primary;
        text-transform: uppercase;
        font-weight: 600;
    }

    h2 {
        font-size: 20px;
    }
}
/* end homepage */

/* entry page */

/* dont show blockquotes on description */
.cp30:before,
.cp30:after {
    display: none;
}

/* end entry page */

/* search page */

.sf61-4, .epf61-4 { 
    @extend .icon-info-sign;
}
.sf61-5, .epf61-5 { 
    @extend .icon-wanderschuh;
}
.sf61-6, .epf61-6 { 
    @extend .icon-berge-wanderhotel;
}
.sf61-7, .epf61-7 { 
    @extend .icon-home;
}
.sf61-8, .epf61-8 { 
    @extend .icon-hotel;
}
.sf61-9, .epf61-9 { 
    @extend .icon-hand-shake-2;
}
.sf61-10, .epf61-10 { 
    @extend .icon-bicycle;
}
.sf61-11, .epf61-11 { 
    @extend .icon-map-marker;
}

.map-button {
    color: #fff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5), 0 0 1px rgba(0, 0, 0, 0.5);
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-berge-wanderhotel;
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-berge-wanderhotel;
        &:before {
            left: 0px;
            top: 1px;
            font-size: 24px;
        }
    }
}
.map-marker-premium span.map-marker-icon:before {
    left: 0px;
    top: 0px;
    font-size: 33px;
}
.map-marker-static span.map-marker-icon,
.map-marker-premium.map-marker-primary span.map-marker-icon {
    &:before {
        left: -1px;
        top: 1px;
        font-size: 24px;
    }
}

.map-marker-primary {
    border-color: $primary;

    &:hover {
        background: $txt-on-primary;
    }
}

.sp110 {
    @include linear-gradient(
        transparentize(lighten($primary, 40), 0.9),
        transparentize(lighten($primary, 35), 0),
        0%,
        66%
    );
}
.sp19 {
    background: #fff;
    color: $primary;
    border: solid 1px $primary;
    &:hover {
        background: $primary;
        color: #fff;
    }
}



/* end search page */


/* kategorie icons */

/* hotel kategorie */
// 3
.Klassifizierung_1 {
    @include category-stars(3);
}
// 3s
.Klassifizierung_2 {
    @include category-stars(3);
    &:after {
        content: "S";
    }
}
// 4
.Klassifizierung_3 {
    @include category-stars(4);
}
// 4s
.Klassifizierung_4 {
    @include category-stars(4);
    &:after {
        content: "S";
    }
}
// 5
.Klassifizierung_5 {
    @include category-stars(5);
}
// 5s
.Klassifizierung_6 {
    @include category-stars(5);
    &:after {
        content: "S";
    }
}
.Klassifizierung_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}

/* preisniveau */
// €
.Preisniveau_1 {
    @include category-euros(1);
}
// €€
.Preisniveau_2 {
    @include category-euros(2);
}
// €€€
.Preisniveau_3 {
    @include category-euros(3);
}
// €€€€
.Preisniveau_4 {
    @include category-euros(4);
}


// wanderschuh 
@mixin Wanderschuhe_All {
    @extend .icon-wanderschuhe-5;
    margin-right: 2px;
    font-size: 22px;
    line-height: normal;
    &:before {
        color: $primary;
        overflow: hidden;
        position: relative;
        top: 6px;
        min-width: auto;
    }
}

// 1 
.Wanderschuhe_1 {
    @include Wanderschuhe_All;
    &:before { 
        width: 27px;
    }
}
// 2
.Wanderschuhe_2 {
    @include Wanderschuhe_All;
    &:before {
        width: 52px;
    } 
}
// 3
.Wanderschuhe_3 {
    @include Wanderschuhe_All;
    &:before {
        width: 78px;
    }
}
// 4
.Wanderschuhe_4 {
    @include Wanderschuhe_All;
    &:before {
        width: 105px;
    }
}
// 5
.Wanderschuhe_5 {
    @include Wanderschuhe_All;
    &:before {
        width: 130px;
    }
}
// 6
.Wanderschuhe_6 {
    @include Wanderschuhe_All;
    &:before {
        width: auto;
    }
}
.Wanderschuhe_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

/* end kategorie icons */

/* footer */
footer {
    border: none;
    width: 100%;
    background: darken($secondary, 5);
}

.footer-top {
    @include footer-top(".icon-wanderschuh", $primary);
}

.footer {
    padding-top: 24px;
}
.footer-logo {
    img {
        max-width: 360px;
    }
    h3 {
        padding: 36px 0;
    }
}
.foot-nav a {
    color: #fff;
}

/* end layout */


/* start Mini gallery */

.mini-gallery12 {
    &:before {
        display: none;
    }
}

.rating-actual {
    color: $primary;
}

/* end Mini gallery */

/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: $page-width) {
    .nav-wrapper {
        background-color: #fff;
    }
    .header-search-links {
        display: none;
    }
}
/* below 768 */
@media only screen and (max-width: 769px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.2em;
    }
}
/* below 380 */
@media only screen and (max-width: 380px) {
    .ms10 {
        font-size: 1em;
    }
}
/* below 320 */
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

@media only screen and (min-width: 1200px) {
    .mini-search-inside {
        width: 80%;
    }
}

// Cookies
// .cc_dialog {
//     @include cookies(
//         $type-of-graphic: logo,
//         $graphic: 'https://wanderhotels.info/themes/wanderhotels/styles/img/logo-wanderhotel-(hash2711122157).png'
//     );
// }