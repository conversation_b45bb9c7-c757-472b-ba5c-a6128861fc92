/* pills */

// Settings

// Primary
$pills-settings--primary-color: $primary !default;
$pills-settings--primary-background-color: $neutral-light !default;
$pills-settings--primary-border-width: 1px !default;
$pills-settings--primary-border-color: #c1c1c1 !default;
$pills-settings--primary-border-color-hover: darken($primary,10%) !default;
$pills-settings--primary-border-style: solid !default;
$pills-settings--primary-border-radius: 24px !default;
$pills-settings--primary-font-weight: 400 !default;
$pills-settings--primary-font-size: inherit !default;

// Secondary
$pills-settings--secondary-color: $primary !default;
$pills-settings--secondary-background-color: $neutral-light !default;
$pills-settings--secondary-border: 1px solid #c1c1c1 !default;
$pills-settings--secondary-border-radius: 12px !default;
$pills-settings--secondary-font-weight: bold !default;
$pills-settings--secondary-font-size: inherit !default;

@mixin filter-pill--primary(
    $color: $pills-settings--primary-color,
    $background-color: $pills-settings--primary-background-color,
    $border-color: $pills-settings--primary-border-color,
    $border-color-hover: $pills-settings--primary-border-color-hover,
    $border-style: $pills-settings--primary-border-style,
    $border-width: $pills-settings--primary-border-width,
    $border-radius: $pills-settings--primary-border-radius,
    $font-weight: $pills-settings--primary-font-weight,
    $margin: 0,
    $padding: 14px 6px 12px 12px,
    $box-sizing: border-box,
    $display: flex,
    $align-items: stretch,
    $float: left,
    $cursor: pointer,
    $position: relative,
    $font-family: 'primary',
    $font-size: $pills-settings--primary-font-size
) {
    color: $color;
    background-color: $background-color;
    border-style: $border-style;
    border-width: $border-width;
    border-color: $border-color;
    border-radius: $border-radius;
    margin: $margin;
    padding: $padding;
    box-sizing: $box-sizing;
    display: $display;
    align-items: $align-items;
    float: $float; 
    cursor: $cursor;
    position: $position;
    font-size: $font-size;

    @if($font-family == 'primary'){
        @include primary-font; 
    } @else if($font-family == 'secondary') {
        @include secondary-font; 
    }
    font-weight: $font-weight;
    
    & > label,
    & > a > label,
    & > strong {
        font-weight: $font-weight;
    }

    & > a{
        color: $color;
    }

    &:hover{
        background-color: lighten($background-color,2);
        border-color: $border-color-hover;
        color: $color;
        & > label,
        & > a > label {
            color: $color;
        }
    }
}

@mixin filter-pill--secondary(
    $color: $pills-settings--secondary-color,
    $background-color: $pills-settings--secondary-background-color,
    $border: $pills-settings--secondary-border,
    $border-radius: $pills-settings--secondary-border-radius,
    $margin: 6px 12px 6px 0,
    $font-family: 'primary',
    $font-weight: $pills-settings--secondary-font-weight,
    $font-size: $pills-settings--secondary-font-size
){
    @extend .button-light;
    @include primary-font;
    background: $background-color;
    color: $color;
    border: $border;
    border-radius: $border-radius;
    margin: $margin;
    font-size: $font-size;
    
    @if($font-family == 'primary'){
        @include primary-font; 
    } @else if($font-family == 'secondary') {
        @include secondary-font; 
    }

    font-weight: $font-weight;

    & > label {
        font-weight: $font-weight;
    }
}

%pill--primary {
    @include filter-pill--primary;
}

%pill--primary--compact {
    @include filter-pill--primary(
        $padding: 8px 8px 6px 8px
        );
}

%pill--primary--square {
    @include filter-pill--primary(
        $padding: 8px 8px 6px 8px,
        $border-radius: 8px
        );
}

@mixin property-pill-display(
    $padding-top: 6px,
    $padding-bottom: 6px,
    $padding-right: 5px,
    $padding-left: 10px
 ) {
    @include primary-font;
    background: $property-pill-display-bg;
    border: $property-pill-display-border solid 1px; 
    border-radius: $property-pill-display-rounded-corners;
    margin: 0;
    color: $txt-color;
    padding-top: $padding-top;
    padding-bottom: $padding-bottom;
    padding-left: $padding-left;
    padding-right: $padding-right;
    text-align: left;
    align-content: center;
    
}


@mixin property-pill-click(
    $padding-top: 8px,
    $padding-bottom: 8px,
    $padding-right: 5px,
    $padding-left: 10px
 ) {
    @include primary-font;
    background: $property-pill-click-bg;
    border: $property-pill-click-border solid 1px; 
    border-radius: $property-pill-click-rounded-corners;
    margin: 0;
    color: $property-pills-click-text-color;
    padding-left: $padding-left; 
    padding-right: $padding-right;
    text-align: left;
    align-content: center;
    &.mc32 {
        padding-top: calc(#{$padding-top} - 1px) !important;
        padding-bottom: $padding-bottom !important;
    }
    .checkbox {
        padding-top: $padding-top;
        padding-bottom: $padding-bottom;
        flex-grow: 1;
        cursor: pointer;
    }
    &:hover {
        border: $property-pill-click-hover-border solid 1px;
        background: $property-pill-click-hover-bg;
        cursor: pointer;
    }
}


.mc32, .slf2 {
    @include button-as-link;
    background: transparent;
    border: none;

    label {
        font-weight: normal;
    } 

    &:hover {
        background: $neutral-background;
        color: $pills-settings--primary-color;
    }
}






