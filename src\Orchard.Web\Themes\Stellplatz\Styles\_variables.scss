/* variables for restaurant - moved 23.11.2016 - by andrej telle - discoverize.com */

/* variables */
$page-width: 1161px;
$mobile-ui: $page-width;
$mini-search-theme-color: rgba(0, 0, 0, 0.4);

/* colors */
$primary: #055445;
$primary-darker: darken($primary, 5%);
$primary-lighter: lighten($primary, 5%);
$txt-on-primary: #fff;
$txt-shadow-on-primary: rgba(0, 0, 0, 0.5); 
 
$dark: #1c1c1c;

$medium: #999;
$medium-lighter: #bababa; 
$medium-lightest: #e1e1e1;

$secondary: #00A139;
$secondary-darker: darken($secondary, 5%);
$secondary-lighter: lighten($secondary, 5%);
$txt-on-secondary: #fff;
$txt-shadow-on-secondary: rgba(000, 000, 000, 0.5);

$tertiary: #EA6B44;
$txt-on-tertiary: #fff;
$txt-shadow-on-tertiary: rgba(000, 000, 000, 0.5);

$link-color: darken($primary, 0);
$sticky-nav-link-color: $txt-on-primary;

$grey: #333333;

$nav-txt-color: $txt-on-primary;

$flat-nav-bg: $primary;
$flat-nav-txt: $txt-on-primary;

$nav-font-size: 1em;

$editorial-rating-background: scale-color($primary, $lightness: 95%);
$editorial-rating-border: scale-color($primary, $lightness: 20%);

/* end colors */

/* Header parts heights */
$header-main-h: 66px;
$header-sub-h: 24px;

/* Screens */
$screen-xs: 767px;
$screen-sm: 991px;

/* cards */ 
$card-shadow: 1px 1px 4px 2px rgba(0, 0, 0, 0.05);
$card-border: solid 1px #ddd;
$card-rounded-corner: 12px;

/* typography */
$footer-bg: $primary;
$footer-links-color: $txt-on-primary;

$footer-top-bg-color: $txt-on-primary;

$footer-custom-1-color: $txt-on-primary;

$footer-custom-2-color: $txt-on-primary;

/* Import Google Web Fonts*/
/* roboto-100 - latin */
@font-face { font-display: fallback; 
    font-family: "Roboto";
    font-style: normal;
    font-weight: 100;
    src: url("./fonts/roboto-v20-latin-100.eot"); /* IE9 Compat Modes */
    src: local("Roboto Thin"), local("Roboto-Thin"),
        url("./fonts/roboto-v20-latin-100.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/roboto-v20-latin-100.woff2") format("woff2"),
        /* Super Modern Browsers */ url("./fonts/roboto-v20-latin-100.woff")
            format("woff"),
        /* Modern Browsers */ url("./fonts/roboto-v20-latin-100.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/roboto-v20-latin-100.svg#Roboto") format("svg"); /* Legacy iOS */
  }
  /* roboto-300 - latin */
  @font-face { font-display: fallback; 
    font-family: "Roboto";
    font-style: normal;
    font-weight: 300;
    src: url("./fonts/roboto-v20-latin-300.eot"); /* IE9 Compat Modes */
    src: local("Roboto Light"), local("Roboto-Light"),
        url("./fonts/roboto-v20-latin-300.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/roboto-v20-latin-300.woff2") format("woff2"),
        /* Super Modern Browsers */ url("./fonts/roboto-v20-latin-300.woff")
            format("woff"),
        /* Modern Browsers */ url("./fonts/roboto-v20-latin-300.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/roboto-v20-latin-300.svg#Roboto") format("svg"); /* Legacy iOS */
  }
  /* roboto-regular - latin */
  @font-face { font-display: fallback; 
    font-family: "Roboto";
    font-style: normal;
    font-weight: 400;
    src: url("./fonts/roboto-v20-latin-regular.eot"); /* IE9 Compat Modes */
    src: local("Roboto"), local("Roboto-Regular"),
        url("./fonts/roboto-v20-latin-regular.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/roboto-v20-latin-regular.woff2")
            format("woff2"),
        /* Super Modern Browsers */ url("./fonts/roboto-v20-latin-regular.woff")
            format("woff"),
        /* Modern Browsers */ url("./fonts/roboto-v20-latin-regular.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/roboto-v20-latin-regular.svg#Roboto") format("svg"); /* Legacy iOS */
  }
  /* roboto-700 - latin */
  @font-face { font-display: fallback; 
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    src: url("./fonts/roboto-v20-latin-700.eot"); /* IE9 Compat Modes */
    src: local("Roboto Bold"), local("Roboto-Bold"),
        url("./fonts/roboto-v20-latin-700.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/roboto-v20-latin-700.woff2") format("woff2"),
        /* Super Modern Browsers */ url("./fonts/roboto-v20-latin-700.woff")
            format("woff"),
        /* Modern Browsers */ url("./fonts/roboto-v20-latin-700.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/roboto-v20-latin-700.svg#Roboto") format("svg"); /* Legacy iOS */
  }

@mixin primary-font {
    font-family: "Roboto";
    font-weight: 500;
    font-size: 1em;
}
@mixin secondary-font {
    font-family: "Roboto", sans-serif;
    font-weight: 300;
}
@mixin footer-font {
    font-family: "Roboto", sans-serif;
    font-weight: 300;
}

/* end variables */
