/* kinderhotel.info - custom theme for discoverize.com portal - created 18.12.12 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "1_base/_color-contrast-new";
@import "default-variables";
@import "variables";
@import "4_layouts/_thematica-global-template-style-variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
@import "4_layouts/_thematica-global-template-style";



/* end import */

/* overwrites */ 

// testing color
// .sp170 {
//     background: $secondary;
//     color: $txt-on-secondary;
// }

h1, h2, h3, h4, h5, h6, legend {
    @include secondary-font;
    font-weight: bold;
}
li {
    @include secondary-font;
}

// .nav-subnav-collapser {
//     background: transparent !important;
// }


/* graphics */ 

/* end graphics */


 
/* layout */

.additional-block--entry-page {
    width: 260px;
    @include media("<1741px") {
        &.additional-block {
            display: none;
        }
    }
}

.hp1 {
    background: #fff;
    padding: 12px;
    min-height: 800px;
}
.hd11 {
    color: #fff;
}
.header {
    background: $tertiary;
}
.header-top {
    background: $secondary;
}

@include media(">mobile-ui") {
    .nav-searchbox-inner--active  
    .twitter-typeahead {
        transition: .6s;

        & > input {
            padding: 3px 5px 2px 5px !important;
            top: 0 !important;
        }
    }
}


.tagline {
    color: #fff;
    font-size: 13px;
    padding-top: 0px;
}
.mini-nav a {
    color: #fff;
    border-color: #fff;
}

.logo-graphic {
    width: 280px;
    @include media("<=mobile-ui") {
        height: 40px;
    }
}

.sticky-header {
    .header-main-inside {
        width: calc(100% - 32px);
        margin-left: 16px;
        margin-right: 16px;
    }
    .logo img {
        height: 60px;
        width: 200px;
    }
}

.nav {
    @include media(">mobile-ui") {
        & > li > a {
            background: none;
            font-size: 1em;
            padding: 36px 12px 34px 12px;
            font-weight: bold;
        }
    }
}

.global-search {
    @include global-search(205px);
    color: #ccc;
    padding-top: 5px;
}

.navm1,
.navm2 {
    @include button(
        lighten($secondary, 10),
        $txt-on-secondary,
        rgba(255, 255, 255, 0.3),
        1em
    );
    background: transparent;
    float: left;
    padding: 0;
    width: 54px;
    height: 54px;
}

.sp16.spa12 {
    position: absolute;
    top: 5px;
    left: 54px;
    background: none !important;
    padding: 0 !important;
    border: none !important;
}

// startseite

// @include mini-search-full-page(#fff, "img/bg-welcome.jpg", (#fff, 0.7));

.home {
    .header {
        position: absolute;
        width: 100%;
        background: none;
    }
    .header-main {
        background: transparent;
        box-shadow: none;
    }
    .header-top {
        background: none;
    }
}
.home.scrolling-down, .home.scrolled-down, .home.scrolling-up {
    .header-main {
        background: $tertiary;
    }
}

.hero-img-wrapper:before {
    content:"";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: block;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, 
        rgba(0,0,0,0.4) 0%, 
        rgba(0,0,0,0.3) 100px, 
        rgba(0,0,0,0.2) 150px, 
        rgba(0,0,0,0.2) 20%, 
        rgba(0,0,0,0.2) 30%, 
        rgba(0,0,0,0.2) 40%, 
        rgba(0,0,0,0.2) 60%,
        rgba(0,0,0,0.2) 100%);
    z-index: 3;
}

.welcome .special {
    color: $primary;
    font-weight: 600;
}

.mini-search.mini-search {  
    min-height: 60vh;
    &:before {
        display:none;
    }
    @include media("<=mobile-ui") {
        height: 600px !important;
        align-items: start;
        padding-top: 120px;
    }

    @include media("<=479px") {
        .ms12{
            flex-grow: 1;
            justify-content: center;
            width: auto;
            
            .mc32{
                width: 100%;
                padding: 0 .25em;
            }
        }
    }
}
 
.mini-search-entry-type {
    background: none; 
    @include media("<=mobile-ui"){
        padding-bottom: 2em;
    }
}

// fake box behind mini-search
// .ms11, .ms12, .ms25 {
//     position: relative;
//     top: calc(30vh - 50px);
//     overflow: visible;
//     &:before {
//         content:"";
//         position: absolute;
//         top: -12px;
//         left: -12px;
//         right: -12px;
//         bottom: -12px;
//         background: $neutral-light;
//         width: calc(100% + 24px);
//         display: block; 
//         z-index: -1;
        
//     }
// }
// .ms12 {
//     margin-top: 7px;
// }
// .ms11:before {
//     border-radius: 12px 0 0 12px;
// }
// .ms25:before {
//     border-radius: 0 12px 12px 0;
// }

.ms11, .ms12, .ms25 {
    @include shadow;
}

.ms12 .mc32 {
    background: #fff;
}


.hero-img {
    background-position: center bottom; 
}

// custom positioning of hero img by name
.hero-img[style*="background-image: url("][style*="Tirol"]{
    background-position: center center;
} 
.hero-img[style*="background-image: url("][style*="Spa-Wellness"]{
    background-position: center top;
}

.hero-img[style*="background-image: url("][style*="center-center"]{
    background-position: center center;
}
.hero-img[style*="background-image: url("][style*="center-bottom"]{
    background-position: center bottom; 
}

.hero-img[style*="background-image: url("][style*="center-top"]{
    background-position: center top;
}
 
// .mini-search {
//     flex-direction: column;
//     flex-wrap: wrap;
//     box-sizing: border-box;
//     min-height: 500px;
//     padding-bottom: 70px;
//     background-position: center left;
//     background-color: $secondary;
//     justify-content: initial;
//     background-repeat: no-repeat;
//     &:before {
//         background: initial;
//     }
//     @include media("<=769px") {
//         height: 820px;
//         padding: 0 !important;
//         background-size: auto 420px;
//         background-position: center bottom;
//         .mini-search-inside {
//             flex-direction: column;
//             display: flex;
//             align-items: center;
//             padding: 12px;
//             max-width: initial;
//         }
//         .ms10 {
//             font-size: 1.2em;
//         }
//     }
// }

// .mini-search-inside{
//     box-sizing: border-box;    
//     @include media("<=480px") {
//         min-height: 455px;
//     }
// }

.e-map,
.e-filter {
    padding-left: 2em;
    padding-right: 2em; 
}
.ms10 {
    font-size: 2.2em;
    line-height: 1.1;
    color: #fff;
    max-width: 774px;
    margin: 0 auto;
    z-index: 1; 
    position: relative;
    @include media("<=769px"){
        font-size: 1.2em;
    }
    &:before {
        display: none;
    }
}

.ms10--highlight {
    position: relative;
    z-index: -1; 
    &:before {
        background: url("img/hero-highlight-bg.svg") no-repeat;
        background-size: 100% 100%;
        content: "";
        position: absolute;
        left: -40%;
        top: -30%;
        width: 170%;
        height: 170%;
        z-index: -1;
    }
}

.ms23, .ms261 {
    color: darken($primary, 10);
}
.ms23 {
    text-align: center;
    display: none;
}
.ms26 {
    @include hide-visually;
}
.ms100 {
    padding-top: 0.7em;
}
.mini-search {
    .hero-entry-banner {
        background: rgba(0,0,0,0.2);
        backdrop-filter: blur(4px);
        right: initial;
        bottom: 40px;
        @include media(">mobile-ui"){
            transform: translateX(-50%);
            left: 50%;
        }
        @include media("<=mobile-ui"){
            margin-top: 0;
        }
    }
}

@mixin hotel-link {
    background: rgba(0, 0, 0, 0.4);
    color: #fff;
    padding: 0.5em 0.8em;
    margin-left: 1em;
    position: absolute;
    right: 1em;
    top: 420px;
}
.hotel-link {
    @include hotel-link;
    @extend .icon-arrow-right;
}
.hotel-link-external {
    @include hotel-link; 
    @extend .icon-share;
    font-size: 1.3em;
    text-align: center;
    bottom: initial;
}

.mini-gallery {
    @include flex-order(5);
}
.explained {
    p a {
        @extend .icon-arrow-right;
        font-weight: bold;
    }
}
.e-map {
    h2 {
        @extend .icon-map-marker;
    }
}
.e-filter {
    h2 {
        @extend .icon-hotel;
    }
}

.benefit {
    @include flex-order(4);
    width: 100%;
    padding: 0;
}
.benefit-standout {
    @include rounded(0, 0, 0, 0);
    background: none;
    border: none;
    color: $txt-color;
    padding-left: 0;
    padding-right: 0;
    h1,
    h2 {
        text-align: center;
    }
    h1 {
        padding-bottom: 0;
    }
    h2 {
        padding-top: 0.5em;
    }
}

.mini-gallery {
    @include flex;
    @include flex-order(7);
}

.content-block-5 {
    @include flex-order(6);
}
.home-page .premium-block {
    @include flex-order(5);
    width: 100%;
}
.area {
    @include flex-order(8);
    width: 100%;
}
.properties {
    @include flex-order(9);
    width: 100%;
}

// end startseite

/* kategorie icons */

/* hotel kategorie */

// 3
.Klassifizierung_3 {
    @include category-stars(3);
}
// 4
.Klassifizierung_5 {
    @include category-stars(4);
}
// 5
.Klassifizierung_7 {
    @include category-stars(5);    
}
// 3s
.Klassifizierung_4 {
    @include category-stars(3);
    &:after {
        content: "S";
    }
}
// 4s
.Klassifizierung_6 {
    @include category-stars(4);
    &:after {
        content: "S";
    }
}
// 5s
.Klassifizierung_8 {
    @include category-stars(5); 
    &:after {
        content: "S";
    }
}
.Klassifizierung_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}

/* preisniveau */
 
// €
.Preisniveau_1 {
    @include category-euros(1);
}
// €€
.Preisniveau_2 {
    @include category-euros(2);
}
// €€€
.Preisniveau_3 {
    @include category-euros(3);
}
// €€€€
.Preisniveau_4 {
    @include category-euros(4);
}
.Preisniveau_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

/* end kategorie icons */

/* search page */

.sf61-1, .epf61-1 { 
    @extend .icon-info-sign;
}
.sf61-2, .epf61-2 { 
    @extend .icon-home;
}
.sf61-3, .epf61-3 { 
    @extend .icon-cutlery;
}
.sf61-63, .epf61-63 { 
    @extend .icon-swimming-pool;
}
.sf61-4, .epf61-4 { 
    @extend .icon-hotel;
}
.sf61-5, .epf61-5 { 
    @extend .icon-child;
}
.sf61-6, .epf61-6 { 
    @extend .icon-buggymodern;
}
.sf61-7, .epf61-7 { 
    @extend .icon-map-marker;
}

.sf61-37, .epf61-37 {
    @extend .icon-auszeichnung-default;
}

.map-button {
    @extend .button-secondary;
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-k;
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-k;
        &:before {
            left: 2px;
            top: 5px;
            font-size: 18px;
        }
    }
}
.map-marker-premium span.map-marker-icon:before {
    left: 5px;
    top: 7px;
    font-size: 24px;
}

.map-button-cluster {
    background: $secondary;
    color: $txt-on-secondary;
    &:hover {
        background: lighten($secondary,5%);
    }
}

.premium-booked, .premium-booked:hover {
    &:before {
        display: none;
    }
}


/* end search page */

/*// award

// search page

.spea1 {
    top: 0;
}
.spea12 {
    border-radius: 0 0 0 18px;
    width: 76px;
    height: 76px;
}
.spea13 {
    background: url("img/kinderhotel-info-award-2022-64.png") no-repeat;
    width: 64px;
    height: 64px;
    margin: 6px;
    display: inline-block;
    &:before {
        display: none;
    }
}
.spea21 {
    background: url("img/kinderhotel-info-award-2022-64.png") no-repeat;
    min-height: 42px;
    display: inline-block;
    content: none;
    &:before {
        display: none;
    }
}*/

/* entry page */

/*.epea1 {
    bottom: none;
}
.epea12 {
    border-radius: 0 0 0 18px;
    width: 76px;
    height: 76px;
}
.epea13 {
    background: url("img/kinderhotel-info-award-2022-64.png") no-repeat;
    width: 64px;
    height: 64px; 
    margin: 6px;
    display: inline-block;
    &:before {
        display: none;
    }
}
.epea21 {
    background: url("img/kinderhotel-info-award-2022-64.png") no-repeat;
    padding-left: 72px;
    min-height: 42px;
    display: inline-block;
    content: none;
    &:before {
        display: none;
    }
}

// end award*/

/* end entry page */

// todo: check priority
footer {
    background: $tertiary;
    position: relative;
    border-radius: $rounded-corner-large-3 $rounded-corner-large-3 0 0;
}
.footer-top {
    @include footer-top(".icon-k", $secondary);
}
.footer-top12 {
    @extend .button-light-white;
    margin-top: 2em;
}
.footer, .footer--custom {
    padding-top: 50px;
}

.footer-logo {
    margin: 3em 12px 6em 12px;
}
.foot-logo {
    img {
        width: 534px;
        max-width: 100%;
        margin: 0 auto;
    }
}
.foot-nav {
    h3 {
        text-transform: uppercase;
        font-size: 1em;
    }
}

/* end layout */
/* end overwrites */

/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: 960px) {
    .cb-picture-list-map {
        .cb-picture-content {
            font-size: 10pt !important;
            line-height: 1.7em;
            margin: 0;
            &:before {
                margin: 0px;
            }
        }
    }
    .nav i.nav-subnav-collapser, .nav .nav-subnav-collapser {
        background: darken($secondary, 10%); 
        border-color: darken($secondary, 15%);
        padding-top: 3px;
    }
    .nav-level-2,
    .sub-2-nav {
        background: $secondary;
    }
}
/* below 768 */
@media only screen and (max-width: 767px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .tagline {
        font-size: 1em;
    }
}
@media only screen and (max-width: 359px) {
}
/* below 320 */
@media only screen and (max-width: 319px) {
    .tagline {
        width: 160px;
    }
    .mini-nav a {
        border: none;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */ 
@import "internet-explorer";

// Facebook button in top nav
.fb-like {
    @include fb-buttons($width: 125px);
}

// Cookies
.cc_dialog {
    @include cookies(
        $type-of-graphic: logo,
        $graphic: 'https://kinderhotel.info/themes/kinderhotel-2025/styles/img/logo.png'
    );
}