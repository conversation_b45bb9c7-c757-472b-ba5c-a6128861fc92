/* coworking-spaces theme for discoverize.com portal - created 10.3.17 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/modern-homepage";

$cb-bg-hp: rgba(255, 255, 255, 1);
$content-block-border: $neutral; 
 
/* end import */

/* graphics */

body {
    background: url(/themes/coworking/styles/img/bg-pattern.png) repeat 0 0;
}
body {
    font-family: trebuchet ms, sans-serif;
        $new-primary-color: #404040 ,
}

/* end graphics */

/* global */ 

h1,
h2,
h3,
h4,
h5,
h6 {
    @include primary-font;
    color: #444;
}

.button-light-on-white {
    color: $txt-on-primary;
}

.blog-overview-page,
.blogpost-page {
    border: solid 1px $content-block-border;
}

#messages {
    position: absolute;
    z-index: 2;
    top: -30px;
    width: 100%;
    .message {
        box-sizing: border-box;
    }
}
.premium-block-inside .pbi2, .mini-gallery .current-entries {
    .ec-name {
        font-weight: 400;
    }
}

/* end global */

/* layout */

.zone-messages {
    @extend .center;
}

.additional-block {
    left: 50%;
    margin-left: 500px;
    top: -12px;
}

.hl-space-all {
    padding: 40px;
}
.hl-center {
    text-align: center;
}

.blog-overview,
.entry-page {
    margin-top: 1em;
}

/* end layout */

/* header */

.header-top {
    background: $grey;
    position: relative;
    z-index: 1;
}
.tagline {
    @include primary-font;
    float: left;
    font-size: 1em;
    text-transform: uppercase;
    font-weight: bold;
    padding: 0;
    margin: 0;
}
.header-top .mini-nav {
    a,
    a:hover {
        color: #fff;
        padding-top: 3px;
        padding-bottom: 2px;
    }
    span {
        color: #eee;
    }
}

.header-main {
    background: none;
    position: relative;
}
.header-main-inside {
    .logo {
        padding: 12px 0;
    }
}


.nav {
    @include media(">page-width") {
        max-height: 45px;
        align-self: center;
    }
}

.nav {
    a {
        text-transform: uppercase;
    }
    @include media(">mobile-ui"){
        & > li > a {
            border-left: solid 2px $primary;
        }
    }
}

.sticky-header  {
    .header-main-inside .logo {
        padding: 0;
    }
    .nav {
        @include media(">mobile-ui") {
            max-height: initial;
        }
    }
}

// @include media(">page-width") {
//     body .nav {
//         background: none;

//         .nav-searchbox {
//             align-self: stretch;
//             background: rgba(255,255,255,.9);
//             position: relative;

//             &:before {
//                 background: inherit;
//                 transform: translateX(100%);
//                 position: absolute;
//                 right: 0;
//                 top: 0;
//                 height: 100%;
//                 width: 4px;
//                 content: '';
//             }
//         }
//     }

//     .sticky-header {
//         .nav .nav-searchbox {
//             background: none;
//         }
//     }
// }



// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 11px 9px 8px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav a {
//     font-size: 1em;
//     border-left: solid 2px $primary;
//     text-transform: uppercase;
//     padding: 11px 9px 8px;
//     background: rgba(255, 255, 255, 0.9);
//     color: #444;
//     font-weight: bold;
// }
// .nav a:hover,
// .nav .current {
//     background: #fff;
//     color: #000;
// }
// .nav li {
//     background: none;
//     border-radius: 0;
// }

// .nav-level-2 {
//     border: none;
//     background: none;
// }
// .nav-level-2 a:hover {
//     background: #fff;
// }
// .nav-level-2 a {
//     background: rgba(255, 255, 255, 0.9);
//     border: none;
//     border-left: solid 2px $primary;
// }

.global-search {
    @include global-search(210px);
    padding: 5px 0px 2px;
    color: #ccc;
    .sb1 {
        border-color: #ccc;
    }
}

/* end header */

/* homepage */

@mixin mini-search-right {
    form {
        padding: 8px 24px;
    }
    .ms10 {
        padding-top: 0;
    }
}

.mini-search {
    @include mini-search-right;
    @include background-responsive(
        "img/bg-coworking-600.jpg",
        $image-height-mobile: 730,
        $image-height-desktop: 650
    );
    min-height: 650px;
    z-index: 0;
    margin-top: -105px;
    position: relative;
    background: no-repeat 0 0;
    background-size: cover;
    margin-bottom: 0;
    padding-top: 5em;
    &:before {
        background: None;
    }

    .mc32{
        width: 100%;
    }
    
}
.ms10 {
    @include primary-font;
    @include text-background-offset(rgba(0, 0, 0, 0.6));
    font-weight: bold;
    text-transform: uppercase;
    color: $primary;
}

.ms12 {
    span,
    label {
        color: #333;
    }
}
.ms261 {
    color: #333;
}

.welcome-inside,
.explained-inside,
.benefit-inside {
    border-left: solid 1px $content-block-border;
    border-right: solid 1px $content-block-border;
}
.benefit-inside {
    border-bottom: solid 1px $content-block-border;
}
.mini-gallery,
.content-block-5-inside,
.blog-teaser-list {
    border: solid 1px $content-block-border;
}
.premium-block {
    border-top: solid 1px $content-block-border;
    border-bottom: solid 1px $content-block-border;
}

/* end homepage */

/* search page */

.sf61-1, .epf61-1 { 
    @extend .icon-door-open;
}
.sf61-2, .epf61-2 { 
    @extend .icon-info-sign;
}
.sf61-3, .epf61-3 { 
    @extend .icon-tag;
}
.sf61-4, .epf61-4 { 
    @extend .icon-lamp;
}
.sf61-8, .epf61-8 { 
    @extend .icon-map-marker;
}

.ls10 a {
    color: #444;
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-lamp;
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-lamp;
        &:before {
            left: 1px;
            top: 4px;
            font-size: 22px;
        }
    }
}

.map-marker-premium span.map-marker-icon:before {
    top: 6px;
    font-size: 27px;
}

.sp5,
.blg200,
.ep21{
    a{
        color: #333;
    }
}

.sp110 {
    @include linear-gradient(
        transparentize(lighten($primary, 50), 0.9),
        transparentize(lighten($primary, 45), 0),
        0%,
        66%
    );
}
.sp19 {
    background: #fff;
    color: $primary;
    border: solid 1px $primary;
    &:hover {
        background: $primary;
        color: #fff;
    }
}

.sfu-list {
    label,
    .checkbox {
        font-weight: 300;
    }
}

/* end search page */

/* entry page */

.ep1 {
    border-bottom: solid #fff 2em;
}
.epi12 {
    margin-top: 11px;
    margin-bottom: 13px;
}
.epr22 {
    @include media(">page-width") {
        margin-top: 19px;
        margin-bottom: 10px;
    }
}

/* end entry page */

/* footer */

.design-guide {
    border-bottom: solid 1px #ccc;
}

blockquote {
    font-weight: 400;
    color: #777;
}

footer {
    border: none;
    width: 100%;
    background: #303441;
}

.logo-footer {
    padding: 16px;
    display: block;
    &:hover {
        background: rgba(255, 255, 255, 0.1);
    }
}
.footer-top {
    @include footer-top(".icon-lamp", $primary-darker, $txt-on-primary);
    h3,
    h4 {
        @include text-background-offset(rgba(255, 255, 255, 0.3));
    }
}

.foot-logo {
    display: block;
    .mini-tagline {
        width: auto;
        text-align: center;
    }
}
.foot-nav a {
    color: #fff;
    &:hover {
        color: #333;
    }
}

/* end footer */

/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: $page-width) {
    .header-top-inside {
        background: $grey;
    }
    .header-main-inside {
        background: none;
    }
    .logo {
        padding: 0;
    }
    .nav {
        padding-top: 0;
        a.nav01,
        a.nav10,
        a.nav21,
        a.nav31,
        a.nav41 {
            padding: 13px 16px 10px 16px;
        }
        .current {
            background: lighten($primary, 5%);
        }
        i.nav-subnav-collapser {
            border-color: darken($primary, 30);
        }
        i.nav-subnav-collapser span {
            padding: 4px 4px 3px;
        }
    }
    .mini-search {
        margin-top: 0;
    }
    .message {
        width: 100%;
    }
    #messages {
        top: -3px;
    }
}

/* below 768 */
@media only screen and (max-width: 767px) {
    .welcome,
    .explained,
    .benefit {
        h2 {
            font-size: 1.4em;
        }
    }
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
.ms10 {
    font-size: 1.3em;
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.2em;
    }
    .footer {
        @include rounded(25%, 25%, 0, 0);
    }
}
/* below 320 */
@media only screen and (max-width: 319px) {
    .ms10 {
        font-size: 1.1em;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";