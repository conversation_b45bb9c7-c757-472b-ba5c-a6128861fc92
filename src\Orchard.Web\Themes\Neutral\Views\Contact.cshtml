﻿@using Teamaton.Discoverize.ContentBlocks
@using Teamaton.Discoverize.Internationalization.Resources
@model Teamaton.Discoverize.ViewModels.ContactViewModel
@{
    // add the class "content-page" to the <div class="zone zone-content"> that wraps this content
    Layout.Content.Classes.Add("content-page");
    var title = TR(RS.ContactForm, "title: (portal name) contact", "{0} Kontakt", Html.PortalName());
    Html.Title(title.ToHtmlString());
    Script.Require("jQuery_Validation_Extensions").AtFoot();
    Script.Require("jQuery_Loading").AtFoot();
}
<h1 class="pc1" id="ContactTitle">
    @TR(RS.ContactForm, "contact headline", "Kontakt")
</h1>
<p class="pc10">
    @TR(RS.ContactForm,
        "description",
        "Wenn Sie Fragen, Anregungen oder Kritik haben, benutzen Sie einfach das Formular, um uns eine Nachricht zu senden.")
</p>
@using (Html.BeginFormAntiForgeryPost(new { @class = "form-horizontal", id = "contact-form" })) {
    <fieldset>
        <legend>
            @TR(RS.ContactForm, "sub headline: send message", "Nachricht an die Redaktion senden")
        </legend>
        @Html.ValidationSummary()
        <div class="control-group myfunnyclass">
            <label class="control-label" for="@Html.IdFor(m => m.Url)">
                @TR(RS.ContactForm, "url field", "Url")
            </label>
            <div class="controls">
                @Html.TextBoxFor(model => model.Url)
                @Html.ValidationMessageFor(model => model.Url)
            </div>
        </div>
        <div class="control-group myfunnyclass">
            <label class="control-label" for="@Html.IdFor(m => m.Ticks)">@T("Ticks")</label>
            <div class="controls">
                @Html.TextBoxFor(model => model.Ticks)
                @Html.ValidationMessageFor(model => model.Ticks)
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="@Html.IdFor(m => m.FirstName)">
                @TR(RS.ContactForm, "first name field", "Vorname") *
            </label>
            <div class="controls">
                @Html.TextBoxFor(model => model.FirstName, new { required = "true", autofocus = "" })
                @Html.ValidationMessageFor(model => model.FirstName)
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="@Html.IdFor(m => m.Surname)">
                @TR(RS.ContactForm, "last name field", "Name") *
            </label>
            <div class="controls">
                @Html.TextBoxFor(model => model.Surname, new { required = "true" })
                @Html.ValidationMessageFor(model => model.Surname)
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="@Html.IdFor(m => m.Email)">
                @TR(RS.ContactForm, "email address field", "E-Mail-Adresse") *
            </label>
            <div class="controls">
                @Html.TextBoxFor(model => model.Email, new { data_rule_email = "true", data_rule_email_local_part_ascii = "true", required = "true", type = "email" })
                @Html.ValidationMessageFor(model => model.Email)
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="@Html.IdFor(m => m.Subject)">
                @TR(RS.ContactForm, "subject field", "Betreff")
            </label>
            <div class="controls">
                @Html.TextBoxFor(model => model.Subject, new { maxlength = "200", @class = "input-xxlarge" })
                @Html.ValidationMessageFor(model => model.Subject)
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" for="@Html.IdFor(m => m.Message)">
                @TR(RS.ContactForm, "message field", "Nachricht") *
            </label>
            <div class="controls">
                @Html.TextAreaFor(model => model.Message, 3, 80, new { @class = "input-xxlarge", required = "true" })
                @Html.ValidationMessageFor(model => model.Message)
            </div>
        </div>
        @if (Html.ReCaptchaIsActiveOnContactForm()) {
            @Html.Partial("_SpamProtection", "contactRecaptcha")
        }
        <div class="form-actions">
            @Html.Partial("_AcceptPrivacyPolicyText")
            <button id="SendMessage" class="pc25" type="submit">
                @TR(RS.ContactForm, "send message button", "Nachricht absenden")
            </button>
            <button id="clear" type="button" class="pc26">
                @RsCommon.Cancel(TR)
            </button>
        </div>
    </fieldset>
}

@if (Model.ShowFeedbackAfterSave) {
    @Html.Partial("_FeedbackAfterUserAction", ContentBlockService.ContactPortalFeedbackBlockName)
}

@using (Script.Foot()) {
    <script>
        $(function () {
            $('#clear').on('click', function () {
                $(this).closest('form')[0].reset();
            });
            $('#contact-form').validateExtended({
                ignore: ':hidden:not(.jq-force-validation)',
                submitHandler: function (form) {
                    const showLoadingTn = function () {
                        $(form).loadingTn({
                            fullscreen: false,
                            text: '@TR(RS.ContactForm, "notification during sending message", "Ihre Anfrage wird übertragen...").ForJavaScript(Html)',
                            loadingLogo: '@Url.HashedInTheme("/styles/img/logo.png")'
                        });
                    };

                    // workaround to show the logo image in ff when not cached before
                    setTimeout(showLoadingTn, 1);

                    form.submit();
                }
            });
        })
    </script>
}