@charset "UTF-8";

:root {
color-scheme: light only;
}

@font-face {
font-display:swap;font-family:'Montserrat';font-style:normal;font-weight:400;src:url("./fonts/montserrat-v29-latin-regular-(hash632261611).eot");src:url("./fonts/montserrat-v29-latin-regular-(hash632261611).eot?#iefix") format("embedded-opentype"),url("./fonts/montserrat-v29-latin-regular-(hash1954107478).woff2") format("woff2"),url("./fonts/montserrat-v29-latin-regular-(hash555651752).woff") format("woff"),url("./fonts/montserrat-v29-latin-regular-(hash2887078505).ttf") format("truetype"),url("./fonts/montserrat-v29-latin-regular-(hash3831599322).svg#Montserrat") format("svg");
}

@font-face {
font-display:swap;font-family:'Montserrat';font-style:normal;font-weight:600;src:url("./fonts/montserrat-v29-latin-600-(hash2695199230).eot");src:url("./fonts/montserrat-v29-latin-600-(hash2695199230).eot?#iefix") format("embedded-opentype"),url("./fonts/montserrat-v29-latin-600-(hash1547956080).woff2") format("woff2"),url("./fonts/montserrat-v29-latin-600-(hash3850140381).woff") format("woff"),url("./fonts/montserrat-v29-latin-600-(hash3537764713).ttf") format("truetype"),url("./fonts/montserrat-v29-latin-600-(hash2656875384).svg#Montserrat") format("svg");
}

@media all and (-ms-high-contrast:none) {
*::-ms-backdrop,.zoom-for-ie,a.dzrw0 {
zoom: 1\0;
}
}

_:-ms-fullscreen,:root .zoom-for-ie,:root a.dzrw0 {
zoom: 1;
}

a.dzrw0,a.dzrw0:hover .dzrw0 span,.dzrw0 img {
margin: 0;
padding: 0;
border: none;
background: none;
text-decoration: none!important;
line-height: 1;
font-size: 16px;
}

a.dzrw0 {
background-color: #f6f6f6;
background-image: -webkit-gradient(linear,left top, left bottom,from(#fbfbfb),to(#f6f6f6));
background-image: linear-gradient(to bottom,#fbfbfb 0%,#f6f6f6 100%);
border-radius: 8px 8px 8px 8px;
-moz-border-radius: 8px 8px 8px 8px;
-webkit-border-top-left-radius: 8px;
-webkit-border-top-right-radius: 8px;
-webkit-border-bottom-right-radius: 8px;
-webkit-border-bottom-left-radius: 8px;
-moz-background-clip: padding-box;
-webkit-background-clip: padding-box;
background-clip: padding-box;
-webkit-box-sizing: border-box;
box-sizing: border-box;
border: solid 2px #8e7a50;
display: block;
max-width: 250px;
min-width: 150px;
width: 100%;
padding: 10px;
position: relative;
font-family: "Montserrat";
font-weight: 400;
color: #555!important;
}

a.dzrw0:before,a.dzrw0:after {
content: "";
display: table;
}

a.dzrw0:after {
clear: both;
}

a.dzrw0:hover {
background: #fff;
border: solid 2px #6d5e3e;
color: #555;
}

a.dzrw0:hover .dzrw11 {
border-color: #6d5e3e;
background: #8e7a50;
background: linear-gradient(135deg,#b3a078 0%,#8e7a50 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b3a078', endColorstr='#8e7a50',GradientType=1 );
}

a.dzrw0:hover .dzrw5 {
color: #8e7a50;
}

a.dzrw0 img {
max-width: 100%;
}

.dzrw0 .dzrw1 {
width: 100%;
margin: 0 0 0.4em 0;
display: inline-block;
}

.dzrw0 .dzrw11 {
border-radius: 8px 8px 8px 8px;
-moz-border-radius: 8px 8px 8px 8px;
-webkit-border-top-left-radius: 8px;
-webkit-border-top-right-radius: 8px;
-webkit-border-bottom-right-radius: 8px;
-webkit-border-bottom-left-radius: 8px;
-moz-background-clip: padding-box;
-webkit-background-clip: padding-box;
background-clip: padding-box;
background: #8e7a50;
background: linear-gradient(135deg,#aa9467 0%,#8e7a50 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#aa9467', endColorstr='#8e7a50',GradientType=1 );
border: solid 2px #7e6c47;
padding-top: calc(20% - 4px);
display: block;
width: calc(20% - 4px);
float: left;
display: block;
position: relative;
}

.dzrw0 .dzrw12 {
-webkit-box-sizing: border-box;
box-sizing: border-box;
position: absolute;
width: 100%;
top: 0;
left: 0;
padding: 4px;
fill: #fff;
opacity: 1!important;
}

.dzrw0 .dzrw13 {
-webkit-box-sizing: border-box;
box-sizing: border-box;
padding-left: 8px;
width: 80%;
float: left;
display: block;
line-height: 1.3;
word-wrap: break-word;
font-size: 1em;
font-weight: 100;
text-align: center;
margin-top: 2px;
}

.dzrw0 .dzrw13:hover {
color: #8e7a50;
}

.dzrw0 .dzrw2 {
position: relative;
margin: 8px 0 2px 0;
max-width: 100%;
}

.dzrw0 .dzrw21 {
-webkit-filter: drop-shadow(1px 2px 1px rgba(0,0,0,0.4));
filter: drop-shadow(1px 2px 1px rgba(0,0,0,0.4));
}

.dzrw0 .dzrw21,.dzrw0 .dzrw23 {
max-height: 60px;
width: 100%;
fill: #ccc;
opacity: 1!important;
}

.dzrw0 .dzrw22 {
overflow: hidden;
position: absolute;
top: 0;
left: 0;
}

.dzrw0 .dzrw23 {
fill: #8e7a50;
}

.dzrw0 .dzrw23:hover {
fill: #9e8859;
}

.dzrw0 .dzrw31 {
margin: 0.25em 0 0.3em 0;
font-size: 2em;
}

.dzrw0 .dzrw31 strong {
font-size: 1.5em;
position: relative;
top: 0.09em;
font-weight: bold;
}

.dzrw0 .dzrw31,.dzrw0 .dzrw32 {
display: block;
text-align: center;
}

.dzrw0 .dzrw31:hover,.dzrw0 .dzrw32:hover {
color: #8e7a50;
}

.dzrw0 .dzrw32 {
margin-bottom: 0.5em;
font-size: 1.15em;
}

.dzrw0 .dzrw5 {
text-align: center;
display: block;
font-size: 0.8em;
padding: 16px 0;
}

.dzrw0 .dzrw51 {
fill: #8e7a50;
max-height: 20px;
width: 16px;
margin: 0 5px 0 0;
position: relative;
top: 3px;
opacity: 1!important;
}

.dzrw0 .dzrw2 {
display: inline-block;
}