/* buttons - default theme for discoverize.com portal - created 16.10.12 - author: andrej telle - discoverize.com */

/*csslint unqualified-attributes:false, outline-none:false*/



@mixin btn-effect-shine(
    $use-for-event: 'hover', 
    $color: $primary,
    $shine-slide-speed: .5s,
    $shine-bg-color: rgba(255, 255, 255, 0.28)
) {
    position: relative;
    overflow: hidden;

    &:#{$use-for-event}{
        outline: 0;
        &:after {
            animation: btn-effect-shine-slide $shine-slide-speed ease-out;
        }
    }

    &:after{
        content: "";
        overflow: none;
        background-color: $shine-bg-color;
        position: absolute;
        top: -200%;
        left: 140%;
        height: 600%;
        width: 150%;
        transform: rotate(338deg);
        z-index:2;
    }

    @keyframes btn-effect-shine-slide {
        0% {
         left: 110%
        }
        100% {
          left: -200%
        }
    }
}

@mixin btn-effect-gradient(
    $use-for-event: 'hover', 
    $speed: .3s,
    $gradient-color: $primary,
    
) {
    position: relative;
    overflow: hidden;
    transition: $speed;
    transform: scale(1);
    background-size: 300% 100%;
    background-image: linear-gradient(to right, 
      darken($gradient-color, 0%), 
      darken($gradient-color, 5%), 
      darken($gradient-color, 0%), 
      darken($gradient-color, 10%)
    );
  
    &:#{$use-for-event}{
        outline: 0;
        background-position: 100% 0;
    }
}

@mixin btn-effect-scale(
    $use-for-event: 'hover', 
    $speed: .5s,
    $starting-scale: scale(1),
    $middle-scale: scale(1.1),
    $ending-scale: scale(1),
    
) {
    position: relative;
    overflow: hidden;
    transition: $speed;
    transform: scale(1);
  
    &:#{$use-for-event}{
        outline: 0;
        animation: btn-effect-scale-intensive-animation $speed ease-out;
    }

    @keyframes btn-effect-scale-intensive-animation {
        0% {
          transform: $starting-scale;
        }
        50% {
          transform: $middle-scale;
        }
        100% {
          transform: $ending-scale;
        }
    }
}

@mixin btn-effect-ghost(
    $use-for-event: 'hover', 
    $color: $primary,
    $lighten-by: 40,
    $border-width: 5px,
    $ghost-bg-color: inherit,
    $ghost-timing: 0.5s,
    $border-radius: inherit,
) {
    position: relative;
    transform-style: preserve-3d;

    //start click effect
    &:after {
        content: '';
        background-color: $ghost-bg-color;
        border-radius: $border-radius;
        display: block;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        transform: scale(0.001, 0.001);
        transform: translateZ(-1px);
        opacity: 0;
    }
    &:#{$use-for-event}{
        outline: 0;
        &:after {
            transition: $ghost-timing;
            animation: btn-effect-ghost-behind-appear forwards $ghost-timing ease-out;
            transform: scale(1.1, 1.2);
        }
    }

    @keyframes btn-effect-ghost-behind-appear {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
}

@mixin btn-effect-outline-modern(
    $outline-width: 2px,
    $outline-offset: 1px
) {
    &:focus {
        outline-style: solid;
        outline: revert;
        outline-offset: $outline-offset;
        outline-width: $outline-width;
    }
}

@mixin btn-effect-border-modern(
    $use-for-event: 'focus',
    $border-color: $primary
) {
    &:#{$use-for-event} {
        outline: none;
        box-shadow: 0px 0px 0px 5px lighten($border-color, 40);
    }
}

$button-primary-color: $primary !default;
$button-secondary-color: $secondary !default;
$button-size: 1em !default;

$button-primary-border-color: darken($primary,5) darken($primary,10) darken($primary,10) darken($primary,5) !default;
$button-secondary-border-color: darken($secondary,3) darken($secondary,6) darken($secondary,6) darken($secondary,3) !default;

@mixin button(
    $color: $button-primary-color,
    $text-color: $txt-on-primary,
    $txt-shadow: $txt-shadow-on-primary,
    $border-color: $button-primary-border-color,
    $size: $button-size,
    $padding: 0.6em 0.6em 0.4em,
    $button-options: $button-options
) {
    @if map-get($button-options, "rounded-corners") {
        @include rounded(map-get($button-options, "rounded-corners-value")...);
    }
    @if map-get($button-options, "rounded-corners-circle") {
        @include rounded(100px, 100px, 100px, 100px); 
    }
    @if map-get($button-options, "drop-shadow") {
        @include shadow;
    }
    @if map-get($button-options, "gradient-background") {
        @include linear-gradient(lighten($color, 5), darken($color, 5));

        &:hover{
            @include linear-gradient($color, darken($color, 15));
            color: $text-color;
        }
    }
    @include primary-font;
    @include border-box;
    @include btn-effect-outline-modern;
    font-size: $size;
    *font-size: $size; /*IE 6/7 - To reduce IE's oversized button text*/
    *overflow: visible; /*IE 6/7 - Because of IE's overly large left/right padding on buttons */
    padding: $padding;

    color: $text-color; /* rgba not supported (IE 8) */
    *color: $text-color; /* IE 6 & 7 */
    background-color: $color;
    &:hover {
        background-color: darken($color, 10);
    }
    @include btn-effect-border-modern(
        $use-for-event: 'focus',
        $border-color: $color
    );
    @if map-get($button-options, "border") {
        border-width: 1px;
        border-style: solid;
        border-color: $border-color;
        // *border: 0;
        // border: 1px solid darken($border-color, 7);
        // border-right-color: darken($border-color, 15);
        // border-bottom-color: darken($border-color, 30); 
    } @else {
        border-width: 0px;
        &:hover {
            border-width: 0px;
        }
    }

    display: inline-block;

    text-align: center;
    text-decoration: none;
    @if map-get($button-options, "text-shadow") {
        text-shadow: 1px 1px 1px $txt-shadow, 0 0 1px $txt-shadow;
    }
    vertical-align: middle;

    /* Transitions on hover - see hover rules */
    @if map-get($button-options, "shadow") {
        -webkit-transition: 0.1s linear -webkit-box-shadow;
        -moz-transition: 0.1s linear -moz-box-shadow;
        -ms-transition: 0.1s linear box-shadow;
        -o-transition: 0.1s linear box-shadow;
        transition: 0.1s linear box-shadow;

        /* highlight on top, darklight on bottom */
        -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
            0 1px 2px rgba(0, 0, 0, 0.05);
        -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
            0 1px 2px rgba(0, 0, 0, 0.05);
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
            0 1px 2px rgba(0, 0, 0, 0.05);

        &:active {
            box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.15) inset,
                0 0 6px rgba(0, 0, 0, 0.2) inset;
        }
    }
}

/* Firefox: Get rid of the inner focus border */
/* .button-new::-moz-focus-inner{
    padding: 0;
    border: 0;
} */

/* button groups */

@mixin button-group-element(
    $color: $primary,
    $text-color: $txt-on-primary,
    $text-shadow: $txt-shadow-on-primary,
    $size: 1em
) {
    @include button($color, $text-color, $text-shadow, $size);
    @include rounded(0 0 0 0);
    float: left;
    display: block;
    padding: 4px 5px 3px 4px;
    font-weight: normal;
    border: solid 1px darken($color, 10);
    border-left: solid 1px lighten($color, 10);

    &:first-child {
        @extend %rounded-l;
        border-left-color: darken($color, 10);
    }
    &:last-of-type {
        @extend %rounded-r;
    }
}

%button-group-element-primary {
    @include button-group-element(
        $primary,
        $txt-on-primary,
        $txt-shadow-on-primary,
        1em
    );
}
%button-group-element-secondary {
    @include button-group-element(
        $secondary,
        $txt-on-secondary,
        $txt-shadow-on-secondary,
        1em
    );
}
%button-group-element-neutral {
    @include button-group-element($neutral, $link-color, none, 1em);
}

/* end button groups */

.button-primary,
.primary-action {
    @include button;
    margin-right: 4px;
}
.button-circle {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
}
.button-secondary {
    @include button($button-secondary-color, $txt-on-secondary, $txt-shadow-on-secondary, $button-secondary-border-color);
}
.button-tertiary {
    @include button($tertiary, $txt-on-tertiary, $txt-shadow-on-tertiary);
}
.button-neutral {
    @include button($btn-neutral-bg, $txt-on-neutral, $txt-shadow-on-neutral);
}
.button-warning {
    @include button($negative, $txt-on-negative, $txt-shadow-on-negative);
}

.button-light {
    @if map-get($button-options, "rounded-corners") {
        @include rounded(map-get($button-options, "rounded-corners-value")...);
    }
    background: $txt-bg;
    color: $link-color;
    text-align: center;
    padding: 10px 12px 8px;
    line-height: 1.6;
    border: solid 1px $border-button-light;
    display: inline-block;
    &:hover{
        background: $primary;
        color: $txt-on-primary;
        border: solid 1px darken($primary, 6);
    }

    @include btn-effect-border-modern(
        $use-for-event: 'focus',
        $border-color: $txt-bg
    );
}

.button-light-white {
    @extend .button-light;
    background: #fff;
}

.button-light-on-white {
    @extend .button-light;
    background: $neutral-light;
}

.button-dark-grey {
    @extend .button-light;
    background: darken($neutral, 40%);
    border: solid 1px darken($neutral, 50%);
    color: #fff;
    text-shadow: none;
}

.secondary-action {
    @extend .button-neutral;
    font-size: 0.8em;
    margin-left: 0.5em;
    padding: 0.4em 1em 0.3em;
    position: relative;
    top: 1px;
}

.button-l,
.button--l {
    font-size: 1.5em;
}
.button-m {
    font-size: 1.2em;
}
.button-ms {
    padding: 0.3em 0.45em 0.2em 0.45em;
}
.button-s {
    font-size: 0.85em;
    padding: 0.3em 0.45em 0.2em 0.45em;
}
@mixin button-s {
    font-size: 0.85em;
    padding: 0.3em 0.45em 0.2em 0.45em;
}
.button-xs {
    font-size: 0.8em;
    padding: 0.1em 0.2em 0.08em 0.2em;
}



.button-compact {
    padding: 0.2em 0.4em;
}

.button-icon-only {
    padding: 0.2em 0.1em 0.1em 0.4em;
    font-size: 1em;
}

.button-link {
    background: none;
    border: none;
    padding: 0 !important;
    cursor: pointer;
    color: $primary;
}
.button-link:hover {
    background: $primary;
    color: $txt-on-primary;
}

// display button as a link
@mixin button-as-link {
    background: none;
    border: none;
    cursor: pointer;
    color: $primary;
    padding: 0;
    text-align: left;
    &:hover {
        background: $primary;
        color: $txt-on-primary;
    }
    &:focus {
        outline-style: solid;
        outline: revert;
        outline-offset: 1px;
        outline-width: 2px;
    }
}

@mixin button-as-link--link-color {
    background: none;
    border: none;
    cursor: pointer;
    color: $link-color;
    padding: 0;
    text-align: left;

    &:hover {
        background: $link-color;
        color: $txt-on-link;
    }

    &:focus {
        outline-style: solid;
        outline: revert;
        outline-offset: 1px;
        outline-width: 2px;
    }
}

@mixin button-as-link-no-colors {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    text-align: left;
}

@mixin link-remove {
    @extend .icon-remove-sign;
    @include rounded(0.3em, 15px, 15px, 0.3em);
    font-size: 1em;
    color: darken($neutral-dark, 20);
    line-height: 1.7;
    cursor: hover;
    float: left;
    border: solid 1px $neutral-dark;
    padding: 1px 32px 1px 8px;
    background: $neutral-white;
    position: relative;

    &:before {
        font-size: 2.2em;
        line-height: 0;
        position: absolute;
        top: 13px;
        right: -8px;
        color: $neutral-dark;
    }
    &:hover {
        background: darken($neutral-white, 0.1);
        color: darken($negative, 20);
        border-color: darken($negative, 10);
        &:before {
            color: darken($negative, 10);
        }
    }
    &:focus {
        outline-style: solid;
        outline: revert;
        outline-offset: 1px;
        outline-width: 2px;
    }
}

@mixin button-remove {
    @extend .button-warning;
    @extend .icon-remove;
    &:before {
        font-size: 1.2em;
        line-height: 0;
        position: relative;
        top: 2px;
    }
}

.button[disabled],
.button-disabled,
.button-disabled:hover,
.button-disabled:active {
    @include button;
    background: $neutral !important;
    background-image: none;
    border: solid 1px #bbb;
    color: #111 !important;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=40);
    -khtml-opacity: 0.4;
    -moz-opacity: 0.4;
    opacity: 0.4;
    cursor: not-allowed;
    box-shadow: none;
    text-shadow: none;
}

@mixin button-hover {
    @include media("any-hover") {
        &:hover {
            @content;
        }
    }
    @include media("ie-11") {
        &:hover {
            @content;
        }
    }
    @include media("no-hover") {
        &:active {
            @content;
        }
    }
}

@mixin square-btn(
    $icon: ".icon-maximize",
    $padding: 10px,
    $-font-size: 1.5em,
    $background-color: $transparent-bg-dark,
    $border-radius: 10px,
    $x-position: right,
    $x-position-value: 10px,
    $y-position: bottom,
    $y-position-value: 10px,
    $position: absolute,
    $opacity: 1,
    
    $on-hover-font-size: 1.3em,
    // Before
    $before-position: initial,
    $before-x-position: right,
    $before-y-position: bottom,
    $before-x-position-value: initial,
    $before-y-position-value: initial,   
) {
    @extend #{$icon} !optional;
    display: block;
    position: $position;
    color: $cmg-neutral;
    transition: .3s;
    z-index: 10;
    #{$y-position}: $y-position-value;
    #{$x-position}: $x-position-value;
    opacity: $opacity;

    &:hover,
    &:active{
        background: transparent;
        color: $cmg-neutral;
        font-size: $on-hover-font-size;
    }

    &:focus {
        outline-style: solid;
        outline: revert;
        outline-offset: 1px;
        outline-width: 2px;
    }

    &:before {
        margin-right: 0;
        background-color: $background-color;
        border-radius: $border-radius;
        padding: $padding;
        position: $before-position;
        #{$before-x-position}: $before-x-position-value;
        #{$before-y-position}: $before-y-position-value;
    }
}

@mixin button-cut-off($offset: 12px) {
    clip-path: polygon(
        /* top left 1*/
        0% $offset,
        /* top left 2 */
        $offset 0%,
        /* top right */ 
        100% 0, 
        /* bottom right 1 */
        100% calc(100% - #{$offset}), 
        /* bottom right 2 */
        calc(100% - #{$offset}) 100%,
        /* bottom left */ 
        0 100%);
}

@mixin button--over-image {
    @include primary-font;
    padding: 5px 8px;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.5);
    color: rgba(255, 255, 255, 0.9);
}

.share-btn--twitter{
    @extend .icon-twitter;
}

.share-btn--facebook{
    @extend .icon-facebook-light;
}

.share-btn--whatsapp{
    @extend .icon-whatsapp;
}

.share-btn--twitter{
    @extend .icon-twitter;
}

.share-btn--email{
    @extend .icon-envelope-alt;
}

.share-btn--copy{
    @extend .icon-copy;
}
 
.share-btn--pinterest{
    @extend .icon-pinterest !optional;
}

.share-btn--telegram{
    @extend .icon-telegram !optional;
}

.share-btns-open {
    @include primary-font;
    @include rounded(1000px, 1000px, 1000px, 1000px);
    font-size: 1em;
    padding: 6px 8px 5px 8px;
    background: $neutral-light;
    border: solid 1px $neutral-darker;
}

.button-double {
    display: flex;
}

.button-double-left {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: darken($primary,4) solid 1px;
    margin-right: 0;
}
.button-double-right {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-left: 0;  
    border-left: lighten($primary,5) solid 1px;
}

@mixin button-select-pill {
    padding: 6px 10px;
    display: inline-block;
    font-size: 1em;
    position: relative;
    border-radius: $button-select-pill-rounded-corners;
    border: solid 1px $button-select-pill-border;
    background: $button-select-pill-bg;
    margin: 0 6px;
    color: $button-select-pill-color;
    &:hover {
        border-color: $ep-nav-active-border;
        background: $ep-nav-active-bg;
        color: $ep-nav-active-color; 
        cursor: pointer;
    }
}
@mixin button-select-pill--active {
    border-color: $ep-nav-active-border; 
    background: $ep-nav-active-bg;
    color: $ep-nav-active-color;
    font-weight: bold;
}

button {
    line-height: 1.6;
    @include button-as-link;
    @include primary-font;
}
