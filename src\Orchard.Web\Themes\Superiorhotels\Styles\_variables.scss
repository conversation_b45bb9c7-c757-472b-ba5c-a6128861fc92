/* variables for pistenhotels - moved 23.11.2016 - by andrej telle - discoverize.com */

/* variables */
$page-width: 1161px;
$mobile-ui: $page-width; 
$mini-search-theme-color: rgba(0, 0, 0, 0);

$button-options: (
    "border": false,
    "rounded-corners": true,
    "rounded-corners-value": 5px 5px 5px 5px,
    "gradient-background": false,
    "text-shadow": false,
    "shadow": false,
);

/* colors */
$primary: #8e7a50;
$primary-darker: darken($primary, 5%);
$primary-lighter: #b09265;
$txt-on-primary: #fff;
$txt-shadow-on-primary: rgba(0, 0, 0, 0.45);

$secondary: #191919;
$secondary-darker: darken($secondary, 5%);
$secondary-lighter: lighten($secondary, 5%);
$txt-on-secondary: #fff;
$txt-shadow-on-secondary: rgba(0, 0, 0, 0.75);

$quaternary: #e63959;
$txt-on-quaternary: #fff;

$link-color: $primary-lighter;
$sticky-nav-link-color: $txt-on-primary;

$grey: #111212;
$bg-area: #e5f0fb;
$bg-properties: #c8e0f5;

$nav-link-padding: 12px;

$nav-txt-color: #fff;

$flat-nav-bg: $secondary;
$flat-nav-txt: $txt-on-secondary;

$premium-color-search-result-bg: darken($primary,5);

$txt-highlight: $primary-lighter;

$link-list-wrapper-bg: transparent;



/* end colors */
/* typography */
$footer-bg: $secondary;
$footer-links-color: $txt-on-secondary;

$footer-top-bg-color: $txt-on-primary;

$footer-custom-1-color: $txt-on-primary;

$footer-custom-2-color: $txt-on-primary;

/* Import Google Web Fonts*/
/* montserrat-regular - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 400;
    src: url('./fonts/montserrat-v29-latin-regular.eot'); /* IE9 Compat Modes */
    src: url('./fonts/montserrat-v29-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('./fonts/montserrat-v29-latin-regular.woff2') format('woff2'), /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
         url('./fonts/montserrat-v29-latin-regular.woff') format('woff'), /* Chrome 5+, Firefox 3.6+, IE 9+, Safari 5.1+, iOS 5+ */
         url('./fonts/montserrat-v29-latin-regular.ttf') format('truetype'), /* Chrome 4+, Firefox 3.5+, IE 9+, Safari 3.1+, iOS 4.2+, Android Browser 2.2+ */
         url('./fonts/montserrat-v29-latin-regular.svg#Montserrat') format('svg'); /* Legacy iOS */
  }
  /* montserrat-600 - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 600;
    src: url('./fonts/montserrat-v29-latin-600.eot'); /* IE9 Compat Modes */
    src: url('./fonts/montserrat-v29-latin-600.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('./fonts/montserrat-v29-latin-600.woff2') format('woff2'), /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
         url('./fonts/montserrat-v29-latin-600.woff') format('woff'), /* Chrome 5+, Firefox 3.6+, IE 9+, Safari 5.1+, iOS 5+ */
         url('./fonts/montserrat-v29-latin-600.ttf') format('truetype'), /* Chrome 4+, Firefox 3.5+, IE 9+, Safari 3.1+, iOS 4.2+, Android Browser 2.2+ */
         url('./fonts/montserrat-v29-latin-600.svg#Montserrat') format('svg'); /* Legacy iOS */
  }

@mixin primary-font {
    font-family: "Montserrat";
    font-weight: 400;
}
@mixin secondary-font {
    font-family: "Montserrat";
    font-weight: 400;
}
/* end typography */

/* end variables */
