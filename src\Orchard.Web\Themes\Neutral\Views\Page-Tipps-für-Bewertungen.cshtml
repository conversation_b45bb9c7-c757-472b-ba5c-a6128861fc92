﻿@using Teamaton.Discoverize.ContentBlocks
@using Teamaton.Discoverize.Internationalization.Resources
@using Teamaton.Discoverize.Utility
@{
    const string resourceIdPrefix = "tips for ratings page";
    if (Html.ContentBlockHasContent("Tipps für Bewertungen")) {
        Html.Title($"{Html.PortalName()} {Html.ContentBlockTitle("Tipps für Bewertungen")}");
    }
    else {
        Html.Title($"{Html.EntrySingular()} {TR(RS.Rating(), $"{resourceIdPrefix}:::title", "Tipps für Bewertungen")}");
    }

    var id = Request.Params["id"];
    string ratingPageLink;
    if (id == null) {
        var urlPartForPrimaryKeyword = !string.IsNullOrEmpty(Html.PrimaryKeywordDefault()) ? "/" + Html.PrimaryKeywordDefault().Slugify() : "";
        ratingPageLink = WorkContext.CurrentSite.BaseUrl + urlPartForPrimaryKeyword + "/Name-Ihres-Eintrags-hier#Bewertung-abgeben";
    }
    else {
        ratingPageLink = WorkContext.CurrentSite.BaseUrl + Url.DetailPageRate(Convert.ToInt32(id));
    }

    
}
<div class="rating-tips">
    <h1 class="rt1">
        @TR(RS.Rating(), 
            $"{resourceIdPrefix}:::01:::tips to collect ratings on (portal name)", 
            "Tipps um Bewertungen auf {0} zu sammeln:", Html.PortalName())
    </h1>
    <h2 class="rt12">
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::02:::on this way you get more ratings",
        "So erhalten Sie mehr Bewertungen!")
    </h2>
    <p>
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::03:::experience has shown...(entry type singular name)",
        "Die Erfahrung zeigt, dass folgende Punkte am wichtigsten sind, um mehr Bewertungen von Kunden zu erhalten. Dazu bieten wir einen Link, der direkt zur Bewertungsmöglichkeit Ihres {0}-Eintrags führt:",
        Html.EntrySingular())
    </p>

    <div class="rt2" id="rt2">
        @if (id == null)
        {
            <h4>
                @TR(RS.Rating(),
                $"{resourceIdPrefix}:::04:::how to create the rating link",
                "So erstellen Sie den Bewertungslink:")
            </h4>
            <ol>
                <li>
                    @TR(RS.Rating(),
                    $"{resourceIdPrefix}:::05:::go to the detail page",
                    "Gehen Sie auf Ihre Eintragsseite (z.B. via der Suchseite)")
                </li>
                <li>
                    @TR(RS.Rating(),
                    $"{resourceIdPrefix}:::06:::copy the url",
                    "Kopieren Sie die url in der Adressleiste")
                </li>
                <li>
                    @TR(RS.Rating(),
                    $"{resourceIdPrefix}:::07:::add submit rating hash to url",
                    "Hängen Sie folgenden Teil hinten an die url: #Bewertung-abgeben")
                </li>
            </ol>
            <p>
                @TR(RS.Rating(),
                $"{resourceIdPrefix}:::08::the link should look...",
                "Der Link sollte am Ende so aussehen:")
            </p>
            <p>@ratingPageLink</p>
        }
        else
        {
            <textarea class="rt22 js-rt22">@ratingPageLink</textarea>
            <a class="rt21" href="@ratingPageLink">@ratingPageLink</a>
            <a href="#rt2" class="rt23 js-rt23">
                @TR(RS.Rating(),
                $"{resourceIdPrefix}:::09:::copy link to clipboard",
                "Link in die Ablage kopieren")
            </a>
        }
    </div>

    <h2>
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::10:::distribute link via the following chanels",
        "Verteilen Sie diesen Link über folgende Kanäle:")
    </h2>

    <h3 class="rt31">
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::11:::send email",
        "1. Senden Sie eine E-Mail an Ihre Kunden")
    </h3>
    <p>
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::12:::send email explanation",
        @"Die einfachste und wirksamste Methode ist der <strong>regelmäßige Versand einer E-Mail</strong> mit der Einladung zu einer Bewertung an alle Kunden.
        Falls Sie <strong>Newsletter</strong> (z.B. an Stammkunden) versenden, können Sie auch darin den Link zu ihrer Bewertungsseite einbauen.
        Verknüpfen Sie dies mit einem <strong>Gewinnspiel</strong>, um Ihre Fans und Follower noch stärker zu animieren.")
    </p>

    <h3 class="rt41">
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::13:::links on homepage",
        "2. Verlinken Sie auf Ihrer Webseite")
    </h3>
    <p>
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::14:::links on homepage explantion (logo url) (portal name)",
        @"Sie können obigen Link direkt auf die Startseite oder an anderer passender Stelle in Ihrer <strong>Homepage</strong> einbauen.
        Dazu dürfen Sie auch das <strong>Logo (<a class=""rt51"" href=""{0}"" target=""_blank"">Verlinkung zum Logo</a>)</strong> von {1} verwenden.
        Damit gelangen die Besucher Ihrer Webseite mit nur einem einzigen Klick zu Ihrem Bewertungsformular.",
        Url.HashedInTheme("/styles/img/logo.png"), Html.PortalName())
    </p>

    <h3 class="rt61">
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::15:::social media",
        "3. Nutzen Sie Ihre Social Media Auftritte")
    </h3>
    <p>
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::16:::social media explanation",
        @"Falls Ihr Unternehmen auf <strong>Facebook, Instagram</strong> etc. aktiv ist, dann können Sie den Bewertungslink auch dort angeben.
        Sie können ihn in den allgemeinen Informationen hinterlegen oder einen Beitrag zum Thema Bewertungen erstellen.
        Verknüpfen Sie dies mit einem Gewinnspiel, um Ihre Fans und Follower noch stärker zu animieren, eine Bewertung abzugeben.
        Eine Aufforderung, nur positive Bewertungen abzugeben, wäre natürlich nicht in Ordnung.")
    </p>
    <p class="rt7">
        @TR(RS.Rating(),
        $"{resourceIdPrefix}:::17:::if you have questions (contact page url)",
        "Bei Fragen kontaktieren Sie uns. <a href=\"{0}\" class=\"rt71\" target=\"_blank\">Zum Kontaktformular</a>." ,Url.ContactPage())
    </p>

    @Html.ContentBlock("Tipps für Bewertungen")

</div>

<script>
    //  copy link to clipboard - via http://stackoverflow.com/questions/400212/how-do-i-copy-to-the-clipboard-in-javascript
    var copyTextareaBtn = document.querySelector('.js-rt23');

    copyTextareaBtn.addEventListener('click', function (event) {
        var copyTextarea = document.querySelector('.js-rt22');
        copyTextarea.select();

        try {
            var successful = document.execCommand('copy');
            var msg = successful ? 'successful' : 'unsuccessful';
            console.log('Copying text command was ' + msg);
        } catch (err) {
            console.log('Oops, unable to copy');
        }
    });
</script>