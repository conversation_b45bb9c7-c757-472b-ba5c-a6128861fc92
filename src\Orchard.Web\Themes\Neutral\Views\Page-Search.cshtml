﻿@using Discoverize.Common.StructuredData
@using Orchard.Mvc.Extensions
@using Teamaton.Discoverize.Areas.Models
@using Teamaton.Discoverize.Configuration.Portal
@using Teamaton.Discoverize.ContentBlocks
@using Teamaton.Discoverize.Feature.FeatureToggle
@using Teamaton.Discoverize.Internationalization.Resources
@using Teamaton.Discoverize.Tracking.Events
@using Teamaton.Search.Models
@using Teamaton.Search.ViewModels
@model SearchViewModel
@{
    Script.Require("jquery.tn.app.search").AtFoot();
    Script.Require("area-search-suggestions").AtFoot();
    Script.Require("compare-entries").AtFoot();
    Script.Require("favorites").AtFoot();
    Script.Require("sticky-second-header").AtFoot();

    // add the class "search-page" to the <div class="zone zone-content"> that wraps this content
    Layout.Content.Classes.Add("search-page");
    SetMeta("dz_page_type", "search-page"); // add meta-Tag for highfivve recognition for ad spaces

    var eventRenderer = WorkContext.Resolve<IEventRenderer>();
    var isPostalCode = Model.MapViewModel?.FilteredArea is PostalCode;
    var portalConfiguration = WorkContext.Resolve<IPortalConfiguration>();
    var portalInformation = portalConfiguration.PortalSettings.PortalInformation;
    var seoSettings = portalConfiguration.SeoSettings;
    var threshold = seoSettings.EntryCompletenessIndexingThreshold;
    var shouldAddNoIndex =
        isPostalCode && !portalInformation.EnablePostCodeLinks ||
        Model.MapViewModel?.FilteredArea is Town && portalInformation.DisableIndexingOfTownsWithoutEntries && Model.TotalInsideAreaResultCount == 0 ||
        Model.HasUrlPropertyFilter && Model.MapViewModel?.FilteredArea != null && Html.IsFeatureEnabled<IReducedSitemapFeature>() ||
        seoSettings.OnlyIndexSearchPagesWithCompleteEntries && threshold > 0 && Model.SearchResults.All(r => r.Completeness < threshold);
}
@using (Script.Head()) {
    //for facebook sharing
    <meta property="og:image" content="@Url.MakeAbsolute(Url.HashedInTheme("/styles/img/logo-manage.png"))?w=200&amp;h=200&amp;scale=both&amp;mode=pad">
    if (shouldAddNoIndex) {
        SetMeta("robots", "noindex, nofollow");
    }
}

@{
    var pageLayout = Model.SearchPageLayout;
}

@Html.Partial("_SearchEntryTypesLinks", Model)

<div id="search-main" class="search-main jq-sticky-filters-parent @("list-and-map-view".If(pageLayout.IsBoth())) @("list-view".If(pageLayout.IsList())) @("map-view".If(pageLayout.IsMap())) @("adblock-on".If(Html.ContentBlockHasContent("Adblock-Searchpage")))">


    @*the layout of this header (h1, slider, crumbtrail, suggestions) must also be modified in SearchResults.cshtml for asynchronous requests (TextForHeadline) - test by applying filter *@
    @*crumbtrail and adblock*@
    <div class="sp00000">
        @if (Html.ContentBlockHasContent(ContentBlockService.SearchPageAdblockBeforeResultsDesktop)) {
            <div class="sp-adblock-top cb-desktop-only">
                @Html.ContentBlock(ContentBlockService.SearchPageAdblockBeforeResultsDesktop)
            </div>
        }
        <div id="crumbtrail" class="sp00001">
            @Display.AreaCrumbtrail(AreaCrumbtrailViewModel: Model.AreaCrumbtrailViewModel)
        </div>
        @Display._SearchNearMeLink(CssClass: "sp02 jq-search-nearby-searchpage",
                                   Text: TR(RS.SearchPage(),
                                            "SearchNearMeLink top::::::(entry type plural) near-by",
                                            "{0} in meiner Nähe", Model.EntryType.EntryTypeNamePlural),
                                   EntryTypeId: Model.EntryTypeId)
    </div>
    @*title, slider, suggestions*@
    <div class="sph3 jq-search-page-top-box-wrapper">
        <div class="search-page-title-box jq-search-page-filters">
            <div id="SearchPageHeader" class="search-page-title">
                <h1 class="sp0">
                    @Html.Partial("_SearchPageHeadline", Model)
                </h1>
            </div>
            @Html.Partial("_TopFilterBar", Model)
        </div>
        <div class="search-page-slider jq-search-page-area-slider">
            @if (Model.IsSearchForAreaNearBy) {
                @Html.Partial("_AreaNearBySlider", Model)
            }
        </div>
        <div class="search-page-suggestions jq-search-page-town-suggestions">
            @if (isPostalCode) {
                @Html.Partial("_TownsInPostalCode", Model)
            }
            @if (Model.PotentialAreas?.Count > 0) {
                @Html.Partial("_TownsInFreetextArea", Model)
            }
        </div>
    </div>

    <section class="search-form">
        @Display.SearchForm(SearchViewModel: Model)
    </section>

    <!-- results -->
    @Display.SearchResults(SearchViewModel: Model)
    <!-- end results -->

    @if (!pageLayout.IsList()) {
        <!-- map -->
        <div id="search_map" class="sp3 jq-sticky @("map-full-width".If(pageLayout.IsMap()))">
            @Display.SearchMap(ViewModel: Model)
        </div>
    }
    <!-- end map -->

    @Html.Partial("_MobileSearchActionBar", Model)

    <div id="jq-app-sort" class="modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <button class="msr210 do-not-trigger-search" 
                        type="button" 
                        data-dismiss="modal" 
                        aria-label="@RsCommon.Close(TR)">
                    <span>
                        @RsCommon.Close(TR)
                    </span>
                </button>
                <h3 class="msr220">@TR(RS.SearchPage(), "order by", "Sortieren nach")</h3>
                <ul class="msr025">
                    <li>
                        <button class="msr026 do-not-trigger-search" data-value="@nameof(CustomSortOrderTypes.Score)" type="button">
                            @TR(RS.SearchPage(), "standard [sort order]", "Standard")
                        </button>
                        <button class="msr0261 trigger-tt-bottom-left do-not-trigger-search" type="button">
                            <span>
                                @TR(RS.SearchPage(), "standard [sort order] explained", "Standard Sortierung erklärt")
                            </span>
                        </button>
                        <div class="msr0262 tooltip">
                            @Html.Partial("_StandardSortOrderExplanation")
                        </div>
                    </li>
                    @if (Model.ShowEventForm) {
                        <li>
                            <button class="msr028 do-not-trigger-search" data-value="@nameof(CustomSortOrderTypes.EventStartDate)" type="button">
                                @TR(RS.SearchPage(), "event start date [sort order]", "Beginn des Events")
                            </button>
                        </li>
                    }
                    @if (!Html.IsRatingDisabled()) {
                        <li>
                            <button class="msr028 do-not-trigger-search" data-value="@nameof(CustomSortOrderTypes.NumberOfRatings)" type="button">
                                @TR(RS.SearchPage(), "number of ratings [sort order]", "Anzahl Bewertungen")
                            </button>
                        </li>
                        <li>
                            <button class="msr028 do-not-trigger-search" data-value="@nameof(CustomSortOrderTypes.AverageRating)" type="button">
                                @TR(RS.SearchPage(), "ratings [sort order]", "Bewertungen")
                            </button>
                        </li>
                    }
                    <li>
                        <button class="msr028 do-not-trigger-search" data-value="@nameof(CustomSortOrderTypes.Created)" type="button">
                            @TR(RS.SearchPage(), "latest entries first [sort order]", "Neueste Einträge zuerst")
                        </button>
                    </li>
                    <li>
                        <button class="msr028 do-not-trigger-search" data-value="@nameof(CustomSortOrderTypes.Distance)" type="button">
                            @TR(RS.SearchPage(), "distance from my position [sort order]", "Entfernung von meinem Standort")
                        </button>
                    </li>
                    @foreach (var additionalSortOrder in Model.AdditionalSortOrders) {
                        <li>
                            <button class="msr028 do-not-trigger-search"
                                    type="button"
                                    data-value="@nameof(CustomSortOrderTypes.NumberProperty)"
                                    data-field-name="@(additionalSortOrder.FieldName)">
                                @additionalSortOrder.DisplayText
                            </button>
                        </li>
                    }

                    @if (Html.Info().AreaNearByRadius > 0) {
                        var isNearByArea = Model.MapViewModel?.FilteredArea is INearByArea;
                        var areaDisplayName = isNearByArea
                            ? Model.MapViewModel?.FilteredArea?.GetShortDisplayName()
                            : "?";
                        <li>
                            <button class='@("sr-link--disabled".If(!isNearByArea)) msr026 do-not-trigger-search' type="button" data-value="@nameof(CustomSortOrderTypes.DistanceFromArea)">
                                @TR(RS.SearchPage(), "distance from (area) [sort order]", "Entfernung von {0}", areaDisplayName)
                            </button>
                            <button class="msr0261 trigger-tt-bottom-left do-not-trigger-search" type="button">
                                <span>
                                    @TR(RS.SearchPage(), "distance from area center link", "Entfernung vom Gebietsmittelpunkt")
                                </span>
                            </button>
                            <div class="msr0262 tooltip">
                                <h3>
                                    @TR(RS.SearchPage(), "distance from area center headline", "Entfernung vom Gebietsmittelpunkt")
                                </h3>
                                <p>
                                    @TR(RS.SearchPage(),
                                        "distance from area center explanation",
                                        "Für Orte, Ortsteile und Postleitzahlgebiete können die Ergebnisse hier nach Entfernung vom Mittelpunkt sortiert werden.")
                                </p>
                            </div>
                        </li>
                    }
                </ul>
            </div>
        </div>
    </div>

    @if (Html.ContentBlockHasContent(ContentBlockService.SearchPageAdblock)) {
        <div class="additional-block additional-block--search-page jq-sticky">
            @Html.ContentBlock(ContentBlockService.SearchPageAdblock)
        </div>
    }
</div>

@if (Html.ContentBlockHasContent(ContentBlockService.SearchPageAdblockAfterResults)) {
    <div class="sp-adslot-3">
        @Html.ContentBlock(ContentBlockService.SearchPageAdblockAfterResults)
    </div>
}

<section class="search-content-block" id="search_content_block">
    <div class="jq_content_block">
        @if (!string.IsNullOrEmpty(Model.ContentBlock)) {
            var content = Model.ContentBlock.EnforceContentBlockDefaults();
            <div class="sp8">@Html.Raw(content)</div>
        }
    </div>
</section>

<div id="blog-posts-target" class="sp-blog-posts">
    @Display.BlogPostLinks(SearchViewModel: Model)
</div>

@Html.Partial("PremiumBlock")

@Html.Partial("_AuthorBlock", Model.AuthorInfo)

<div class="search-area-property-links" id="search_results_area_links">
</div>

@Html.Partial("_FavoritesTracking")
@using (Script.Foot()) {
    <!--suppress JSUnusedLocalSymbols -->
    <script type="text/javascript">
        $(function() {
            const options = {
                open: '@pageLayout.ToString().ToLower()',
                layoutSel: '[name=SearchPageLayout]'
            };
            // defined in jquery.tn.app.search.js
            $.tn && $.tn.appButtonStateMachine && $.tn.appButtonStateMachine(options);

            // can probably be removed?
            $(document).on('click', 'a[href="#search_map"]', function(ev) {
                ev.preventDefault();
                $(document).scrollTop($('#search_map').offset().top);
            });

            $('.jq-search-nearby-searchpage').on("click", function() {
                @eventRenderer.RenderEvent(GoogleAnalyticsEvent.SearchNearby.SearchNearbySearchPage())
            });

            // columnizer
            $(".jq-columnize").columnizeLists();

            $('.additional-block').on("click", function(event) {
                // ReSharper disable once UnusedLocals - used in GA event
                const url = $(event.target).closest("a").attr('href');
                @eventRenderer.RenderEvent(GoogleAnalyticsEvent.Advertisement.SearchPageBannerClicked("url"))
            });

            $('.jq-back-to-top').addClass('on-search-page');
        });
    </script>
}

@if (Model.ShowStructuredItemList) {
    @Html.Partial("_StructuredDataItemList",
                  new StructuredDataItemList { Items = Model.SearchResults.Where(r => r.StructuredDataItem != null).Select(r => r.StructuredDataItem).ToList() })
}