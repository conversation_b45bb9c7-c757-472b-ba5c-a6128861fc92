﻿/* myrecruiter theme for discoverize.com portal - created 1.7.2020 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */
@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
$cb-bg-hp: rgba(255, 255, 255, 1);

/* end import */

/* overwrites */

/* graphics */

body {
    background: none;    
    @include primary-font;
}

/* end graphics */

/* global */

// .mini-search-inside {
    
// }

// .mini-search-entry-types-tabs ~ .mini-search-entry-type {
//     border:none;
// }
.zone-content.search-page {
    margin-top: 0;
}
.SEARCH-FULLWIDTH .search-page-title-box {
    @include linear-gradient(#f9f9f9, #fff);
}
// end hide entry types

/* end global */

/* layout */

.main-wrap{
    background: #fbfbfb;
}

/* end layout */

/* header */

.header {
    position: relative;
}
.header-top {
    background: #eee;
}

.header-main-inside, .header-top-inside {
    @include media(">page-width") {
        padding-left: 0;
        padding-right: 0;
    }
}
.tagline {
    @include primary-font;
    color: #333;
    float: left;
    font-size: 0.85em;    
}

.mini-nav {
    a,
    a:hover {
        color: $primary;
        border-right: none;
    }
    a:hover {
        color: $txt-on-primary; 
        background: $primary;
    }
    span {
        color: #333;
    }
    .mn11 {
        border-right: none;
    }
}
 
.logo {
    padding: 16px 0;
    margin-right: 12px;
    
}

// .nav {
//     @include border-box;
//     margin: 0;
//     position: relative;
//     z-index: 3;
// }
// .nav a {
//     @include primary-font;
//     color: $primary;
//     border: none !important;
//     background: none;
// }
// .nav .current {
//     background: $primary-darker;
//     color: $txt-on-primary;
// }

// .nav > li:hover > a {
//     background: $primary-darker; 
//     color: $txt-on-primary;
// }

// a.nav01 {
//     @extend .icon-home;
// }

// @include media(">=1161px") {
//     .nav {
//         align-self: stretch;
//         align-items: center;
//         display: flex;
//     }

//     .nav1,
//     .nav2,
//     .nav3,
//     .nav5,
//     .nav-section-1,
//     .nav-section-2,
//     .nav-section-3,
//     .nav-section-4,
//     .nav-section-5,
//     .nav-section-6 {
//         align-self: stretch;
//         align-items: stretch;
//         display: flex;

//         a {
//             display: flex;
//             align-items: center;

//             @include media("<page-width") {
//                 padding: 12px 8px;
//             }
//         }
//     }

//     .nav-level-2 {
//         top: 100%;
//     }
// }

// .nav-level-2 {
//     @include shadow-remove;
//     background: none;
//     border: none;
// }
// .nav-level-2 a {
//     background: transparentize($primary-darker, 0.1);
//     color: #fff;
// }
// .nav-level-2 a:hover {
//     background: $primary-darker;
//     color: $txt-on-primary;
// }

.global-search {
    @include global-search($width: 140px); 
    margin: 5px 5px 0;
    color: #ccc;
    .sticky-header & {
        margin-top: 0px;
    }
}

.sticky-header {
    .nav a {
        color: darken($primary,10);
    }
}

/* end header */

/* homepage */

.hero-img {
    background-position: center bottom;
}

// .full-page-mini-search-background {
//     background-color: #fff;
//     background-repeat: no-repeat;
//     background-size: cover;
//     background-image: url("img/hotel-gastronomie-immobilien-pachten-kaufen.jpg");
//     position: absolute;
//     left: 0;
//     right: 0;
//     bottom: 0;
//     z-index: -10;
//     height: 100%; 

//     &::before {
//         content: "";
//         background-image: linear-gradient( to bottom, rgba(#000, 0), transparent );
//         position: absolute;
//         width: 100%;
//         height: 100%;
//     }

//     video {
//         height: 100%;
//         width: 100%;
//         object-fit: cover;
//     } 
// }


.mini-search {
    &:before {
        @include radial-gradient(
            $start-color: rgba(0,0,0,0),
            $end-color:  rgba(0,0,0,0),
        )
    }
}

.tabs li.active a:hover {
    background: $primary;
    color: $txt-on-primary;
}
 
.ms10 {
    padding-top: 0;
    @include media("<=479px") {
        font-size: 1em; 
    } 
    @include media("<=380px") {
        font-size: 0.9em;
    }
}
.ms10,
.ms261,
.ms23 {
    color: #fff;
    @include text-background-offset(
        $text-shadow-color: #000,
    ) 
}
.ms23 {
    @include media(">page-width") {
        margin-top: 15px;
    }
}

.welcome {
    @include flex-order(2);

    .cb-cta[class*=" icon-"]:before {
        font-size: 7em;
    } 
    @include media("<=page-width") {
        padding-left: 0;
        padding-right: 0;
    }
}
.welcome-inside {
    padding-top: 32px;
    .cb-cards--2 {
        margin-top: 48px;
        .cb-card-visual {
            flex-grow: 1;
        }
    }
}
.mini-gallery {
    @include flex-order(3);
}
.explained {
    @include flex-order(4);
}
.home-page .premium-block {
    @include flex-order(5);
} 
.benefit {
    @include flex-order(6);
} 
.content-block-5 {
    @include flex-order(7);
} 

.content-block-5-inside {
    display: block;
    max-width: $page-width;
    @include media("<=380px") {
      padding: 0px;
    }
}

.cb51,
.cb52,
.cb53 {
    float: none;
    text-align: left;
    padding: 0;
    width: 100%;
    margin-bottom: 36px;
    padding-left:0;
    padding-right:0;
}
html
body
.mini-search {
    min-height: 60vh;
}

.benefit-standout {
    padding: 0;
}

.mini-gallery,
.welcome-inside,
.explained-inside,
.blog-teaser-list,
.content-block-5-inside,
.benefit-inside,
.premium-block-inside, 
.area-inside,
.properties-inside {
    background: none;
}

/* end homepage */

/* search page */

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-handshake !optional;
    font-size: 23px;
    color: #fff;

    &:before {
        display: none;
    }
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-handshake !optional;

    }
}

.map-marker span:before, .map-marker-static span:before{
    left: 0;
}

body
.map-marker-primary
.map-marker-icon{
    &:before {
        left: 0;
        top: 3px;
    }
}

.map-marker-static
.map-marker-icon{
    &:before{
        left: 0px;
        top: 3px;    
    }
}

.map-marker span:before, 
.map-marker-static span:before{
    font-size: 23px;
}

.map-marker-premium span.map-marker-icon:before {
    left: 2px;
    top: 5px;
}
.map-marker-premium.map-marker-primary span.map-marker-icon {
    &:before {
        left: -1px;
        top: 3px;
        font-size: 40px;
    }
}

// .sf61-1, .epf61-1 { 
//     @extend .icon-info-sign;
// }
// .sf61-2, .epf61-2 { 
//     @extend .icon-home;
// }
// .sf61-3, .epf61-3 { 
//     @extend .icon-cutlery;
// }
// .sf61-4, .epf61-4 { 
//     @extend .icon-hotel;
// }
// .sf61-5, .epf61-5 { 
//     @extend .icon-child;
// }
// .sf61-6, .epf61-6 { 
//     @extend .icon-bicycle;
// }
// .sf61-7, .epf61-7 { 
//     @extend .icon-map-marker;
// }

/* map marker icon font-size reapplied dut to IE */

// .map-marker span.map-marker-icon::before, .map-marker-static span.map-marker-icon::before {font-size: 28px}

/* end search page */

/* create entry page */

.ae1010 {
    @include hide-visually;
}

// blog

.blg3410 {
    display: none;
}

// end blog

/* end create entry page */

footer { 
    border: none;
    width: 100%;
    background: #ebebeb;
}

.footer-top {
    @include footer-top(".icon-hotel", #f1f1f1);
    padding-top: 5em;
    .footer-top12 {
        @extend .button-secondary, .icon-arrow-right;
        margin-top: 3em;
    }

    h3,
    h4{
        color: $txt-on-neutral;
    }

    &:before{
        font-size: 8em;
        top: 20px;
    }
}

.area{
    background: #f9f9f9;
}

.properties{
    background: #f5f5f5;
}

.footer {
    padding-top: 50px;
}
.footer .foot-nav {
    @extend .bx33; 
    a {
        color: $primary;
    }
    a:hover {
        color: $txt-on-primary;
        background: $primary;
    }
    h3 {
        color: $txt-on-neutral;
    }
}

.footer-logo {
    margin-bottom: 24px;
    display: block;
    .logo-graphic {
        max-width: 80%;
    }
}


/* end layout */
/* end overwrites */

/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: 960px) {
}
/* below 768 */
@media only screen and (max-width: 769px) {
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";
