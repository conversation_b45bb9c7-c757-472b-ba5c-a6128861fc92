﻿@using Teamaton.Discoverize.Internationalization.Resources
@{
    Script.Require("cmg-components").AtFoot();
}
<div class="cmg0 cb-full-width">
    <div class="cp-parent content-management-guide">
        <div class="cmg3 jq-sticky jq-sidenav-container">
            <ul class="cmg31 jq-sidenav">
                <li class="cmg311 jq-sidenav-link">
                    <a href="#cm-guide" class="cmg3111 jq-trigger-scroll">Html Vorlagen</a>
                </li>
                <li class="cmg311 cmg312 jq-sidenav-link">
                    <a href="#getting-started" class="cmg3111 jq-trigger-scroll">Starten</a>
                </li>
                <li class="cmg311 cmg313 jq-nav-dropdown jq-sidenav-link">
                    <a href="#layout" class="cmg3132 jq-section-link jq-trigger-scroll">Layout</a>
                    <button class="icon-chevron-thin-down cmg3131 jq-nav-dropdown-btn jq-sidenav-link" aria-label="Subnavigation aufklappen"></button>
                    <ul class="cmg3133">
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#editorial">Editorial</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#columns">Spalten</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#cardsWithoutGraphic">Elemente mit Farben</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#dividers">Trenner</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#collapsible">Auf- und Zuklappen</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#readmore">Weiterlesen</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#margins">Abstand links/rechts</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#full-width">Volle Breite</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#fullImagePlusBox">Bild + Box</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#mobile-desktop">Mobil / Desktop</a>
                        </li>
                    </ul>
                </li>
                <li class="cmg311 cmg313 jq-nav-dropdown jq-sidenav-link">
                    <a href="#cardsSection" class="cmg3132 jq-section-link jq-trigger-scroll">Boxen</a>
                    <button class="icon-chevron-thin-down cmg3131 jq-nav-dropdown-btn jq-sidenav-link"></button>
                    <ul class="cmg3133">
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#cardsWithImgs">mit Bild</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#cardsWithSimpleIcons">mit Icon</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#cardsWithIconsInCircles">mit Icon-Kreis</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#cardsWithGradient">mit Verlauf</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#cardsWithBorder">mit Rahmen</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#cardsTeam">Team/Kontakt</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#topics">Themen</a>
                        </li>
                    </ul>
                </li>
                <li class="cmg311 cmg313 jq-nav-dropdown jq-sidenav-link">
                    <a href="#images" class="cmg3132 jq-section-link jq-trigger-scroll">Bilder</a>
                    <button class="icon-chevron-thin-down cmg3131 jq-nav-dropdown-btn jq-sidenav-link"></button>
                    <ul class="cmg3133">
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#simpleImage">einfaches Bild</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#gallery">Galerie</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#shuffle">rotierende Galerie</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#imgDownload">für Download</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#hero">Hero Bild</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#banner">Banner</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#banner-slim">Banner slim</a>
                        </li>
                    </ul>
                </li>
                <li class="cmg311 cmg313 jq-nav-dropdown jq-sidenav-link">
                    <a href="#buttons" class="cmg3132 jq-section-link jq-trigger-scroll">Buttons</a>
                    <button class="icon-chevron-thin-down cmg3131 jq-nav-dropdown-btn jq-sidenav-link"></button>
                    <ul class="cmg3133">
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#btnPrimary">Primärer Button</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#btnSecondary">Sekundärer Button</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#btnsGroup">Button Gruppe</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#cta">Call to Action</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#btnsTags">Tag Liste</a>
                        </li>
                    </ul>
                </li>
                <li class="cmg311 jq-sidenav-link">
                    <a href="#icons" class="cmg3111 jq-trigger-scroll">Icons</a>
                </li>
                <li class="cmg311 jq-sidenav-link">
                    <a href="#counters" class="cmg3111 jq-trigger-scroll">Zähler</a>
                </li>
                <li class="cmg311 jq-sidenav-link">
                    <a href="#faq" class="cmg3111 jq-trigger-scroll">FAQ</a>
                </li>
                <li class="cmg311 jq-sidenav-link">
                    <a href="#lists" class="cmg3111 jq-trigger-scroll">Listen</a>
                </li>
                <li class="cmg311 jq-sidenav-link">
                    <a href="#sliders" class="cmg3111 jq-trigger-scroll">Slider</a>
                </li>
                <li class="cmg311 jq-sidenav-link">
                    <a href="#tables" class="cmg3111 jq-trigger-scroll">Tabellen</a>
                </li>
                <li class="cmg311 jq-sidenav-link">
                    <a href="#pricing-table" class="cmg3111 jq-trigger-scroll">Preis Übersicht</a>
                </li>
                <li class="cmg311 jq-sidenav-link">
                    <a href="#tabs" class="cmg3111 jq-trigger-scroll">Reiter (Tabs)</a>
                </li>

                <li class="cmg311 jq-sidenav-link">
                    <a href="#testimonials" class="cmg3111 jq-trigger-scroll">Kundenstimmen</a>
                </li>
                <li class="cmg311 cmg313 jq-nav-dropdown jq-sidenav-link">
                    <a href="#others" class="cmg3132 jq-section-link jq-trigger-scroll">Sonstige</a>
                    <button class="icon-chevron-thin-down cmg3131 jq-nav-dropdown-btn jq-sidenav-link"></button>
                    <ul class="cmg3133">
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#highlightedHeader">Überschrift Spezial</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#navigation">Navigation</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#typo">Typographie</a>
                        </li>
                        <li class="cmg31331 jq-sidenav-link">
                            <a class="cmg313311 jq-subsection-link jq-trigger-scroll" href="#logo">Logo - Bekannt aus</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
        <button class="mobile-nav-btn jq-toggle-mobileNav icon-bars">
            Starten
        </button>
        <div class="cp-content" style="padding-right: 10px;">
            <div class="cmg1 jq-cmg-section" id="cm-guide">
                <h1 class="cmg11 jq-scrollspy">Design und Layout Html Vorlagen</h1>
                <p class="cmg12">Mit der Liste der unten stehenden Html Vorlagen kann die Startseite (und alle weiteren statischen Content Blöcke) für Besucher optimiert werden. Durch das einfache Klicken auf den "kopieren" Button kann der Html Code im Portal Management Bereich auf der jeweiligen Seite eingefügt werden.</p>
                <div class="cmg14">
                    <a href="#layout" class="cmg13 cmg131 jq-trigger-scroll ">
                        <div class="cmg141">
                            <h4 class="cmg1412">Layout</h4>
                            <p class="cmg1413">Inhalte auf der Seite ausrichten</p>
                        </div>
                    </a>
                    <a href="#cardsSection" class="cmg13 cmg132 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Boxen</h4>
                            <p class="cmg1413">gruppierte Inhalte in verschiedenen Designs</p>
                        </div>
                    </a>
                    <a href="#images" class="cmg13 cmg133 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Bilder</h4>
                            <p class="cmg1413">verschiedene Möglichkeiten für Fotos</p>
                        </div>
                    </a>
                    <a href="#buttons" class="cmg13 cmg134 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Buttons</h4>
                            <p class="cmg1413">Fokus auf hervorgehobene Interaktionen</p>
                        </div>
                    </a>
                    <a href="#icons" class="cmg13 cmg135 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Icons</h4>
                            <p class="cmg1413">Inhalte mit visueller Darstellung verknüpfen</p>
                        </div>
                    </a>
                    <a href="#counters" class="cmg13 cmg136 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Zähler</h4>
                            <p class="cmg1413">animierte Darstellung von Metriken und Zahlen</p>
                        </div>
                    </a>
                    <a href="#faq" class="cmg13 cmg137 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">FAQ</h4>
                            <p class="cmg1413">Häufig gestellte Fragen</p>
                        </div>
                    </a>
                    <a href="#lists" class="cmg13 cmg1371 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Listen</h4>
                            <p class="cmg1413">Aufzählungen und Nummerierungen</p>
                        </div>
                    </a>
                    <a href="#sliders" class="cmg13 cmg138 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Slider</h4>
                            <p class="cmg1413">animierte Slide-Show mit Bildern oder Texten</p>
                        </div>
                    </a>
                    <a href="#tables" class="cmg13 cmg139 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Tabellen</h4>
                            <p class="cmg1413">Präsentation komplexerer Daten oder Inhalte</p>
                        </div>
                    </a>
                    <a href="#tabs" class="cmg13 cmg1391 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Reiter (Tabs)</h4>
                            <p class="cmg1413">viele Inhalte auf begrenzten Platz anzeigen</p>
                        </div>
                    </a>
                    <a href="#testimonials" class="cmg13 cmg1392 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Kundenstimmen</h4>
                            <p class="cmg1413">Testimonials und Personen-bezogene Inhalte</p>
                        </div>
                    </a>
                    <a href="#others" class="cmg13 cmg1393 jq-trigger-scroll">
                        <div class="cmg141">
                            <h4 class="cmg1412">Sonstige</h4>
                            <p class="cmg1413">weitere, verschiedene Design Vorlagen</p>
                        </div>
                    </a>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="getting-started">
                <h2 class="cmg11 jq-scrollspy">Mit den Html Vorlagen loslegen</h2>
                <div class="faq-wrapper">
                    <div itemscope itemtype="https://schema.org/FAQPage">
                        <div class="faq jq-faq" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                            <h4 class="faq-head jq-faq-head" itemprop="name">
                                <button class="faq-question">
                                    Warum sollte ich die Html Vorlagen verwenden?
                                </button>
                            </h4>
                            <div class="faq-body" itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                <div itemprop="text">
                                    <div class="group cb-centered-vertical">
                                        <div class="box-left-50">
                                            <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/satisfaction.jpeg">
                                        </div>
                                        <div class="box-left-50">
                                            <p>Die Html Vorlagen decken eine große Bandbreite von Designs, Layout und Funktionen ab, die sowohl für die Besucher als auch Suchmaschinen von Mehrwert sind.</p>
                                            <p>Folgende Vorteile:</p>
                                            <ul>
                                                <li>Inhalte lassen sich klar strukturieren und sind somit einfacher zu verstehen</li>
                                                <li>Unterschiedliche Layouts machen das Branchenportal attraktiver und damit verbessert sich die User Experience</li>
                                                <li>Suchmaschinen erhalten eine größere Vielfalt an Inhalten und die verwendeten Keywords verbessern das Ranking</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="faq jq-faq" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                            <h4 class="faq-head jq-faq-head" itemprop="name">
                                <button class="faq-question">
                                    Worauf sollte ich beim Einsatz der Vorlagen achten?
                                </button>
                            </h4>
                            <div class="faq-body" itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                <div itemprop="text">
                                    <div class="group cb-centered-vertical">
                                        <div class="box-left-50">
                                            <p>Grundsätzlich sollte der Fokus darauf liegen, Inhalte für Besucher attraktiv und leicht zugänglich zu präsentieren.</p>
                                            <p>Für Suchmaschinen und Usability kann man folgende Punkte beachten:</p>
                                            <ul>
                                                <li>Relevante Keywords in Überschriften, fetten Texten, Listen, Links etc. platzieren - der Einsatz von Keywords sollte aber natürlich bleiben</li>
                                                <li>Inhalte sollten klar strukturiert sein: Überschriften in der richtigen Hierarchie, Erläuterungstexte bei Bedarf, etc.</li>
                                                <li>Lieber auf zu viele Inhalte verzichten und dafür gut strukturierte, einfach lesbare Inhalte präsentieren. Weitere Inhalte können auf neuen Seiten präsentiert werden.</li>
                                            </ul>
                                        </div>
                                        <div class="box-left-50">
                                            <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/notebook.jpeg">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="faq jq-faq" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                            <h4 class="faq-head jq-faq-head" itemprop="name">
                                <button class="faq-question">
                                    Video Anleitung für den Einsatz der Vorlagen
                                </button>
                            </h4>
                            <div class="faq-body" itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                <div itemprop="text">
                                    <iframe width="560" height="315" src="https://youtube.com/embed/AxUWJQgTT78" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Components -->
            <div class="cmg1 jq-cmg-section" id="layout">
                <h2 class="cmg11 jq-scrollspy">Layout</h2>
                <p class="cmg12">Komponenten der Layoutkategorie können beim Festlegen des Rasters und der Ränder auf dem Branchenportal helfen.</p>
                <div class="cmg2 jq-component jq-cmg-subsection" id="editorial">
                    <h2 class="cmg21">Editorial</h2>
                    <div class="cmg23 jq-element">
                        <article class="cb-editorial" itemscope="" itemtype="https://schema.org/Article">
                            <header class="cb-editorial-10">
                                <h4 class="cb-editorial-1">Editorial</h4>
                                <h3 class="cb-editorial-2">
                                    Chefredakteurin Petra Lustig über das aktuelle Schwerpunktthema Wetter
                                </h3>
                                <h2 class="cb-editorial-3" itemprop="headline">
                                    Es endet der Herbst und mit ihm die Frage: Was fällt ab?
                                </h2>
                            </header>
                            <section class="cb-editorial-4 readmore readmore--mobile-only" itemprop="articleBody">
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>
                                <div class="cb-editorial-5">
                                    <img class="cb-editorial-51" src="/themes/neutral/styles/img/people/woman-120.jpg" alt="Bildbeschreibung hier einfügen!" loading="lazy" />
                                    <div class="cb-editorial-520">
                                        <p class="cb-editorial-52" itemscope="" itemprop="author">
                                            Ihre Petra Lustig
                                        </p>
                                        <p class="cb-editorial-53">Autorin und Humoristin</p>
                                    </div>
                                </div>
                            </section>

                        </article>
                    </div>
                    <div class="cmg24">
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="columns">
                    <h2 class="cmg21">2 Spalten</h2>
                    <div class="cmg23 jq-element">
                        <div class="group">
                            <div class="box-left-50">
                                Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
                            </div>
                            <div class="box-left-50">
                                Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection">
                    <h2 class="cmg21">2 Spalten 2/3 und 1/3</h2>
                    <div class="cmg23 jq-element">
                        <div class="group">
                            <div class="box-left-66">
                                Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
                            </div>
                            <div class="box-left-33">
                                Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">3 Spalten</h2>
                    <div class="cmg23 jq-element">
                        <div class="group">
                            <div class="box-left-33">
                                At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas.
                            </div>
                            <div class="box-left-33">
                                Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem.
                            </div>
                            <div class="box-left-33">
                                Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores.
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">4 Spalten</h2>
                    <div class="cmg23 jq-element">
                        <div class="group">
                            <div class="box-left-25">
                                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                            </div>
                            <div class="box-left-25">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et.
                            </div>
                            <div class="box-left-25">
                                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                            </div>
                            <div class="box-left-25">
                                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">5 Spalten</h2>
                    <div class="cmg23 jq-element">
                        <div class="group">
                            <div class="box-left-20">
                                Excepteur sint occaecat cupidatat non proident.
                            </div>
                            <div class="box-left-20">
                                Duis aute irure dolor in reprehenderit in voluptate.
                            </div>
                            <div class="box-left-20">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                            </div>
                            <div class="box-left-20">
                                Ut enim ad minim veniam, quis nostrud exercitation.
                            </div>
                            <div class="box-left-20">
                                Nor again is there anyone who loves or pursues.
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">2 Elemente pro Linie - 1/4 und 3/4</h2>
                    <div class="cmg23 jq-element">
                        <div class="group">
                            <div class="box-left-1-of-4">
                                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu.
                            </div>
                            <div class="box-left-3-of-4">
                                At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident qui blanditiis.
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">2 Elemente pro Linie - 3/4 und 1/4</h2>
                    <div class="cmg23 jq-element">
                        <div class="group">
                            <div class="box-left-3-of-4">
                                At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident qui blanditiis.
                            </div>
                            <div class="box-left-1-of-4">
                                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu.
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id='cardsWithoutGraphic'>
                    <h2 class="cmg21">Element mit primärer Farbe</h2>
                    <p class="cmg262">Elemente mit Farben heben sich von anderen Inhalten auf der Seite ab. Wir bieten mehrere Variationen an. Diese können auf nahezu jedes Element angewendet werden</p>
                    <div class="cmg23 jq-element">
                        <div class="cb-bg-color cb-bg--primary">
                            <h3 class="cb-bg-text">Überschrift</h3>
                            <p class="cb-bg-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                            <a class="button-secondary" href="link-hier-setzen">Weiterlesen</a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Element mit sekundärer Farbe</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-bg-color cb-bg--secondary">
                            <h3 class="cb-bg-text">Überschrift</h3>
                            <p class="cb-bg-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                            <a class="button-primary" href="link-hier-setzen">Weiterlesen</a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Element mit Verlauf von primärer zu sekundärer Farbe</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-bg-color cb-bg--primary-secondary">
                            <h3 class="cb-bg-text">Überschrift</h3>
                            <p class="cb-bg-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                            <a class="button-primary" href="link-hier-setzen">Weiterlesen</a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Element mit warmem Ocker-Ton</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-bg-color cb-bg--linen">
                            <h3 class="cb-bg-text">Überschrift</h3>
                            <p class="cb-bg-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                            <a class="button-secondary" href="link-hier-setzen">Weiterlesen</a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Element mit hell-grau Farbe</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-bg-color cb-bg--neutral">
                            <h3 class="cb-bg-text">Überschrift</h3>
                            <p class="cb-bg-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                            <a class="button-secondary" href="link-hier-setzen">Weiterlesen</a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Element mit dunkel-grau Farbe</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-bg-color cb-bg--grey">
                            <h3 class="cb-bg-text">Überschrift</h3>
                            <p class="cb-bg-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                            <a class="button-primary" href="link-hier-setzen">Weiterlesen</a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Element mit primär-Farbe und Content Overflow</h2>
                    <p class="info">Der Inhalt ist höher als der farbige Hintergrund.</p>
                    <div class="cmg23 jq-element">
                        <div class="cb-background-overflow cb-background-overflow--primary">
                            <div class="cb-background-overflow-inside center">
                                <div class="cb-bo-top">
                                    <div class="cb-bo-left">
                                        <h2 class="compact-text">Familienhotel-Inspirationen</h2>
                                        <p>Finde dein passenden Kinderhotel</p>
                                    </div>
                                    <a class="cb-bo-button" href="link-auf-die-entsprechende-seite">alle Kinderhotels finden</a>
                                </div>

                                <div class="cb-picture-list-4">
                                    <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                        <img src="/themes/neutral/Styles/img/New-images/winter1.jpg" alt="Bildbeschreibung hier einfügen!!!">
                                        <span class="cb-picture-content">
                                            <span>572 Einträge in</span>
                                            <strong>Nordrhein-Westfalen</strong>
                                        </span>
                                    </a>
                                    <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                        <img src="/themes/neutral/Styles/img/New-images/winter2.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                        <span class="cb-picture-content">
                                            <span>489 Einträge in</span>
                                            <strong>Niedersachsen</strong>
                                        </span>
                                    </a>
                                    <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                        <img src="/themes/neutral/Styles/img/New-images/winter8.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                        <span class="cb-picture-content">
                                            <span>377 Einträge in</span>
                                            <strong>Bayern</strong>
                                        </span>
                                    </a>
                                    <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                        <img src="/themes/neutral/Styles/img/New-images/winter4.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                        <span class="cb-picture-content">
                                            <span>321 Einträge in</span>
                                            <strong>Baden-Württemberg</strong>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Element mit sekundär-Farbe und Content Overflow</h2>
                    <p class="info">Der Inhalt ist höher als der farbige Hintergrund.</p>
                    <div class="cmg23 jq-element">
                        <div class="cb-background-overflow cb-background-overflow--secondary">
                            <div class="cb-background-overflow-inside center">
                                <div class="cb-bo-top">
                                    <div class="cb-bo-left">
                                        <h2 class="compact-text">Familienhotel-Inspirationen</h2>
                                        <p>Finde dein passenden Kinderhotel</p>
                                    </div>
                                    <a class="cb-bo-button">alle Kinderhotels finden</a>
                                </div>

                                <div class="cb-picture-list-4">
                                    <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                        <img src="/themes/neutral/Styles/img/New-images/winter1.jpg" alt="Bildbeschreibung hier einfügen!!!">
                                        <span class="cb-picture-content">
                                            <span>572 Einträge in</span>
                                            <strong>Nordrhein-Westfalen</strong>
                                        </span>
                                    </a>
                                    <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                        <img src="/themes/neutral/Styles/img/New-images/winter2.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                        <span class="cb-picture-content">
                                            <span>489 Einträge in</span>
                                            <strong>Niedersachsen</strong>
                                        </span>
                                    </a>
                                    <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                        <img src="/themes/neutral/Styles/img/New-images/winter8.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                        <span class="cb-picture-content">
                                            <span>377 Einträge in</span>
                                            <strong>Bayern</strong>
                                        </span>
                                    </a>
                                    <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                        <img src="/themes/neutral/Styles/img/New-images/winter4.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                        <span class="cb-picture-content">
                                            <span>321 Einträge in</span>
                                            <strong>Baden-Württemberg</strong>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="dividers">
                    <h2 class="cmg21">Trenner</h2>
                    <p class="cmg262">Dank Trennern können verschiedene Arten von Inhalten auf den Seiten durch eine horizontale Linie getrennen werden.</p>
                    <div class="cmg23 jq-element">
                        <hr class="hr-shadow" />
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Trenner mit Farbe</h2>
                    <div class="cmg23 jq-element">
                        <div style="width: 100%">
                            <hr class="cb-hr-colors" />
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Trenner - unsichtbar - für Abstände</h2>
                    <p class="cmg262">Diese unsichtbaren Trenner können für Abstände eingesetzt werden. Passen Sie die Zahl an: 16, 32, 64 sind möglich.</p>
                    <div class="cmg23 jq-element">
                        <div style="width: 100%">
                            <hr class="cb-hr-transparent cb-hr-32" />
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component" id="collapsible">
                    <h2 class="cmg21">Auf- und Zuklappen</h2>
                    <p class="cmg262">Für Inhalte die nur bei Bedarf angezeigt werden sollen. Der Benutzer kann Inhalte auf- und zuklappen. Vergleichbar zu FAQ nur ohne zusätzliches Schema.org Markup.</p>
                    <div class="cmg23 jq-element">
                        <div class='faq-wrapper'>
                            <div>
                                <div class="faq jq-faq">
                                    <h4 class="faq-head jq-faq-head">
                                        <button class="faq-question">
                                            Warum sollte ich die Html Vorlagen verwenden?
                                        </button>
                                    </h4>
                                    <div class="faq-body">
                                        <div>
                                            <!-- Your content hier -->
                                            <h4>Uberschrift hier</h4>
                                            <p>Lorem ipsum dolor ism amet</p>
                                            <p>Suchmaschinenoptimierung oder SEO (Englisch für "search engine optimization") beschäftigt sich mit der Darstellung und dem Ranking von Webseiten in Suchmaschinen.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="faq jq-faq">
                                    <h4 class="faq-head jq-faq-head">
                                        <button class="faq-question">
                                            Wie kann SEO verbessern?
                                        </button>
                                    </h4>
                                    <div class="faq-body">
                                        <div>
                                            <!-- Your content hier -->
                                            <h4>Uberschrift hier</h4>
                                            <ol>
                                                <li>User Experience der Besucher verbessern</li>
                                                <li>Interessante Inhalte veröffentlichen</li>
                                                <li>Seiten-Lade-Geschwindigkeit verbessern</li>
                                                <li>Backlinks sammeln</li>
                                                <li>Korrektes Html verwenden</li>
                                                <li>Bilder optimieren</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2" id="readmore">
                    <h2 class="cmg21">Weiterlesen</h2>
                    <p class="cmg262">Für Texte, die nur bei Bedarf vollständig angezeigt werden sollen. Der Benutzer kann Texte vollständig anzeigen und wieder einklappen.</p>
                    <div class="jq-component">
                        <h4>Für Desktop und Mobil</h4>
                        <div class="cmg23 cmg2300 jq-element">
                            <div class="readmore">
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam eget pulvinar tellus. Nullam luctus lorem odio. Ut neque orci, ullamcorper ut tellus vel, pharetra aliquet metus. Nunc eget sem ipsum. Nulla varius diam diam, ac vehicula velit tempor sed. Vestibulum ultrices eros sit amet diam dictum mollis. Ut pretium ac neque vel ornare. Duis in mollis neque, non imperdiet nibh. Etiam nisl arcu, efficitur sed massa ut, auctor consectetur quam. Mauris tortor nibh, ultricies sit amet porta nec, bibendum sit amet ex.
                                </p>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam eget pulvinar tellus. Nullam luctus lorem odio. Ut neque orci, ullamcorper ut tellus vel, pharetra aliquet metus. Nunc eget sem ipsum. Nulla varius diam diam, ac vehicula velit tempor sed. Vestibulum ultrices eros sit amet diam dictum mollis. Ut pretium ac neque vel ornare. Duis in mollis neque, non imperdiet nibh. Etiam nisl arcu, efficitur sed massa ut, auctor consectetur quam. Mauris tortor nibh, ultricies sit amet porta nec, bibendum sit amet ex.
                                </p>
                                <p>
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/Styles/img/New-images/friends5.jpeg" />
                                </p>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam eget pulvinar tellus. Nullam luctus lorem odio. Ut neque orci, ullamcorper ut tellus vel, pharetra aliquet metus. Nunc eget sem ipsum. Nulla varius diam diam, ac vehicula velit tempor sed. Vestibulum ultrices eros sit amet diam dictum mollis. Ut pretium ac neque vel ornare. Duis in mollis neque, non imperdiet nibh. Etiam nisl arcu, efficitur sed massa ut, auctor consectetur quam. Mauris tortor nibh, ultricies sit amet porta nec, bibendum sit amet ex.
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="jq-component">
                        <h4>Nur auf mobiler Ansicht einklappen, auf Desktop vollständig anzeigen</h4>
                        <p class="cmg262">Der Text wird nur in der mobilen Ansicht verkürzt dargestellt. Ebenso wird der "weiterlesen" Link nur in der mobilen Ansicht angezeigt. Zum Testen bitte in eine mobile Ansicht wechseln.</p>
                        <div class="cmg23 cmg2300 jq-element">
                            <div class="readmore readmore--mobile-only">
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam eget pulvinar tellus. Nullam luctus lorem odio. Ut neque orci, ullamcorper ut tellus vel, pharetra aliquet metus. Nunc eget sem ipsum. Nulla varius diam diam, ac vehicula velit tempor sed. Vestibulum ultrices eros sit amet diam dictum mollis. Ut pretium ac neque vel ornare. Duis in mollis neque, non imperdiet nibh. Etiam nisl arcu, efficitur sed massa ut, auctor consectetur quam. Mauris tortor nibh, ultricies sit amet porta nec, bibendum sit amet ex.

                                    Integer auctor eu augue id dictum. Phasellus congue libero vitae malesuada faucibus. Morbi porttitor tellus eget porttitor maximus. Integer eleifend feugiat metus. Mauris vulputate tincidunt nibh eu hendrerit. Mauris vel fermentum sapien. Duis non vestibulum velit, in efficitur odio. Vestibulum tempus, enim et mattis accumsan, enim nibh interdum orci, vel sollicitudin velit metus interdum nisi. Mauris massa urna, venenatis sed laoreet non, ullamcorper at purus. Ut neque justo, feugiat vitae semper et, aliquam bibendum est. Suspendisse non erat in tellus luctus ultricies a a purus. Aliquam erat volutpat. Maecenas turpis odio, fringilla ut scelerisque aliquam, fermentum non turpis. Vivamus sed dolor sed purus mollis ullamcorper eu a magna. Donec malesuada faucibus tellus ac tincidunt.

                                    Vestibulum lobortis vehicula lobortis. In laoreet semper sem, sit amet auctor quam tristique quis. Fusce sit amet imperdiet quam. Nullam tempus ipsum vitae eros blandit, eget pharetra mauris blandit. Phasellus enim enim, finibus eu pellentesque vitae, finibus a justo. Curabitur euismod at neque et fringilla. Duis posuere sed mauris vel mattis. Pellentesque tempus diam at magna malesuada, at mattis sapien cursus. Fusce ut erat ut dolor mattis mattis a nec ipsum.

                                    Donec rhoncus consequat felis nec finibus. Aliquam posuere lacinia nibh vitae sagittis. Sed ligula enim, dapibus eget orci vitae, consectetur luctus velit. Aliquam dui augue, venenatis sed tempus id, pulvinar ut enim. Sed lobortis erat sed erat lacinia mattis id at dolor. Proin ipsum odio, dapibus et porta at, commodo sit amet metus. Duis fringilla, ante egestas posuere luctus, purus mauris ullamcorper ligula, in pretium dolor nibh quis sapien.

                                    Nam et felis justo. Duis cursus, justo ut luctus condimentum, risus nulla lacinia lectus, ut dignissim nibh libero a felis. Etiam eu nulla eget dui posuere blandit quis fermentum purus. Morbi sed leo quis dui convallis placerat. Nullam ultricies erat vel sem maximus, eget elementum lorem sodales. Donec venenatis orci eu felis malesuada efficitur. Ut vel augue erat. Etiam tellus lorem, ultricies eu urna id, aliquet suscipit turpis. Vestibulum blandit magna lacus, in feugiat massa eleifend vitae. Vivamus posuere tortor in tincidunt aliquam. Cras molestie quam commodo nisl fringilla cursus. Vivamus mollis eget odio sit amet convallis. Etiam hendrerit lacinia odio, eu dictum justo faucibus non. Donec vel lacus lacus. Mauris lobortis velit vel neque iaculis, ac cursus nunc interdum. Praesent quis luctus turpis.
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="margins">
                    <h2 class="cmg21">Schmaler Block</h2>
                    <div class="cmg23 jq-element">
                        <div class="content-block-700">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                            </p>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Große Abstände</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-text-narrow">
                            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component component-full-width jq-cmg-subsection" id="full-width">
                    <h2 class="cmg21">Inhalt in voller Breite</h2>
                    <p class="cmg262">Diese Komponente kann verwendet werden, wenn man der Meinung ist, dass Ränder entfernt werden müssen.</p>
                    <div class="cmg23 jq-element">
                        <div class="cb-full-width">
                            <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur</p>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="fullImagePlusBox">
                    <h2 class="cmg21">Volles Bild und Box - Links/Rechts</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Um einen Link verwenden, hinterlegen sie die Url im <i>href</i> Attribut.</li>
                    </ul>
                    <div class="cmg23 jq-element component-cards-grid">
                        <div class="group cb-centered-vertical cards-grid-txt-right">
                            <div class="box-left-50">
                                <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/board1.jpg">
                            </div>
                            <div class="box-left-50">
                                <h3>Überschrift hier - Bild Rechts</h3>
                                <p>
                                    Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs.
                                    The passage is attributed to an unknown typesetter in the 15th century who is thought to have scrambled parts of
                                    Cicero"s De Finibus Bonorum et Malorum for use in a type specimen book.
                                </p>
                                <a class="button-primary" href="#link">Read more about us</a>
                            </div>
                        </div>
                        <div class="group cb-centered-vertical cards-grid-txt-left">
                            <div class="box-left-50">
                                <h3>Überschrift hier - Bild Links</h3>
                                <p>
                                    Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs.
                                    The passage is attributed to an unknown typesetter in the 15th century who is thought to have scrambled parts of
                                    Cicero"s De Finibus Bonorum et Malorum for use in a type specimen book.
                                </p>
                                <a class="button-primary" href="#link">Read more about us</a>
                            </div>
                            <div class="box-left-50">
                                <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/board2.jpg">
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>

                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Horizontale Boxen 50/50 Bild links</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="group cb-centered-vertical">
                            <div class="box-left-50">
                                <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/board1.jpg" />
                            </div>
                            <div class="box-left-50">
                                <h3>Überschrift hier - Bild links</h3>
                                <p>
                                    Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs.
                                    The passage is attributed to an unknown typesetter in the 15th century who is thought to have scrambled parts of
                                    Cicero"s De Finibus Bonorum et Malorum for use in a type specimen book.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>

                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Horizontale Boxen 50/50 Bild rechts</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="group cb-centered-vertical">
                            <div class="box-left-50">
                                <h3>Überschrift hier - Bild Rechts</h3>
                                <p>
                                    Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs.
                                    The passage is attributed to an unknown typesetter in the 15th century who is thought to have scrambled parts of
                                    Cicero"s De Finibus Bonorum et Malorum for use in a type specimen book.
                                </p>
                            </div>
                            <div class="box-left-50">
                                <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/board2.jpg" />
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Horizontale Boxen 50/50 Bild links - mit primärer Hintergrundfarbe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--1 cb-cards--stand-alone jq-fully-responsive-cards">
                            <div class="cb-card cb-card--primary cb-card--left">
                                <div class="cb-card-content">
                                    <h2>Hotel des <span class="text-primary">Monats</span></h2>
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Weiterlesen</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img src="/themes/neutral/Styles/img/New-images/card-white.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Horizontale Boxen 50/50 Bild rechts - mit sekundärer Hintergrundfarbe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--1 cb-cards--stand-alone jq-fully-responsive-cards">
                            <div class="cb-card cb-card--secondary cb-card--right">
                                <div class="cb-card-content">
                                    <h2>Hotel des <span class="text-secondary">Monats</span></h2>
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Weiterlesen</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img src="/themes/neutral/Styles/img/New-images/card-white.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Box mit Bild und weiteren Details</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-sides">
                            <div class="cb-sides-left">
                                <div class="cb-sides-img">
                                    <img alt="Bildbeschreibung hier einfügen!" class="cb-sides-img-1" src="/themes/neutral/styles/img/New-images/cup.jpeg">
                                </div>
                            </div>
                            <div class="cb-sides-right">
                                <div class="cb-sides-content">
                                    <div class="cb-sides-content-0">
                                        <h4 class="cb-sides-content-1">Unser location-tipp des monats</h4>
                                        <h3 class="cb-sides-content-2">Hochzeitlocation Hamburg Altona</h3>
                                        <p>
                                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
                                            tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
                                            veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                                            commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
                                            velit esse cillum dolore eu fugiat nulla pariatur.
                                        </p>
                                        <div class="cb-sides-content-3">
                                            <span class="cb-sides-content-31">250 Gaste, EEE</span>
                                            <a class="cb-sides-content-32" href="link-hier">mehr erfahren</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="mobile-desktop">
                    <h2 class="cmg21">Darstellung von Inhalten nur in Mobilen Endgeräten</h2>
                    <ul class="cmg263">
                        <li>Diese Inhalte werden auf Desktops ausgeblendet.</li>
                        <li>Um die Performance des Portals zu verbessern, können Inhalte (Werbung, Bilder, Videos) in versteckten Blöcken (s.o.) nur geladen werden, wenn diese auch angezeigt werden.</li>
                        <li>Ansonsten werden diese Inhalte im Hintergrund weiterhin geladen, obwohl sie dem Besucher nicht angezeigt werden.</li>
                        <li>Dafür können für iframes, externe Scripte und Bilder ein alternativer Code-Schnipsel hinterlegt werden.</li>
                        <li>Das "src"-Attribut muss durch ein "data-src"-Attribut ersetzt werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-mobile-only">
                            <p>Die Inhalte in diesem Element (class="cb-mobile-only") werden nur auf mobilen Endgeräten (Smartphones, Tablets bis zu 1160px Breite) angezeigt.</p>
                            <iframe data-src="/themes/neutral/Styles/img/New-images/friends5.jpeg"></iframe>
                            <p>Die Inhalte (img, script, iframe, etc.), bei denen das "src"-Attribute durch "data-src" ersetzt wurden, werden nur Mobil geladen. Auf Desktop werden sie auch im Hintergrund nicht geladen, um die Performance zu verbessern.</p>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection">
                    <h2 class="cmg21">Darstellung von Inhalten nur im Desktop</h2>
                    <ul class="cmg263">
                        <li>Diese Inhalte werden auf mobilen Endgeräten ausgeblendet.</li>
                        <li>Um die Performance des Portals zu verbessern, können Inhalte (Werbung, Bilder, Videos) in versteckten Blöcken (s.o.) nur geladen werden, wenn diese auch angezeigt werden.</li>
                        <li>Ansonsten werden diese Inhalte im Hintergrund weiterhin geladen, obwohl sie dem Besucher nicht angezeigt werden.</li>
                        <li>Dafür können für iframes, externe Scripte und Bilder ein alternativer Code-Schnipsel hinterlegt werden.</li>
                        <li>Das "src"-Attribut muss durch ein "data-src"-Attribut ersetzt werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-desktop-only">
                            <p>Die Inhalte in diesem Element (class="cb-desktop-only") werden nur im Desktop (Bildschirme mit über 1160px Breite) angezeigt.</p>
                            <iframe data-src="/themes/neutral/Styles/img/New-images/friends5.jpeg"></iframe>
                            <p>Die Inhalte (img, script, iframe, etc.), bei denen das "src"-Attribute durch "data-src" ersetzt wurden, werden nur im Desktop geladen. In Mobil werden sie auch im Hintergrund nicht geladen, um die Performance zu verbessern.</p>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="cardsSection">
                <h2 class="cmg11 jq-scrollspy">Boxen</h2>
                <p class="cmg12">Mit Boxen können Informationen zu einem bestimmten Thema in einem einzigen Block anzeigen werden. Dies ermöglicht es den Besuchern die Inhalte leichter zu überblicken und zu durchsuchen.</p>
                <div class="cmg2 jq-component jq-cmg-subsection" id="cardsWithImgs">
                    <h2 class="cmg21">Boxen mit Bild 1 pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst, indem das <i>src</i> Attribut im <i>img</i> überschrieben wird</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des  <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--1 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift</h3>
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Weiterlesen</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/ferns.jpeg" />
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="cardsWithImgs">
                    <h2 class="cmg21">Box, grauer Hintergrund mit Bild rechts</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst, indem das <i>src</i> Attribut im <i>img</i> überschrieben wird</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des  <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-neutral-box cb-neutral-box--centered">
                            <div class="group cb-centered-vertical">
                                <div class="box-left-50">
                                    <h2>Newsletter</h2>
                                    <p>
                                        Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs.
                                        The passage is attributed to an unknown typesetter in the 15th century who is thought to have scrambled parts of
                                        Cicero"s De Finibus Bonorum et Malorum for use in a type specimen book.
                                    </p>
                                    <a class="button-primary" href="#link">Read more about us</a>
                                </div>
                                <div class="box-left-50">
                                    <img alt="Beschreibung des Bildes" src="/themes/neutral/styles/img/New-images/board2.jpg">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>

                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Bild 2 pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--2 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--right">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p class="cb-card-content-text">Text für Beschreibung.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food1.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--right">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p class="cb-card-content-text">Text für Beschreibung.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food7.jpg" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Bild 3 pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--3 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--left">
                                <div class="cb-card-content">
                                    <p>Top See Hotels</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food5.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--left">
                                <div class="cb-card-content">
                                    <p>Top See Hotels</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food2.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--left">
                                <div class="cb-card-content">
                                    <p>Top See Hotels</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food9.jpg" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Bild 4 pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst, indem das <i>src</i> Attribut im <i>img</i> überschrieben wird</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des  <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--4 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food1.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food2.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food3.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/food6.jpg" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>

                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Bild 5 pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst, indem das <i>src</i> Attribut im <i>img</i> überschrieben wird</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des  <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--5 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/friends1.jpeg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/friends2.jpeg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/friends3.jpeg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/friends4.jpeg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/friends5.jpeg" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Bild 6 pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--6 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion1.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion2.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion3.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion4.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion5.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion6.jpg" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Bild - Clean</h2>
                    <ul class="cmg263">
                        <li>Anzeige ohne Hintergrund und Rahmen: Auf die gewünschte Box die class="cb-card--clean" hinzufügen.</li>
                        <li>Das Bild der Box wird angepasst indem man das <i>src</i> Attribut im <i>img</i> Element überschreibt</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--6 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--clean cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion1.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--clean cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion2.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion3.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion4.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion5.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3>Überschrift hier</h3>
                                    <p>Ausführlicher Beschreibungstext hier, weitere Infos, Texte, etc.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Hotels am See</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion6.jpg" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Bild unten - 4 pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst, indem das <i>src</i> Attribut im <i>img</i> überschrieben wird</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des  <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards-grouped--4 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--bottom">
                                <div class="cb-card-content cb-card-content--quaternary">
                                    <h3>Überschrift hier</h3>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion1.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--bottom">
                                <div class="cb-card-content cb-card-content--quinary">
                                    <h3>Überschrift hier</h3>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion2.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--bottom">
                                <div class="cb-card-content cb-card-content--senary">
                                    <h3>Überschrift hier</h3>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion3.jpg" />
                                </div>
                            </div>
                            <div class="cb-card cb-card--bottom">
                                <div class="cb-card-content cb-card-content--primary">
                                    <h3>Überschrift hier</h3>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/discussion4.jpg" />
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="cardsWithImgs">
                    <h2 class="cmg21">Boxen mit Bild - horizontal</h2>
                    <ul class="cmg263">
                        <li>Das Bild der Box wird angepasst, indem das <i>src</i> Attribut im <i>img</i> überschrieben wird</li>
                        <li>Der alternative Text für das neue Bild, wird im  <i>alt</i> Attribut des <i>img</i> Elements angepasst</li>
                        <li>Ein neuer Link wird hochgeladen, indem der Wert des  <i>href</i> Attributs in einem <i>a</i> tag aktualisiert wird</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--1--horizontal jq-fully-responsive-cards">
                            <div class="cb-card cb-card--right">
                                <div class="cb-card-content">
                                    <h3>Überschrift</h3>
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Weiterlesen</a>
                                </div>
                                <div class="cb-card-visual">
                                    <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/New-images/ferns.jpeg" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="cardsWithSimpleIcons">
                    <h2 class="cmg21">Boxen mit Icons</h2>
                    <ul class="cmg263">
                        <li>Die Anzahl der in einer Reihe angezeigten Boxen werden durch ändern der <i>x</i> in <i>cb-cards--x</i> Klasse</li>
                        <li>Hintergundfarben ändern durch setzten von <i>primary</i>, <i>secondary</i>, <i>tertiary</i> oder <i>quaternary</i> statt <i>z</i> in <i>cb-card-visual--z</i> class</li>
                        <li>Ersetzen wird das Standardicon durch ein neues aus unserer <a href="/icons-guide" target="_blank">Bibliothek</a>. Dazu muss einfach <i>icon-y</i> mit Icon <i>y</i> ausgetauscht werden.</li>
                        <li>Es kann vom Link- zum Schaltflächenstil gewechselt werden, indem die Klasse <i>cb-card-content-button</i> und <i>cb-card-content-link</i> Klasse in <i>a</i> Element </li>
                        <li>Die Katen werden anklickbar indem das <i>div</i> tag mit <i>a</i> in Element ersetzten, das die <i>cb-card</i> Klasse enthält. Denk daran das <i>href</i> Attribute in einem <i>a</i> tag hinzuzufügen und ihm die url zuzuweisen</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--1 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--right">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Stadtläufe</h3>
                                    <p class="cb-card-content-text">Läufe auf der Karte finden, egal ob in der Stadt oder auf dem Land: Läufe aller Art.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu den Läufen</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--primary">
                                    <i class="icon-child"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Icons</h2>
                    <ul class="cmg263">
                        <li>Die Anzahl der in einer Reihe angezeigten Boxen werden durch ändern der <i>x</i> in <i>cb-cards--x</i> Klasse</li>
                        <li>Hintergundfarben ändern durch setzten von <i>primary</i>, <i>secondary</i>, <i>tertiary</i> oder <i>quaternary</i> statt <i>z</i> in <i>cb-card-visual--z</i> class</li>
                        <li>Ersetzen wird das Standardicon durch ein neues aus unserem <a href="/icons-guide" target="_blank">Bibliothek</a>. Dazu muss einfach <i>icon-y</i> mit Icon <i>y</i> ausgetauscht werden.</li>
                        <li>Es kann vom Link- zum Schaltflächenstil wechseln, indem die Klasse <i>cb-card-content-button</i> und <i>cb-card-content-link</i> Klasse in <i>a</i> Element </li>
                        <li>Die Katen werden anklickbar indem das <i>div</i> tag mit <i>a</i> in Element ersetzten, das die <i>cb-card</i> Klasse enthält. Denk daran das <i>href</i> Attribute in einem <i>a</i> tag hinzuzufügen und ihm die url zuzuweisen.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--2 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--right">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Twitter</h3>
                                    <p class="cb-card-content-text">Läufe auf der Karte finden, egal ob in der Stadt oder auf dem Land: Läufe aller Art.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu den Läufen</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--primary">
                                    <i class="icon-twitter"></i>
                                </div>
                            </div>
                            <div class="cb-card cb-card--right">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Facebook</h3>
                                    <p class="cb-card-content-text">Läufe auf der Karte finden, egal ob in der Stadt oder auf dem Land: Läufe aller Art.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu den Läufen</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--primary">
                                    <i class="icon-facebook-light"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">3 Boxen pro Reihe mit Icons</h2>
                    <ul class="cmg263">
                        <li>Die Anzahl der in einer Reihe angezeigten Boxen werden durch ändern der <i>x</i> in <i>cb-cards--x</i> Klasse</li>
                        <li>Hintergundfarben ändern durch setzten von <i>primary</i>, <i>secondary</i>, <i>tertiary</i> oder <i>quaternary</i> statt <i>z</i> in <i>cb-card-visual--z</i> class</li>
                        <li>Ersetzen wird das Standardicon durch ein neues aus unserem <a href="/icons-guide" target="_blank">Bibliothek</a>. Dazu muss einfach <i>icon-y</i> mit Icon <i>y</i> ausgetauscht werden.</li>
                        <li>Es kann vom Link- zum Schaltflächenstil wechseln, indem die Klasse <i>cb-card-content-button</i> und <i>cb-card-content-link</i> Klasse in <i>a</i> Element </li>
                        <li>Die Katen werden anklickbar indem das <i>div</i> tag mit <i>a</i> in Element ersetzten, das die <i>cb-card</i> Klasse enthält. Denk daran das <i>href</i> Attribute in einem <i>a</i> tag hinzuzufügen und ihm die url zuzuweisen.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--3 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--left">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Design</h3>
                                    <a class="cb-card-content-button" href="link-hier-setzen">mehr erfahren</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--secondary">
                                    <i class="icon-pen-nib-light"></i>
                                </div>
                            </div>
                            <div class="cb-card cb-card--left">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Feedback</h3>
                                    <a class="cb-card-content-button" href="link-hier-setzen">mehr erfahren</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--secondary">
                                    <i class="icon-comment"></i>
                                </div>
                            </div>
                            <div class="cb-card cb-card--left">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Verbindung</h3>
                                    <a class="cb-card-content-button" href="link-hier-setzen">mehr erfahren</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--secondary">
                                    <i class="icon-globe"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">6 Boxen pro Reihe mit Icons</h2>
                    <ul class="cmg263">
                        <li>Die Anzahl der in einer Reihe angezeigten Boxen werden durch ändern der <i>x</i> in <i>cb-cards--x</i> Klasse</li>
                        <li>Hintergundfarben ändern durch setzten von <i>primary</i>, <i>secondary</i>, <i>tertiary</i> oder <i>quaternary</i> statt <i>z</i> in <i>cb-card-visual--z</i> class</li>
                        <li>Ersetzen wird das Standardicon durch ein neues aus unserem <a href="/icons-guide" target="_blank">Bibliothek</a>. Dazu muss einfach <i>icon-y</i> mit Icon <i>y</i> ausgetauscht werden.</li>
                        <li>Es kann vom Link- zum Schaltflächenstil wechseln, indem die Klasse <i>cb-card-content-button</i> und <i>cb-card-content-link</i> Klasse in <i>a</i> Element </li>
                        <li>Die Katen werden anklickbar indem das <i>div</i> tag mit <i>a</i> in Element ersetzten, das die <i>cb-card</i> Klasse enthält. Denk daran das <i>href</i> Attribute in einem <i>a</i> tag hinzuzufügen und ihm die url zuzuweisen.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-cards--6 jq-fully-responsive-cards">
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Venidi</h3>
                                    <p class="cb-card-content-text">Einträge am See.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu Einträgen am See</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--tertiary">
                                    <i class="icon-auszeichnung-default"></i>
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Imit</h3>
                                    <p class="cb-card-content-text">Einträge am See.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu Einträgen am See</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--tertiary">
                                    <i class="icon-location-city"></i>
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Sibid</h3>
                                    <p class="cb-card-content-text">Einträge am See.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu Einträgen am See</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--tertiary">
                                    <i class="icon-plus"></i>
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Lelum</h3>
                                    <p class="cb-card-content-text">Einträge am See.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu Einträgen am See</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--tertiary">
                                    <i class="icon-shopping-cart"></i>
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Lorem</h3>
                                    <p class="cb-card-content-text">Einträge am See.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu Einträgen am See</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--tertiary">
                                    <i class="icon-bicycle"></i>
                                </div>
                            </div>
                            <div class="cb-card cb-card--top">
                                <div class="cb-card-content">
                                    <h3 class="cb-card-content-header">Demet</h3>
                                    <p class="cb-card-content-text">Einträge am See.</p>
                                    <a class="cb-card-content-link" href="link-hier-setzen">Link zu Einträgen am See</a>
                                </div>
                                <div class="cb-card-visual cb-card-visual--tertiary">
                                    <i class="icon-camera"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="cardsWithIconsInCircles">
                    <h2 class="cmg21">Boxen mit Icon-Kreis - 4 Boxen pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Um die Anzahl der Elemente in einer Zeile zu ändern, fügt man sie mit <i>cwc</i> <i>cwc--x</i> class zum Element hinzu. Dabei ist <i>x</i> die Anzahl der Elemente</li>
                        <li>Soll die Box auf eine andere Seite zeigen, so kann man einen Link einfügen, indem beim Element mit class="cwc-card" die URL in das <i>href</i> Attribut einfügen</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cwc cwc--4">
                            <a href="#" class="cwc-card cwc-card--primary icon-wrench">
                                <span class="cwc-body-title">Newsletter</span>
                                <span class="cwc-body-text">Keep your clients up to date with the latest news</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--secondary icon-rss">
                                <span class="cwc-body-title">Good organization</span>
                                <span class="cwc-body-text">Our experience helped us create best quality services</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--quaternary icon-cog">
                                <span class="cwc-body-title">Secure connection</span>
                                <span class="cwc-body-text">High quality passwords will keep your data safe</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--tertiary icon-flag">
                                <span class="cwc-body-title">Improvements</span>
                                <span class="cwc-body-text">We are keep improving our product</span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Icon-Kreis - 6 Boxen pro Reihe</h2>
                    <ul class="cmg263">
                        <li>Um die Anzahl der Elemente in einer Zeile zu ändern, fügt man sie mit <i>cwc</i> <i>cwc--x</i> class zum Element hinzu. Dabei ist <i>x</i> die Anzahl der Elemente</li>
                        <li>Soll die Box auf eine andere Seite zeigen, so kann man einen Link einfügen, indem beim Element mit class="cwc-card" die URL in das <i>href</i> Attribut einfügen</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cwc cwc--6">
                            <a href="#" class="cwc-card cwc-card--secondary icon-envelope-alt">
                                <span class="cwc-body-title">Newsletter</span>
                                <span class="cwc-body-text">Keep your clients up to date with the latest news</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--primary icon-cog">
                                <span class="cwc-body-title">Good organization</span>
                                <span class="cwc-body-text">Our experience helped us create best quality services</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--tertiary icon-rss">
                                <span class="cwc-body-title">Secure connection</span>
                                <span class="cwc-body-text">High quality passwords will keep your data safe</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--quaternary icon-wrench">
                                <span class="cwc-body-title">Improvements</span>
                                <span class="cwc-body-text">We are keep improving our product</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--secondary icon-blank-doc">
                                <span class="cwc-body-title">Data</span>
                                <span class="cwc-body-text">Save your files on our server</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--primary  icon-lock">
                                <span class="cwc-body-title">Privacy</span>
                                <span class="cwc-body-text">We care about your data</span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Boxen mit Icon-Kreis - Clean</h2>
                    <ul class="cmg263">
                        <li>Um den Rahmen und die weiße Hintergrund-Farbe zu entfernen, fügt man  <i>cwc--clean</i> class zur Box hinzu.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cwc cwc--6">
                            <a href="#" class="cwc-card cwc-card--clean cwc-card--secondary icon-envelope-alt">
                                <span class="cwc-body-title">Newsletter</span>
                                <span class="cwc-body-text">Keep your clients up to date with the latest news</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--clean cwc-card--primary icon-cog">
                                <span class="cwc-body-title">Good organization</span>
                                <span class="cwc-body-text">Our experience helped us create best quality services</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--tertiary icon-rss">
                                <span class="cwc-body-title">Secure connection</span>
                                <span class="cwc-body-text">High quality passwords will keep your data safe</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--quaternary icon-wrench">
                                <span class="cwc-body-title">Improvements</span>
                                <span class="cwc-body-text">We are keep improving our product</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--secondary icon-blank-doc">
                                <span class="cwc-body-title">Data</span>
                                <span class="cwc-body-text">Save your files on our server</span>
                            </a>
                            <a href="#" class="cwc-card cwc-card--primary  icon-lock">
                                <span class="cwc-body-title">Privacy</span>
                                <span class="cwc-body-text">We care about your data</span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg1 jq-cmg-section" id="squareCards">
                    <h2 class="cmg11 jq-scrollspy">Einfache, farbige, quadratische Boxen</h2>
                    <p class="cmg12">Für thematische Darstellung</p>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">5 Boxen in einer Zeile</h2>
                        <div class="cmg23 jq-element">
                            <ul class="cb-cards--square">
                                <li class="cb-card--square cb-card--square--primary cb-card--square--5">
                                    <a href="#" class="cb-card--square-link">
                                        <span class="cb-card--square-text-hidden">
                                            Text sichtbar on hover
                                        </span>
                                    </a>
                                    <h3 class="cb-card--square-header">Box 1</h3>
                                </li>
                                <li class="cb-card--square cb-card--square--secondary cb-card--square--5">
                                    <a href="#" class="cb-card--square-link">
                                        <span class="cb-card--square-text-hidden">
                                            Text sichtbar on hover
                                        </span>
                                    </a>
                                    <h3 class="cb-card--square-header">Box 1</h3>
                                </li>
                                <li class="cb-card--square cb-card--square--tertiary cb-card--square--5">
                                    <a href="#" class="cb-card--square-link">
                                        <span class="cb-card--square-text-hidden">
                                            Text sichtbar on hover
                                        </span>
                                    </a>
                                    <h3 class="cb-card--square-header">Box 1</h3>
                                </li>
                                <li class="cb-card--square cb-card--square--quaternary cb-card--square--5">
                                    <a href="#" class="cb-card--square-link">
                                        <span class="cb-card--square-text-hidden">
                                            Text sichtbar on hover
                                        </span>
                                    </a>
                                    <h3 class="cb-card--square-header">Box 1</h3>
                                </li>
                                <li class="cb-card--square cb-card--square--quinary cb-card--square--5">
                                    <a href="#" class="cb-card--square-link">
                                        <span class="cb-card--square-text-hidden">
                                            Text sichtbar on hover
                                        </span>
                                    </a>
                                    <h3 class="cb-card--square-header">Box 1</h3>
                                </li>
                            </ul>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                </div>
                <div class="cmg2 jq-cmg-subsection" id="cardsWithGradient">
                    <h2 class="cmg11 jq-scrollspy">Boxen mit Verlauf</h2>
                    <p class="cmg12">Für hervorgehobene Boxen</p>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Neutraler (Grau) Farbverlauf von oben nach unten</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-gradient cb-box-gradient--neutral">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-gradient-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Primärer Farbverlauf von oben nach unten</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-gradient cb-box-gradient--primary">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-gradient-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Farbe 4 Farbverlauf von oben nach unten</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-gradient cb-box-gradient--secondary">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-gradient-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Farbe 3 Farbverlauf von oben nach unten</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-gradient cb-box-gradient--tertiary">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-gradient-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Farbe 4 Farbverlauf von oben nach unten</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-gradient cb-box-gradient--quaternary">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-gradient-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="cardsWithBorder">
                    <h2 class="cmg21">Boxen mit Rahmen</h2>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Primäre Farbe</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-border cb-box-border--primary">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-border-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Farbe 2</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-border cb-box-border--secondary">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-border-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Farbe 3</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-border cb-box-border--tertiary">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-border-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Farbe 4</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-box-border cb-box-border--quaternary">
                                <h2>Überschrift hier</h2>
                                <p>Weitere Text-Inhalte hier: Es ist ein lang erwiesener Fakt, dass ein Leser vom Text abgelenkt wird, wenn er sich ein Layout ansieht. Der Punkt, Lorem Ipsum zu nutzen, ist, dass es mehr oder weniger die normale Anordnung von Buchstaben darstellt und somit nach lesbarer Sprache aussieht.</p>
                                <p>
                                    <a href="link-hier-setzen" class="cb-box-border-button">
                                        Hier gehts zum spannenden Content
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">Hintergrund Grau, runde Ecken</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-neutral-box">
                                <div class="cb-neutral-box-inside center">
                                    <h2 class="compact-text">Häufig gesuchte <span class="text-primary">Regionen</span></h2>
                                    <p>Finde dein passendes Familienhotel</p>
                                    <div class="cb-picture-list-3">
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter1.jpg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>572 Einträge in</span>
                                                <strong>Nordrhein-Westfalen</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter2.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>489 Einträge in</span>
                                                <strong>Niedersachsen</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter8.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>377 Einträge in</span>
                                                <strong>Bayern</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter4.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>321 Einträge in</span>
                                                <strong>Baden-Württemberg</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter1.jpg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>572 Einträge in</span>
                                                <strong>Nordrhein-Westfalen</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter2.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>489 Einträge in</span>
                                                <strong>Niedersachsen</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter8.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>377 Einträge in</span>
                                                <strong>Bayern</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter4.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>321 Einträge in</span>
                                                <strong>Baden-Württemberg</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter1.jpg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>572 Einträge in</span>
                                                <strong>Nordrhein-Westfalen</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter2.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>489 Einträge in</span>
                                                <strong>Niedersachsen</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter8.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>377 Einträge in</span>
                                                <strong>Bayern</strong>
                                            </span>
                                        </a>
                                        <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                            <img src="/themes/neutral/Styles/img/New-images/winter4.jpeg" alt="Bildbeschreibung hier einfügen!!!">
                                            <span class="cb-picture-content">
                                                <span>321 Einträge in</span>
                                                <strong>Baden-Württemberg</strong>
                                            </span>
                                        </a>
                                    </div>
                                    <p class="text-center">
                                        <a class="button-primary">alle Kinderhotels finden</a>
                                    </p>
                                </div>
                            </div>

                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                </div>
                <div class="cmg1 jq-cmg-section" id="cardsTeam">
                    <h2 class="cmg11 jq-scrollspy">Team - Kontakt</h2>
                    <p class="cmg12">Zur Vorstellung und Informationen über Menschen im Team. Als Kontakt-Karte.</p>
                    <ul class="cmg263">
                        <li>Das Bild sollte so quadratisch wie möglich sein, es wird automatisch rund gemacht. Hier ist jede Größe denkbar, jedoch empfehlen wir, dass alle Bilder auf das selbe Format/Größe zugeschnitten sind.</li>
                        <li>Das Bild kann oben oder links angezeigt werden.</li>
                        <li>Die Boxen können klassisch, inset oder ohne Box angezeigt werden.</li>
                    </ul>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">2 Team Boxen pro Zeile - Vertikal</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-cards--2 cb-cards--team jq-fully-responsive-cards">
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/woman1.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/man2.jpg" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">3 Team Boxen pro Zeile - Vertikal - Ohne Box</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-cards--3 cb-cards--team cb-cards--clean jq-fully-responsive-cards">
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/woman1.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/woman-420.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/man4.jpg" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">4 Team Boxen pro Zeile - Horizontal - Inset</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-cards--4 cb-cards--team cb-cards--inset jq-fully-responsive-cards">
                                <div class="cb-card cb-card--top">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/woman1.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--top">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/man2.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--top">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/man4.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--top">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/man4.jpg" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                </div>
                <div class="cmg1 jq-cmg-section" id="cardsTeam">
                    <h2 class="cmg11 jq-scrollspy">Team - Kontakt</h2>
                    <p class="cmg12">Zur Vorstellung und Informationen über Menschen im Team. Als Kontakt-Karte.</p>
                    <ul class="cmg263">
                        <li>Das Bild sollte so quadratisch wie möglich sein, es wird automatisch rund gemacht. Hier ist jede Größe denkbar, jedoch empfehlen wir, dass alle Bilder auf das selbe Format/Größe zugeschnitten sind.</li>
                        <li>Das Bild kann oben oder links angezeigt werden.</li>
                        <li>Die Boxen können klassisch, inset oder ohne Box angezeigt werden.</li>
                    </ul>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">2 Team Boxen pro Zeile - Vertikal</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-cards--2 cb-cards--team jq-fully-responsive-cards">
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/woman1.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/man2.jpg" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21">3 Team Boxen pro Zeile - Vertikal - Ohne Box</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-cards--3 cb-cards--team cb-cards--clean jq-fully-responsive-cards">
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/woman1.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/woman-420.jpg" />
                                    </div>
                                </div>
                                <div class="cb-card cb-card--left">
                                    <div class="cb-card-content">
                                        <ul>
                                            <li>
                                                <h4><strong class="icon-user">Peter Müller</strong></h4>
                                            </li>
                                            <li class="icon-home">Hamburg</li>
                                            <li class="icon-phone">+49 123 456 789</li>
                                            <li class="icon-envelope-alt"><a href="mailto:#"><EMAIL></a></li>
                                        </ul>
                                    </div>
                                    <div class="cb-card-visual">
                                        <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/people/man4.jpg" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                    <div class="cmg2 jq-component">
                        <h2 class="cmg21" id="topics">5 Boxen zu Themen, Suchseiten - Horizontal</h2>
                        <div class="cmg23 jq-element">
                            <div class="cb-topics cb-topics--5">
                                <div class="cb-topic">
                                    <a class="cb-topic-header" href="#link-setzen">
                                        Thema 1
                                    </a>
                                    <a href="#link-setzen" class="cb-topic-button">
                                        <span>zum Thema 1</span>
                                    </a>
                                </div>
                                <div class="cb-topic">
                                    <a class="cb-topic-header" href="#link-setzen">
                                        Thema 2
                                    </a>
                                    <a href="#link-setzen" class="cb-topic-button">
                                        <span>zum Thema 2</span>
                                    </a>
                                </div>
                                <div class="cb-topic">
                                    <a class="cb-topic-header" href="#link-setzen">
                                        Thema 3
                                    </a>
                                    <a href="#link-setzen" class="cb-topic-button">
                                        <span>zum Thema 3</span>
                                    </a>
                                </div>
                                <div class="cb-topic">
                                    <a class="cb-topic-header" href="#link-setzen">
                                        Thema 4
                                    </a>
                                    <a href="#link-setzen" class="cb-topic-button">
                                        <span>zum Thema 4</span>
                                    </a>
                                </div>
                                <div class="cb-topic">
                                    <a class="cb-topic-header" href="#link-setzen">
                                        Thema 5
                                    </a>
                                    <a href="#link-setzen" class="cb-topic-button">
                                        <span>zum Thema 1</span>
                                    </a>
                                </div>
                                <h4 class="cb-topics-title">Themen</h4>
                            </div>
                        </div>
                        <div class="cmg24">
                            <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                            <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                        </div>
                        <code class="cmg25 jq-code"></code>
                    </div>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="images">
                <h2 class="cmg11 jq-scrollspy">Bilder</h2>
                <p class="cmg12">Mit Bildern kann das Gefühl des Besuchers entsprechend Ihren Anforderungen geändernt werden. Außerdem kann der Besucher mit Bildern über weitere Inhalte der Seite infomiert werden.</p>
                <div class="cmg2 jq-component jq-cmg-subsection" id="simpleImage">
                    <h2 class="cmg21">Einfaches Bild mit Rahmen</h2>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird die url des neuen Bildes dem Attribut <i>src</i> hinzugefügt</li>
                        <li>Wenn alternativer Text von einem benutzerdefinierten Bild angepasst werden soll überschreibt man den <i>alt</i> Attributwert im <i>img</i> tag</li>
                        <li><strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <img class="img-frame" alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/Styles/img/New-images/arctica.jpg" />
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Einfaches Bild ohne Rahmen</h2>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li><strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <img class="img-clean" alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/Styles/img/New-images/arctica.jpg" />
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Einfaches Bild mit runden Ecken</h2>
                    <ul class="cmg263">
                        <li>Auf dem gewünschenten img-Element die class="cb-img--rounded" setzen.</li>
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li><strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <img class="cb-img--rounded" alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/Styles/img/New-images/arctica.jpg" />
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Einfaches Bild abgeschnitten Ecken</h2>
                    <ul class="cmg263">
                        <li>Auf dem gewünschenten img-Element die class="cb-img--rounded-xl--tr" für tr: top-right, tl: top-left, bl: bottom-left, br: bottom-right etc. setzen. Die Klassen können beliebig kombiniert werden (siehe Beispiel unten):</li>
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li><strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <img class="cb-img--rounded-xl--tr cb-img--rounded-xl--bl" alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/Styles/img/New-images/arctica.jpg" />
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Einfaches Bild mit Text overlay</h2>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li><strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="img-with-text">
                            <div>
                                <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/Styles/img/New-images/arctica.jpg" />
                                <span class="text">Text hier</span>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Einfaches Bild mit link und Text overlay</h2>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li>Um den link zu ändern muss die neue url im <i>href</i> Attribut im <i>a</i> tag geändert werden</li>
                        <li><strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="img-with-text">
                            <a href="Ziel-Url">
                                <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/Styles/img/New-images/arctica.jpg" />
                                <span class="text">Text hier</span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Einfaches Bild mit Rahmen und Bildunterschrift</h2>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="img-with-caption">
                            <img class="img-frame" alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/Styles/img/New-images/arctica.jpg" />
                            <span class="img-caption">Hier die Bildbeschreibung</span>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="gallery">
                    <h2 class="cmg21">Galerie mit zwei Spalten</h2>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird einfach die URL des neuen Fotos dem Attribut <i>src</i> zugewiesen</li>
                        <li>Wenn alternativer Text von einem benutzerdefinierten Bild angepasst werden soll überschreibt man den <i>alt</i> Attributwert im <i>img</i> tag</li>
                        <li>
                            <strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.
                            Bilder in einer Reihe sollten ebenso alle die gleiche Höhe und Breite haben, so dass sie gleichmäßig angezeigt werden können.
                        </li>

                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-picture-list-2">
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/card-white.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>25&#36;</span>
                                    <strong>Apple card</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/card-black.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>10&#36;</span>
                                    <strong>Revolut card</strong>
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Galerie mit 3 Spalten</h2>
                    <p class="cmg262">Jedes Bild in der Galerie hat einen eigenen Link. Es kann also jederzeit geändert werden</p>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li>
                            <strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.
                            Bilder in einer Reihe sollten ebenso alle die gleiche Höhe und Breite haben, so dass sie gleichmäßig angezeigt werden können.
                        </li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-picture-list-3">
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/village1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>572 Einträge in</span>
                                    <strong>Nordrhein-Westfalen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/village2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>489 Einträge in</span>
                                    <strong>Niedersachsen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/village3.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>377 Einträge in</span>
                                    <strong>Bayern</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/village4.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>321 Einträge in</span>
                                    <strong>Baden-Württemberg</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/village5.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>264 Einträge in</span>
                                    <strong>Rheinland-Pfalz</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/village6.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Galerie mit 4 Spalten</h2>
                    <p class="cmg262">Jedes Bild in der Galerie hat einen eigenen Link. Sie können also jederzeit angepasst werden</p>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li>
                            <strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.
                            Bilder in einer Reihe sollten ebenso alle die gleiche Höhe und Breite haben, so dass sie gleichmäßig angezeigt werden können.
                        </li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-picture-list-4">
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/winter1.jpg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>572 Einträge in</span>
                                    <strong>Nordrhein-Westfalen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/winter2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>489 Einträge in</span>
                                    <strong>Niedersachsen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/winter8.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>377 Einträge in</span>
                                    <strong>Bayern</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/winter4.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>321 Einträge in</span>
                                    <strong>Baden-Württemberg</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/winter5.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>264 Einträge in</span>
                                    <strong>Rheinland-Pfalz</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/winter6.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/winter7.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/winter3.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Galerie mit 5 Spalten</h2>
                    <p class="cmg262">Jedes Bild in der Galerie hat einen eigenen Link. Es kann also jederzeit geändert werden </p>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li>
                            <strong>Wichtiger Hinweis zur Größe der Bilder:</strong> Bilder sollten in der Höhe und Breite genau so groß sein, wie sie im Browser angezeigt werden.
                            Bilder in einer Reihe sollten ebenso alle die gleiche Höhe und Breite haben, so dass sie gleichmäßig angezeigt werden können.
                        </li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-picture-list-5">
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>572 Einträge in</span>
                                    <strong>Nordrhein-Westfalen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>489 Einträge in</span>
                                    <strong>Niedersachsen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains3.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>377 Einträge in</span>
                                    <strong>Bayern</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains4.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>321 Einträge in</span>
                                    <strong>Baden-Württemberg</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains5.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>264 Einträge in</span>
                                    <strong>Rheinland-Pfalz</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains6.jpg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains7.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains8.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains9.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/mountains10.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>158 Einträge in</span>
                                    <strong>Hessen</strong>
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>

                <div class="cmg2 jq-component" id="shuffle">
                    <h2 class="cmg21">Kompakte, zufällig rotierende 2x8 Bilder Galerie mit Hover Effekt</h2>
                    <p class="cmg262">Die 2x8 Bilder rotieren zufällig beim Seitenaufruf. Jedes Bild in der Galerie hat einen eigenen Link und Text, der beim Hovern mit der Maus angezeigt wird.</p>
                    <p class="cmg262">Wir empfehlen die Nutzung über die gesamte Breite mit der Klasse "cb-full-width" auf dem Element mit der Klasse "cb-picture-gallery".</p>
                    <p class="cmg262">In mobilen Ansichten werden Karten auf weitere Zeilen aufgeteilt.".</p>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li>Die Bilder sollten optimaler Weise die selbe Höhe und Breite haben.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-picture-gallery jq-shuffle-parent">
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/road1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/road2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains3.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains4.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains5.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains6.jpg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains3.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains4.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains5.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/mountains6.jpg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/road1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                            <a class="jq-shuffle-element cb-pg11" href="#link-setzen" title="Beschreibung eingeben">
                                <img src="/themes/neutral/Styles/img/New-images/road2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-pg14">
                                    <span>normaler Text</span>
                                    <strong>Hervorgehobener Text</strong>
                                    <span>weiterer Text</span>
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>

                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Funky Galerie</h2>
                    <p class="cmg262">Jedes Bild in der Galerie hat einen eigenen Link. Es kann also jederzeit geändert werden </p>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class='cb-gallery-funky'>
                            <a class="cb-picture-normal" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/road1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>25$</span>
                                    <strong>Apple card</strong>
                                </span>
                            </a>
                            <div class='cb-gallery-funky-subsection'>
                                <a class="cb-picture-normal" href="link-auf-die-entsprechende-seite">
                                    <img src="/themes/neutral/Styles/img/New-images/road2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                    <span class="cb-picture-content">
                                        <span>25$</span>
                                        <strong>Apple card</strong>
                                    </span>
                                </a>
                                <a class="cb-picture-normal" href="link-auf-die-entsprechende-seite">
                                    <img src="/themes/neutral/Styles/img/New-images/mountains9.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                    <span class="cb-picture-content">
                                        <span>25$</span>
                                        <strong>Apple card</strong>
                                    </span>
                                </a>
                                <a class="cb-picture-normal" href="link-auf-die-entsprechende-seite">
                                    <img src="/themes/neutral/Styles/img/New-images/mountains7.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                    <span class="cb-picture-content">
                                        <span>25$</span>
                                        <strong>Apple card</strong>
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Galerie mit 2 Spalten und Boxen Icon</h2>
                    <p class="cmg262">Jedes Bild in der Galerie hat einen eigenen Link. Es kann also jederzeit geändert werden </p>
                    <ul class="cmg263">
                        <li>Um das Bild zu ändern, wird dem Attribut <i> src </i> einfach die URL des neuen Fotos zugewiesen</li>
                        <li>Wenn alternativer Text an ein benutzerdefiniertes Bild angepasse werden soll, wird einfach den Attributwert <i> alt </i> im Tag <i> img </i> überschrieben </li>
                        <li>Bitte beachten: Der Inhalt der Klasse <i>cb-picture-list-map</i> in <i>div</i> ist verantwortlich für die Darstellung der Icons über dem Bild.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-picture-list-2 cb-picture-list-map">
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/road1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>Road to Delhi</span>
                                    <strong>India</strong>
                                </span>
                            </a>
                            <a class="cb-picture-item" href="link-auf-die-entsprechende-seite">
                                <img src="/themes/neutral/Styles/img/New-images/road2.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <span class="cb-picture-content">
                                    <span>Road to Novgorod</span>
                                    <strong>Russia</strong>
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>

                <div class="cmg2 jq-component jq-cmg-subsection" id="imgDownload">
                    <h2 class="cmg21">Bild Download</h2>
                    <p class="cmg262">Diese Komponente ist die perfekte Lösung, wenn das Herunterladen von Bildern von der Website aktiviert sein soll.</p>
                    <ul class="cmg263">
                        <li>Man muss beim Anpassen dieser Komponente daran denken, das Attribut <i> href </i> in <i> a </i> -Tags und das Attribut <i> src </i> in <i> img </i> mit mit dem Foto zu überschreiben "s url</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <ul class="content-image-list">
                            <li>
                                <a href="/themes/neutral/styles/img/New-images/bike1.jpeg">
                                    <img src="/themes/neutral/styles/img/New-images/bike1.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                </a>
                                <h3>Bild Überschrift</h3>
                                <p>sonstiger Text hier</p>
                                <p><a class="download-link" href="/themes/neutral/styles/img/New-images/bike1.jpeg" target="_blank">dieses Bild als druckfähige Datei herunterladen</a></p>
                            </li>
                            <li>
                                <a href="/themes/neutral/styles/img/New-images/bike2.jpg">
                                    <img src="/themes/neutral/styles/img/New-images/bike2.jpg" alt="Bildbeschreibung hier einfügen!" />
                                </a>
                                <h3>Bild Überschrift</h3>
                                <p>sonstiger Text hier</p>
                                <p><a class="download-link" href="/themes/neutral/styles/img/New-images/bike2.jpg" target="_blank">dieses Bild als druckfähige Datei herunterladen</a></p>
                            </li>
                        </ul>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="hero">
                    <h2 class="cmg21">Hero Bild</h2>
                    <p class="cmg262">Hero Bilder befinden sich normalerweise oben auf der Website, um dem Benutzer die wichtigsten Informationen anzuzeigen. Neben etwas Text kann dort ein interessantes auffälliges Bild eingefügt werden.</p>
                    <div class="cmg23 jq-element">
                        <div class="group">
                            <div class="cb-hero-block">
                                <img src="/themes/neutral/Styles/img/New-images/hero.jpeg" alt="Bildbeschreibung hier einfügen!" />
                                <div class="cb-hero-text">
                                    <h1>Sommer in Deiner Branche</h1>
                                    <h2>Zeit, Premium zu buchen</h2>
                                    <p>Alle Vorteile abgreifen</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection component-full-width" id="banner">
                    <h2 class="cmg21">Banner</h2>
                    <p class="cmg262">Der Banner funktioniert sehr gut für Premium, Aktionen oder besondere Hinweise.</p>
                    <p class="cmg262">
                        Das gewünschte Bild sollte zur Zielgruppe passen.
                    </p>
                    <ul class="cmg263">
                        <li>Optimal wären auf das Template abgestimmte Farben im Foto</li>
                        <li>Die Augen können entweder auf den Besucher gerichtet sein oder zum Button schauen</li>
                        <li>Wir empfehlen emotionale Ausdrücke/Gesten: Freude, Überraschung, Empfehlung</li>
                    </ul>
                    <p class="cmg262">Hier einige kostenfreie Online Tools, um Hintergründe von Fotos zu entfernen (freistellen):</p>
                    <ul class="cmg263">
                        <li><a href='https://designify.com' target="_blank">designify.com</a></li>
                        <li><a href='https://remove.bg' target="_blank">remove.bg</a></li>
                        <li><a href='https://removal.ai' target="_blank">removal.ai</a></li>
                    </ul>
                    <p class="cmg262">Hier einige Online Bilder Bibliotheken, für ggf. lizenzfreie Bilder und Fotos (Bitte die Lizenzen und Bestimmungen von jedem Bild selbst prüfen):</p>
                    <ul class="cmg263">
                        <li><a href='https://unsplash.com' target="_blank">unsplash.com</a></li>
                        <li><a href='https://pexels.com' target="_blank">pexels.com</a></li>
                    </ul>
                    <div class="cb-tabbed-nav">
                        <ul class="cb-tabbed-tabs">
                            <li class="active">
                                <a data-toggle="tab" href="#banner-primary">Banner 1</a>
                            </li>
                            <li>
                                <a data-toggle="tab" href="#banner-secondary">Banner 2</a>
                            </li>
                            <li>
                                <a data-toggle="tab" href="#banner-tertiary">Banner 3</a>
                            </li>
                        </ul>
                        <div id="banner-primary" class="cb-tabbed-pane hide active">
                            <div class="cmg23 jq-element">
                                <div class="bnr0 bnr--primary">
                                    <div class="bnr1">
                                        <h4 class="bnr131">15% <span class="bnr1311">Neukunden Rabatt f&uuml;r Premium-Buchung</span></h4>
                                        <div class="bnr12">
                                            <h2 class="bnr121">Mit meinem Unternehmen direkt mit Premium durchstarten</h2>
                                            <h3 class="bnr122">Vorteile: bessere Sichtbarkeit, gr&ouml;&szlig;ere Reichweite, mehr Kunden</h3>
                                            <a class="bnr123" href="/Produkte-und-Preise">F&uuml;r mein Unternehmen Premium buchen</a>
                                        </div>
                                        <img class="bnr11" src="/themes/neutral/Styles/img/New-images/banner-img.png" alt="Bildbeschreibung hier einfügen!" />
                                        <h5 class="bnr132"><span class="bnr1312">G&uuml;ltig bis</span> 30.09.2026</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="cmg24">
                                <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                                <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                            </div>
                            <code class="cmg25 jq-code"></code>
                        </div>
                        <div id="banner-secondary" class="cb-tabbed-pane hide">
                            <div class="cmg23 jq-element">
                                <div class='bnr0 bnr--secondary'>
                                    <div class='bnr1'>
                                        <h4 class='bnr131'>
                                            15%
                                            <span class='bnr1311'>neukunden-rabatt</span>
                                        </h4>
                                        <div class='bnr12'>
                                            <h2 class='bnr121'>Premium buchen</h2>
                                            <h3 class='bnr122'>mehr Nutzer anziehen</h3>
                                            <a class='bnr123' href='/Produkte-und-Preise'>Jetzt buchen</a>
                                        </div>
                                        <img class='bnr11' src='/themes/neutral/Styles/img/New-images/banner-img.png' alt="Bildbeschreibung hier einfügen!" />
                                        <h5 class='bnr132'>
                                            <span class='bnr1312'>Gultig bis</span>
                                            30.09.2021
                                        </h5>
                                    </div>
                                </div>
                            </div>
                            <div class="cmg24">
                                <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                                <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                            </div>
                            <code class="cmg25 jq-code"></code>
                        </div>
                        <div id="banner-tertiary" class="cb-tabbed-pane hide">
                            <div class="cmg23 jq-element">
                                <div class='bnr0 bnr--tertiary'>
                                    <div class='bnr1'>
                                        <h4 class='bnr131'>
                                            15%
                                            <span class='bnr1311'>neukunden-rabatt</span>
                                        </h4>
                                        <div class='bnr12'>
                                            <h2 class='bnr121'>Premium buchen</h2>
                                            <h3 class='bnr122'>mehr Nutzer anziehen</h3>
                                            <a class='bnr123' href='/Produkte-und-Preise'>Jetzt buchen</a>
                                        </div>
                                        <img class='bnr11' src='/themes/neutral/Styles/img/New-images/banner-img.png' alt="Bildbeschreibung hier einfügen!" />
                                        <h5 class='bnr132'>
                                            <span class='bnr1312'>Gultig bis</span>
                                            30.09.2021
                                        </h5>
                                    </div>
                                </div>
                            </div>
                            <div class="cmg24">
                                <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                                <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                            </div>
                            <code class="cmg25 jq-code"></code>
                        </div>
                    </div>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection component-full-width" id="banner-slim">
                    <h2 class="cmg21">Banner kompakt</h2>
                    <p class="cmg262">Der Banner kompakt ist weniger hoch und eignet sich gut als Zwischen-Content oder Trenner. Wenn der Inhalt nicht über die gesamte Höhe des Bildschirms gehen soll.</p>
                    <div class="cb-tabbed-nav">
                        <ul class="cb-tabbed-tabs">
                            <li class="active">
                                <a data-toggle="tab" href="#banner-primary-slim">Banner 1</a>
                            </li>
                            <li>
                                <a data-toggle="tab" href="#banner-secondary-slim">Banner 2</a>
                            </li>
                            <li>
                                <a data-toggle="tab" href="#banner-tertiary-slim">Banner 3</a>
                            </li>
                        </ul>
                        <div id="banner-primary-slim" class="cb-tabbed-pane hide active">
                            <div class="cmg23 jq-element">
                                <div class='bnr0 bnr--primary bnr--slim'>
                                    <div class='bnr1'>
                                        <h4 class='bnr131'>
                                            15%
                                            <span class='bnr1311'>neukunden-rabatt</span>
                                        </h4>
                                        <div class='bnr12'>
                                            <h2 class='bnr121'>Premium buchen</h2>
                                            <h3 class='bnr122'>mehr Nutzer anziehen</h3>
                                            <a class='bnr123' href='/Produkte-und-Preise'>Jetzt buchen</a>
                                        </div>
                                        <img class='bnr11' src='/themes/neutral/Styles/img/New-images/banner-img.png' alt="Bildbeschreibung hier einfügen!" />
                                        <h5 class='bnr132'>
                                            <span class='bnr1312'>Gultig bis</span>
                                            30.09.2021
                                        </h5>
                                    </div>
                                </div>
                            </div>
                            <div class="cmg24">
                                <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                                <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                            </div>
                            <code class="cmg25 jq-code"></code>
                        </div>
                        <div id="banner-secondary-slim" class="cb-tabbed-pane hide">
                            <div class="cmg23 jq-element">
                                <div class='bnr0 bnr--secondary bnr--slim'>
                                    <div class='bnr1'>
                                        <h4 class='bnr131'>
                                            15%
                                            <span class='bnr1311'>neukunden-rabatt</span>
                                        </h4>
                                        <div class='bnr12'>
                                            <h2 class='bnr121'>Premium buchen</h2>
                                            <h3 class='bnr122'>mehr Nutzer anziehen</h3>
                                            <a class='bnr123' href='/Produkte-und-Preise'>Jetzt buchen</a>
                                        </div>
                                        <img class='bnr11' src='/themes/neutral/Styles/img/New-images/banner-img.png' alt="Bildbeschreibung hier einfügen!" />
                                        <h5 class='bnr132'>
                                            <span class='bnr1312'>Gultig bis</span>
                                            30.09.2021
                                        </h5>
                                    </div>
                                </div>
                            </div>
                            <div class="cmg24">
                                <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                                <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                            </div>
                            <code class="cmg25 jq-code"></code>
                        </div>
                        <div id="banner-tertiary-slim" class="cb-tabbed-pane hide">
                            <div class="cmg23 jq-element">
                                <div class='bnr0 bnr--tertiary bnr--slim'>
                                    <div class='bnr1'>
                                        <h4 class='bnr131'>
                                            15%
                                            <span class='bnr1311'>neukunden-rabatt</span>
                                        </h4>
                                        <div class='bnr12'>
                                            <h2 class='bnr121'>Premium buchen</h2>
                                            <h3 class='bnr122'>mehr Nutzer anziehen</h3>
                                            <a class='bnr123' href='/Produkte-und-Preise'>Jetzt buchen</a>
                                        </div>
                                        <img class='bnr11' src='/themes/neutral/Styles/img/New-images/banner-img.png' alt="Bildbeschreibung hier einfügen!" />
                                        <h5 class='bnr132'>
                                            <span class='bnr1312'>Gultig bis</span>
                                            30.09.2021
                                        </h5>
                                    </div>
                                </div>
                            </div>
                            <div class="cmg24">
                                <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                                <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                            </div>
                            <code class="cmg25 jq-code"></code>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="buttons">
                <h2 class="cmg11 jq-scrollspy">Buttons</h2>
                <p class="cmg12">Mit den Schaltflächen laden Sie Besucher ein mit Ihrer Website zu interagieren. Hinter den Buttons können sich Links zu anderen Seiten innerhalb oder außerhalb des Portals verbergen.</p>
                <div class="cmg2 jq-component jq-cmg-subsection" id="btnPrimary">
                    <h2 class="cmg21">Button - primär</h2>
                    <p class="cmg262">Um Ihren Link dieser Schaltfläche zuzuweisen, suchen Sie das <i>href</i> Attribut und überschreiben Sie dessen Inhalt.</p>
                    <div class="cmg23 jq-element">
                        <a class="button-primary" href='#link'>Button</a>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="btnSecondary">
                    <h2 class="cmg21">Button - sekundär</h2>
                    <p class="cmg262">Um Ihren Link dieser Schaltfläche zuzuweisen, suchen Sie das <i>href</i> Attribut und überschreiben Sie dessen Inhalt.</p>
                    <div class="cmg23 jq-element">
                        <a class="button-secondary" href='#link'>Button</a>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="btnsGroup">
                    <h2 class="cmg21">CTA - mehrere Buttons</h2>
                    <p class="cmg262">Um Ihren Link dieser Schaltfläche zuzuweisen, suchen Sie das <strong>href</strong> Attribut und überschreiben Sie dessen Inhalt.</p>
                    <div class="cmg23 jq-element cmg-component--multiple-buttons">
                        <p class="cb-cta-action">
                            <a class="button-primary" href='#link'>1. Button</a>
                            <a class="button-secondary" href='#link'>2. Button</a>
                        </p>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="btnsGroup">
                    <h2 class="cmg21">Buttons mit interessanten Animationen</h2>
                    <p class="cmg262">Um bestimmte Buttons noch interessanter zu gestalten, bieten verschiedene Animationen an. Diese können auch global eingesetzt werden - bitte beim Support melden.</p>
                    <p class="cmg262">Um Ihren Link dieser Schaltfläche zuzuweisen, suchen Sie das <strong>href</strong> Attribut und überschreiben Sie dessen Inhalt.</p>
                    <p class="cmg262">Um die Animation in Aktion zu sehen, einfach über den jeweiligen Button hovern und klicken:</p>
                    <div class="cmg23 jq-element cmg-component--multiple-buttons">
                        <button class='cmg-btn-animated--scale'>Skaliert</button>
                        <button class='cmg-btn-animated--gradient'>Verlauf</button>
                        <button class='cmg-btn-animated--shine'>Glanz</button>
                        <button class='cmg-btn-animated--ghost'>Pulsieren</button>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="cta">
                    <h2 class="cmg21">Call to action</h2>
                    <p class="cmg262">Diese Komponente kann mit<a href="#full-width" class="jq-trigger-scroll"> Vollerbreite </a>kombiniert werden um eine bessere Wirkung zu erzielen.</p>
                    <div class="cmg23 jq-element">
                        <div class="cb-cta icon-home">
                            <div class="cb-cta-content">
                                <h3>Überschrift hier - Newsletter Anmeldung</h3>
                                <p>Weiterer Text oder andere Inhalte hier. Zum Beispiel Anmeldung zu einem Newsletter. Der Button leitet auf eine andere Seite im Portal oder auch extern weiter.</p>
                            </div>
                            <div class="cb-cta-action">
                                <a href="link-hier" class="button-primary icon-arrow-right-thin-alt">Zum Newsletter anmelden</a>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="btnsTags">
                    <h2 class="cmg21">Gruppierte Buttons</h2>
                    <p class="cmg262">Wenn auf einmal mehrere Links auf den Seiten geteilt werden sollen, ist diese Komponente die richtige Wahl.</p>
                    <div class="cmg23 jq-element">
                        <div class="cb-tag-list">
                            <a href="url-hier" title="alle L&auml;ufe">
                                <span>L&auml;ufe bis</span>
                                <strong>10km</strong>
                                <span class="cb-tag-amount">(2571)</span>
                            </a>
                            <a href="url-hier" title="alle L&auml;ufe"> <span>L&auml;ufe von</span> <strong>5km bis 10km</strong> <span class="cb-tag-amount">(2139)</span> </a>
                            <a href="url-hier" title="alle L&auml;ufe"> <span>L&auml;ufe bis</span> <strong>5km</strong> <span class="cb-tag-amount">(2062)</span> </a>
                            <a href="url-hier" title="alle L&auml;ufe"> <span>L&auml;ufe von</span> <strong>10km bis 20km</strong> <span class="cb-tag-amount">(682)</span> </a>
                            <a href="url-hier" title="alle L&auml;ufe"> <span>alle</span> <strong>Halbmarathons</strong> <span class="cb-tag-amount">(545)</span> </a>
                            <a href="url-hier" title="alle L&auml;ufe"> <span>alle</span> <strong>Marathons</strong> <span class="cb-tag-amount">(172)</span> </a>
                            <a href="url-hier" title="alle L&auml;ufe"> <span>L&auml;ufe von</span> <strong>20 bis 30km</strong> <span class="cb-tag-amount">(170)</span> </a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="icons">
                <h2 class="cmg11 jq-scrollspy">Icons</h2>
                <p class="cmg12">Icons sind wie Metaphern. Sie helfen dem Benutzer, den richtigen Inhalt zu finden und Aufmerksamkeit zu erregen, wenn dies erforderlich ist. Je einfacher es ist, Ihre Website für Benutzer zu verstehen, desto eher sind sie bereit, sie zu verwenden und erneut zu besuchen.</p>
                <p class="cmg12">Um ein Icon zu ändern muss einfach der Wert des Attributs <i>class</i> geändert werden</p>
                <p class="cmg12">Mehr Icons finden sie in unserer <a href="icons-guide" target="_blank">Bibliothek</a></p>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Icon</h2>
                    <div class="cmg23 jq-element">
                        <p class="icon-rounded-checkbox">Einzelner Check</p>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                    <div class="cmg26">
                        <h3 class="cmg261">Tips & Details</h3>
                        <p class="cmg262">Als Komponente, die die Aufmerksamkeit des Benutzers auf sich zieht, können Sie hier eine Einladung für Ihren Newsletter einfügen oder zu bestimmten Maßnahmen ermutigen. Weitere Icons finden Sie in unserer <a href="icons-guide">Bibliothek.</a></p>
                        <ul class="cmg263">
                        </ul>
                    </div>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Icons in Listen</h2>
                    <div class="cmg23 jq-element">
                        <ul style="list-style: none">
                            <li class="icon-comments">Gute Kommunikation mit Kunden</li>
                            <li class="icon-auszeichnung-default">Höchste Qualität</li>
                            <li class="icon-time">Immer pünktlich</li>
                        </ul>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="counters">
                <h2 class="cmg11 jq-scrollspy">Zähler</h2>
                <p class="cmg12">Zähler sind die perfekte Lösung, um Ihre Leistungen in Bezug auf Ihre Kunden, Verkäufe und zufriedene Kunden zu demonstrieren. Dadurch gewinnt man Vertrauen bei den Besuchern und die Wahrscheinlichkeit, sie zu Ihren Kunden zu machen, steigt drastisch an.</p>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Zähler</h2>
                    <ul class="cmg263">
                        <li>Die Anzahl der Zähler pro Zeile lassen sich ändern indem man <i>x</i> in einem <i>div</i> mit <i>cb-Nummern--x</i> ändert und zum Haupt <i>hinzufügt div"s</i> enthält einen Codeblock mit <i>div</i> der das Attribut <i>class</i> von <i>cb-numbers-item hat</i></li>
                        <li>Um Icons in den Zählern anzupassen sucht man das <i>i</i> tag mit der <i>cb-numbers-item-icon</i> Klasse und ändert es in das <i>icon-x</i> in ein anderes Icon aus unserer <a href="icons-guide" target="_blank">Bibliothek</a></li>
                        <li>Wenn die Zählernummer in ein Zeitformat wie <i>5:25</i> geändert werden soll, muss die Klasse <i>jq-cb-numbers-item-count</i> aus diesem Element entfernt werden</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-numbers cb-numbers--5">
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-home"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">250.321</h3>
                                <h5 class="cb-numbers-item-header">Besucher</h5>
                            </div>
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-flag"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">50.321</h3>
                                <h5 class="cb-numbers-item-header">Seitenaufrufe</h5>
                            </div>
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-globe"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">321</h3>
                                <h5 class="cb-numbers-item-header">Aufrufe</h5>
                            </div>
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-time"></i>
                                <h3 class="cb-numbers-item-counter">4:35min</h3>
                                <h5 class="cb-numbers-item-header">Aufenthaltsdauer</h5>
                            </div>
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-mail-forward"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">12321</h3>
                                <h5 class="cb-numbers-item-header">Homepageclicks</h5>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Zähler mit Rahmen</h2>
                    <ul class="cmg263">
                        <li>Die Anzahl der Zähler pro Zeile lassen sich ändern indem man <i>x</i> in einem <i>div</i> mit <i>cb-Nummern--x</i> ändert und zum Haupt <i>hinzufügt div"s</i> enthänlt einen Codeblock mit <i>div</i> der das Attribut <i>class</i> von <i>cb-numbers-item hat</i></li>
                        <li>Um Icon in den Zählern anzupassen sucht man das <i>i</i> tag mit der <i>cb-numbers-item-icon</i> Klasse und ändert es in das <i>icon-x</i> in ein anderes Icon aus unserer <a href="icons-guide" target="_blank">Bibliothek</a></li>
                        <li>Wenn die Zählernummer in ein Zeitformat wie <i>5:25</i> geändert werden soll, muss die Klasse <i>jq-cb-numbers-item-count</i> aus diesem Element entfernt werden</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-numbers cb-numbers--3 cb-numbers--border">
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-twitter"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">486</h3>
                                <h5 class="cb-numbers-item-header">Tweets</h5>
                            </div>
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-auszeichnung-default"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">38</h3>
                                <h5 class="cb-numbers-item-header">Auszeichnungen</h5>
                            </div>
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-comments"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">2951</h3>
                                <h5 class="cb-numbers-item-header">Meetings</h5>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Zähler mit unterschiedlichem Hintergrund</h2>
                    <ul class="cmg263">
                        <li>Die Anzahl der Zähler pro Zeile lassen sich ändern indem man <i>x</i> in einem <i>div</i> mit <i>cb-Nummern--x</i> ändert und zum Haupt <i>hinzufügt div"s</i> enthänlt einen Codeblock mit <i>div</i> der das Attribut <i>class</i> von <i>cb-numbers-item hat</i></li>
                        <li>Um Icons in den Zählern anzupassen sucht man das <i>i</i> tag mit der <i>cb-numbers-item-icon</i> Klasse und ändert es in das <i>icon-x</i> in ein anderes Icon aus unserer <a href="icons-guide" target="_blank">Bibliothek</a></li>
                        <li>Wenn die Zählernummer in ein Zeitformat wie <i>5:25</i> geändert werden soll, muss die Klasse <i>jq-cb-numbers-item-count</i> aus diesem Element entfernt werden</li>
                        <li>Rahmen können hinzugefügt werden, indem man der Hauptklasse <i>div</i> mit <i>cb-numbers</i> <i>cb-numbers--border</i> zuweist</li>
                        <li>Standardmäßig ist die Hintergrundfarbe des Zählers auf Primärfarbe eingestellt, um diese zu ändern, kann die Hauptklasse <i> cb-Nummern - sekundäre </i> oder <i> cb-Nummern - tertiäre </i> der Klasse hinzugefügen werden <i> div </i> mit <i> cb-numbers </i> Klasse</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class="cb-numbers cb-numbers--2 cb-numbers--secondary cb-numbers--border">
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-instagram-light"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">879</h3>
                                <h5 class="cb-numbers-item-header">Followers</h5>
                            </div>
                            <div class="cb-numbers-item">
                                <i class="cb-numbers-item-icon icon-child"></i>
                                <h3 class="cb-numbers-item-counter jq-cb-numbers-item-count">1247</h3>
                                <h5 class="cb-numbers-item-header">Supporters</h5>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="faq">
                <h2 class="cmg11 jq-scrollspy">FAQ</h2>
                <p class="cmg12">FAQ und Listen ermöglichen die vertikale Gruppierung von Inhalten.</p>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">FAQ - häufig gestellte Fragen</h2>
                    <p class="cmg262">
                        FAQ oder häufig gestellte Fragen sind ein wichtiges und produktives Tool, um Fragen von Besuchern oder Unternehmen zu beantworten.
                        Durch das interaktive Format bleibt der Inhalt übersichtlich, da Antworten auf- und zugeklappt werden können.
                        Unsere Implementierung ist auch optimiert für Suchmaschinen, da wir hier ein Format einsetzen, welches <a target="_blank" href="https://developers.google.com/search/docs/data-types/faqpage">FAQ Rich Results von Google</a> unterstützt.
                    </p>
                    <p class="cmg262">
                        Pro Seite kann normalerweise nur eine FAQ eingestellt werden. Wenn Sie mehrere FAQ Bereiche auf einer Seite einrichten wollen, stellen Sie bitte sicher,
                        dass das div mit itemtype="https://schema.org/FAQPage" nur ein Mal pro Seite gesetzt wird. Sie können diesen Parameter auf ein Eltern-Element setzen und
                        bei den Kindern entfernen.
                    </p>
                    <p class="cmg262">Link zum <a class="icon-share" target="_blank" href='https://search.google.com/structured-data/testing-tool/u/0/'>Testing Tool</a></p>
                    <p class="cmg262">Wir empfehlen den Einsatz der FAQ auf einer eigenen Seite oder auch aufgeteilt in verschiedene Fragen-Blöcke.</p>
                    <div class="cmg23 jq-element">
                        <div class='faq-wrapper'>
                            <div itemscope itemtype="https://schema.org/FAQPage">
                                <div class="faq jq-faq" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                    <h4 class="faq-head jq-faq-head" itemprop="name">
                                        <button class="faq-question">
                                            Warum sollte ich die Html Vorlagen verwenden?
                                        </button>
                                    </h4>
                                    <div class="faq-body" itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                        <div itemprop="text">
                                            <!-- Your content hier -->
                                            <h4>Uberschrift hier</h4>
                                            <p>Lorem ipsum dolor ism amet</p>
                                            <p>Suchmaschinenoptimierung oder SEO (Englisch für "search engine optimization") beschäftigt sich mit der Darstellung und dem Ranking von Webseiten in Suchmaschinen.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="faq jq-faq" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                    <h4 class="faq-head jq-faq-head" itemprop="name">
                                        <button class="faq-question">
                                            Wie kann SEO verbessern?
                                        </button>
                                    </h4>
                                    <div class="faq-body" itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                        <div itemprop="text">
                                            <!-- Your content hier -->
                                            <h4>Uberschrift hier</h4>
                                            <ol>
                                                <li>User Experience der Besucher verbessern</li>
                                                <li>Interessante Inhalte veröffentlichen</li>
                                                <li>Seiten-Lade-Geschwindigkeit verbessern</li>
                                                <li>Backlinks sammeln</li>
                                                <li>Korrektes Html verwenden</li>
                                                <li>Bilder optimieren</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>

            </div>
            <div class="cmg1 jq-cmg-section" id="lists">
                <h2 class="cmg11 jq-scrollspy">Listen</h2>
                <p class="cmg12">Listen ermöglichen die vertikale Gruppierung von Inhalten.</p>

                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Listen mit Kreisen</h2>
                    <div class="cmg23 jq-element">
                        <ul class="cb-list-circle">
                            <li>
                                <a href="link-hier">
                                    <span>32</span>
                                    <strong>Locations im Grünen</strong>
                                    <i class="icon-leaf"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>172</span>
                                    <strong>Zahnärzte mit Parkplatz</strong>
                                    <i class="icon-smile"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>58</span>
                                    <strong>Laufveranstaltungen mit Siegerpokal</strong>
                                    <i class="icon-star"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>24</span>
                                    <strong>Locations im Grünen</strong>
                                    <i class="icon-star"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Liste mit Header</h2>
                    <div class="cmg23 jq-element">
                        <div>
                            <h2>Ihre Vorteile auf kinderHOTEL.info:</h2>
                            <ul class="list-checks">
                                <li><a href="url-der-suchseite">Hotels abgestimmt auf die Bedürfnisse von Familien</a></li>
                                <li><a href="url-der-suchseite">Unabhängige und sorgfältig geprüfte Inhalte</a></li>
                                <li><a href="url-der-suchseite">Kostenfreie und unkomplizierte Anfrage an Ihr Kinderhotel</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Listen in 2 Spalten</h2>
                    <div class="cmg23 jq-element">
                        <ul class="cb-list-table">
                            <li>
                                <a href="link-hier">
                                    <span>12</span>
                                    <strong>Locations im Grünen</strong>
                                    <i class="icon-leaf"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>76</span>
                                    <strong>Zahnärzte mit Parkplatz</strong>
                                    <i class="icon-smile"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>30</span>
                                    <strong>Laufveranstaltungen mit Siegerpokal</strong>
                                    <i class="icon-star"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>45</span>
                                    <strong>Zahnärzte mit Parkplatz</strong>
                                    <i class="icon-smile"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>58</span>
                                    <strong>Laufveranstaltungen mit Siegerpokal</strong>
                                    <i class="icon-star"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>172</span>
                                    <strong>Zahnärzte mit Parkplatz</strong>
                                    <i class="icon-smile"></i>
                                </a>
                            </li>
                            <li>
                                <a href="link-hier">
                                    <span>32</span>
                                    <strong>Locations im Grünen</strong>
                                    <i class="icon-leaf"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Listen zentriert</h2>
                    <div class="cmg23 jq-element">
                        <ul class="cb-centered-list">
                            <li>
                                Facilis obcaecati a velit corrupti ullam fugit iure animi magnam at vitae nostrum quia, atque possimus tenetur, fuga harum odio sit.
                            </li>
                            <li>
                                Magnam at vitae nostrum quia, atque possimus tenetur, fuga harum odio sit.
                            </li>
                            <li>
                                Adipisicing elit. Eaque facilis obcaecati a velit corrupti ullam fugit iure animi magnam at vitae nostrum quia, atque possimus tenetur, fuga harum odio sit.
                            </li>
                        </ul>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Listen mit text</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-list-with-descr">
                            <div class="cb-list-with-descr-txt">
                                <h2>The Oberallgäu An impressive holiday region</h2>
                                <p>
                                    Im Oberallgäu beginnt das Urlaubsglück! Die Alpenregion ist ein wahres Paradies für Genussurlauber und Naturliebhaber. Entspannung und Wohlbefinden gehen einher mit Wow-Effekten und Gipfelgiganten. Nicht nur das! Die gesegnete Region lebt Kontraste und Abwechslung wie keine andere - atemberaubende Alpenkulissen und sanfte Täler, urige Berghütten und edle Hotels, grasende Kühe und attraktive Einkaufsmöglichkeiten.
                                </p>
                            </div>
                            <div class="cb-list-with-descr-list">
                                <ul class="cb-list-table">
                                    <li>
                                        <a href="#">
                                            Unterkünfte
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            Orte
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            Veranstaltungen
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            Restaurantführer
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            Hüttenführer
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            Erlebnisse & Freizeit
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            Webcams
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            Wetter
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            Riedbergpass
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Listen für globalen SEO Footer Content Block</h2>
                    <p class="cmg262">
                        Der SEO Footer wird auf allen Seiten vor dem regulären Footer angezeigt und ist optional zu befüllen.
                        Wir empfehlen ihn mit besonders relevant und oft gesuchten Links zu internen Seiten zu bestücken, die für die größte Anzahl an Besuchern relevant ist.
                        Zum Beispiel besondere Regionen oder Orte, oft besuchte Eigenschaften (Suchseiten) oder Top Themen aus dem Blog.
                    </p>
                    <div class="cmg23 jq-element">
                        <div class="cb-pre-footer">
                            <div>
                                <h3>Interessante Kinderhotel Destinationen</h3>
                                <ul>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel in</strong>
                                            <strong class="cbpf22">Tirol</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel an der</strong>
                                            <strong class="cbpf22">Ostsee</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel in</strong>
                                            <strong class="cbpf22">Italien</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel auf</strong>
                                            <strong class="cbpf22">Mallorca</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel in den</strong>
                                            <strong class="cbpf22">Alpen</strong>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h3>Beliebte Kinderhotel Merkmale</h3>
                                <ul>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel mit</strong>
                                            <strong class="cbpf22">Kinderbetreuung</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel mit</strong>
                                            <strong class="cbpf22">Swimming Pool</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel mit</strong>
                                            <strong class="cbpf22">Wellness</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel am</strong>
                                            <strong class="cbpf22">See</strong>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h3>Interessante Kinderhotel Themen</h3>
                                <ul>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel im</strong>
                                            <strong class="cbpf22">Sommer</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel mit</strong>
                                            <strong class="cbpf22">Ausflügen</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotel auf dem</strong>
                                            <strong class="cbpf22">Land</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">die besten Kinderhotels</strong>
                                            <strong class="cbpf22">2023</strong>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="url-hier-hinterlegen">
                                            <strong class="cbpf21">Kinderhotels</strong>
                                            <strong class="cbpf22">günstig finden</strong>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="sliders">
                <h2 class="cmg11 jq-scrollspy">Slider</h2>
                <p class="cmg12">Slider sind Komponenten, mit denen Inhaltsreihen wie Text oder Bilder in einem Karussell gruppieren werden können.</p>
                <p class="cmg262">Wir empfehlen, keine wichtigen Informationen in einem Slider abzulegen, da diese nicht direkt sichtbar sein können.</p>
                <ul class="cmg263">
                    <li>Um die Rotationszeit zu regulieren, können die Anzahl der Millisekunden im Attribut <i>Datenintervall</i> bearbeitet werden</li>
                    <li>Wenn die automatische Rotation enfernt werden soll wird dem <i> Datenintervall </i> Attribut<i>false</i>zugewiesen</li>
                </ul>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Slider</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-slider slide jq-swipable" data-ride="carousel" data-interval="5000">
                            <a class="backward cb-slider-backward" 
                               data-slide="prev"
                               aria-label="@RsCommon.SliderBackward(TR)"></a>
                            <div class="images cb-slider-inner">
                                <div class="item active">
                                    <div>
                                        <h3>Slide 1</h3>
                                        <p>
                                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
                                            tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
                                            veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                                            commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
                                            velit esse cillum dolore eu fugiat nulla pariatur.
                                        </p>
                                        <p>
                                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
                                            tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
                                            veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                                            commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
                                            velit esse cillum dolore eu fugiat nulla pariatur.
                                        </p>
                                    </div>
                                </div>
                                <div class="item ">
                                    <div>
                                        <h3>Slide 2</h3>
                                        <p>
                                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
                                            tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
                                            veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                                            commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
                                            velit esse cillum dolore eu fugiat nulla pariatur.
                                        </p>
                                    </div>
                                </div>
                                <div class="item ">
                                    <div>
                                        <h3>Slide 3</h3>
                                        <p>
                                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
                                            tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
                                            veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                                            commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
                                            velit esse cillum dolore eu fugiat nulla pariatur.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <a class="forward cb-slider-forward" 
                               data-slide="next"
                               aria-label="@RsCommon.SliderForward(TR)"></a>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="tables">
                <h2 class="cmg11 jq-scrollspy">Tabellen</h2>
                <p class="cmg12">Tabellen sind Komponenten, die Daten in geordneter Weise speichern. Dank dieser Funktion können Benutzer den Inhalt problemlos scannen.</p>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Tabellen</h2>
                    <div class="cmg23 jq-element">
                        <table>
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Surename</td>
                                    <td>Age</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Tomas</td>
                                    <td>SehrlangerNamefürmöglichstbreiteAnzeigezumTestendesLayouts</td>
                                    <td>24</td>
                                </tr>
                                <tr>
                                    <td>Henry</td>
                                    <td>Willson</td>
                                    <td>32</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Tabelle ohne Ränder</h2>
                    <div class="cmg23 jq-element">
                        <table class="table-clean">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Surename</td>
                                    <td>Age</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Tomas</td>
                                    <td>Becket</td>
                                    <td>24</td>
                                </tr>
                                <tr>
                                    <td>Henry</td>
                                    <td>Willson</td>
                                    <td>32</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="pricing-table">
                <h2 class="cmg11 jq-scrollspy">Preis Übersicht / Tabelle</h2>
                <p class="cmg12">Diese Ansicht kann für individuellere Preis-Präsentation eingesetzt werden, anstatt der Default-Preis-Übersicht.</p>
                <div class="cmg2 jq-component">
                    <div class="cmg23 jq-element">
                        <div class="cpp-wrapper">
                            <div class="cpp-box cpp--basis">
                                <h3>Basis</h3>
                                <span class="cpp-price">€ 0</span>
                                <a class="cpp-button">
                                    Hotel eintragen
                                </a>
                                <ul class="cpp-benefits">
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                </ul>
                            </div>
                            <div class="cpp-box cpp--premium">
                                <h3>Premium</h3>
                                <span class="cpp-price">
                                    <span>€ 790</span>
                                    <span class="cpp-year">/Jahr</span>
                                </span>
                                <a class="cpp-button">
                                    zur Buchung
                                </a>
                                <ul class="cpp-benefits">
                                    <li>
                                        Eintrag inkl. Daten & Bilder Eintrag inkl. Daten & BilderEintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                </ul>
                            </div>
                            <div class="cpp-box cpp--premium">
                                <h3>Premium Plus</h3>
                                <span class="cpp-price">
                                    <span>€ 1.290</span>
                                    <span class="cpp-year">/Jahr</span>
                                </span>
                                <a class="cpp-button">
                                    zur Buchung
                                </a>
                                <ul class="cpp-benefits">
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                </ul>
                                <div class="cpp-optional">
                                    <h4>Optional</h4>
                                    <a>
                                        Social-Media-Package
                                    </a>
                                    <p>
                                        <strong>+ € 1.000</strong>
                                        (anstatt <span class="cpp-red">€ 1.690</span>)
                                    </p>
                                    <p>Beides zum Gesamtpreis:</p>
                                    <span class="cpp-price">
                                        <span>€ 2.290</span>
                                        <span class="cpp-year">/Jahr</span>
                                    </span>
                                    <a class="cpp-button">
                                        zur Kombi-Buchung
                                    </a>
                                </div>
                            </div>
                            <div class="cpp-box cpp--premium">
                                <h3>Premium Top</h3>
                                <span class="cpp-price">
                                    <span>€ 1.290</span>
                                    <span class="cpp-price-break">bis</span>
                                    <span class="cpp-price-end">
                                        <span>€ 3.290</span>
                                        <span class="cpp-year">/Jahr</span>
                                    </span>
                                </span>
                                <a class="cpp-button">
                                    unverbindliche Anfrage
                                </a>
                                <ul class="cpp-benefits">
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                    <li>
                                        Eintrag inkl. Daten & Bilder
                                    </li>
                                    <li>
                                        provisionsfreie Anfragen
                                    </li>
                                </ul>
                                <div class="cpp-optional">
                                    <h4>Optional</h4>
                                    <a>
                                        Social-Media-Package
                                    </a>
                                    <p>
                                        <strong>+ € 1.000</strong>
                                        (anstatt <span class="cpp-red">€ 1.690</span>)
                                    </p>
                                    <a class="cpp-button">
                                        zur Kombi-Buchung
                                    </a>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Tabelle ohne Ränder</h2>
                    <div class="cmg23 jq-element">
                        <table class="table-clean">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Surename</td>
                                    <td>Age</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Tomas</td>
                                    <td>Becket</td>
                                    <td>24</td>
                                </tr>
                                <tr>
                                    <td>Henry</td>
                                    <td>Willson</td>
                                    <td>32</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="tabs">
                <h2 class="cmg11 jq-scrollspy">Reiter (Tabs)</h2>
                <p class="cmg12">Reiter bzw. Tabs ermöglichen das Wechseln zwischen Inhaltskategorien. Es kann immer nur einer gleichzeitig angezeigt werden.</p>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Tabs</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-tabbed-nav">
                            <ul class="cb-tabbed-tabs">
                                <li id="example-tab-1" class="active">
                                    <a data-toggle="tab" href="#example-pane-1">Beispiel Reiter 1</a>
                                </li>
                                <li id="example-tab-2">
                                    <a data-toggle="tab" href="#example-pane-2">Beispiel Reiter 2</a>
                                </li>
                                <li id="example-tab-3">
                                    <a data-toggle="tab" href="#example-pane-3">Beispiel Reiter 3</a>
                                </li>
                            </ul>
                            <div id="example-pane-1" class="cb-tabbed-pane hide active">
                                <h2>Inhalte zum Reiter 1</h2>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                                </p>
                            </div>
                            <div id="example-pane-2" class="cb-tabbed-pane hide">
                                <h2>Inhalte zum Reiter 2</h2>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                                </p>
                            </div>
                            <div id="example-pane-3" class="cb-tabbed-pane hide">
                                <h2>Inhalte zum Reiter 3</h2>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Reiter volle Breite</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-tabbed-nav-full-width">
                            <ul class="cb-tabbed-tabs">
                                <li id="example-wide-tab-1" class="active">
                                    <a data-toggle="tab" href="#example-wide-pane-1">Beispiel Reiter 1</a>
                                </li>
                                <li id="example-wide-tab-2">
                                    <a data-toggle="tab" href="#example-wide-pane-2">Beispiel Reiter 2</a>
                                </li>
                                <li id="example-wide-tab-3">
                                    <a data-toggle="tab" href="#example-wide-pane-3">Beispiel Reiter 3</a>
                                </li>
                            </ul>
                            <div id="example-wide-pane-1" class="cb-tabbed-pane hide active">
                                <h2>Inhalte zum Reiter 1</h2>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                                </p>
                            </div>
                            <div id="example-wide-pane-2" class="cb-tabbed-pane hide">
                                <h2>Inhalte zum Reiter 2</h2>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                                </p>
                            </div>
                            <div id="example-wide-pane-3" class="cb-tabbed-pane hide">
                                <h2>Inhalte zum Reiter 3</h2>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>





            <div class="cmg1 jq-cmg-section" id="testimonials">
                <h2 class="cmg11 jq-scrollspy">Kundenstimmen</h2>
                <p class="cmg12">Wenn die Meinung von Personen zu einem bestimmten Thema geteilt werden möchte, kann man Testimonials-Komponenten verwenden.</p>
                <ul class="cmg263">
                    <li>Das Bild sollte so quadratisch wie möglich sein, es wird automatisch rund gemacht. Hier ist jede Größe denkbar</li>
                    <li>Die Impene- und Ausführungszeichen werden automatisch hinzugefügt, sodass sie im Text selbst weglassen werden müssen</li>
                </ul>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Testimonials</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-people-list">
                            <div class="cb-people-item">
                                <div class="cb-people-img-wrapper"><img src="/themes/neutral/Styles/img/people/woman1.jpg" alt="Bildbeschreibung hier einfügen!" /></div>
                                <blockquote>Die Zusammenarbeit mit discoverize ist unkompliziert, die Software ist einfach zu bedienen.</blockquote>
                                <strong> Antonia aus Barcelona </strong>
                            </div>
                            <div class="cb-people-item">
                                <div class="cb-people-img-wrapper"><img src="/themes/neutral/Styles/img/people/man4.jpg" alt="Bildbeschreibung hier einfügen!" /></div>
                                <blockquote>Unglaublich, wie schnell der Umzug zu discoverize von meiner vorherigen Software von statten ging.</blockquote>
                                <strong> Gérald aus Bordeaux </strong>
                            </div>
                            <div class="cb-people-item">
                                <div class="cb-people-img-wrapper"><img src="/themes/neutral/Styles/img/people/man2.jpg" alt="Bildbeschreibung hier einfügen!" /></div>
                                <blockquote>Ich bin total zufrieden mit dem Prokukt. Danke für den tollen Service.</blockquote>
                                <strong> Rolf aus London </strong>
                            </div>
                            <div class="cb-people-item">
                                <div class="cb-people-img-wrapper"><img src="/themes/neutral/Styles/img/people/woman2.jpg" alt="Bildbeschreibung hier einfügen!" style="width:50px; height:50px;" /></div>
                                <blockquote>Die Arbeiten am Portal machen mir sehr viel Freude. Ich lerne meine Branche jeden Tag etwas besser kennen und kann es im Portal anpassen.</blockquote>
                                <strong> Marta aus Poland </strong>
                            </div>
                            <div class="cb-people-item">
                                <div class="cb-people-img-wrapper"><img src="/themes/neutral/Styles/img/people/man1.jpg" alt="Bildbeschreibung hier einfügen!" style="width:75px; height:75px;" /></div>
                                <blockquote>Dankeschön! Mit discoverize ein 2. Standbein zu starten war eine sehr gute Entscheidung.</blockquote>
                                <strong> Wilhelm aus Berlin </strong>
                            </div>
                            <div class="cb-people-item">
                                <div class="cb-people-img-wrapper"><img src="/themes/neutral/Styles/img/people/man3.jpg" alt="Bildbeschreibung hier einfügen!" style="width:150px; height:150px;" /></div>
                                <blockquote>Endlich konnte ich meinen lang gehegten Wunsch ein Branchenportal zu starten einfach und unkompliziert umsetzten.</blockquote>
                                <strong> Otto aus Cologne </strong>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component">
                    <h2 class="cmg21">Horizontale Testimonials</h2>
                    <ul class="cmg263">
                        <li>Das Bild sollte so quadratisch wie möglich sein, es wird automatisch rund gemacht. Hier ist jede Größe denkbar</li>
                        <li>Die Impene- und Ausführungszeichen werden automatisch hinzugefügt, sodass sie im Text selbst weglassen werden müssen</li>
                        <li>Für die größere Version schreibt man einfach das äußere Div mit der Klasse = "quote" anstelle von class = "quote": class = "quote quote-xl"</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div>
                            <div class="quote">
                                <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/People/man5.jpg" />
                                <div>
                                    <blockquote>
                                        Endlich konnte ich meinen lang gehegten Wunsch ein Branchenportal zu starten einfach und unkompliziert umsetzten.
                                    </blockquote>
                                    <p><strong>Tim Becket</strong>, Designer in <a href="https://discoverize.com" target="_blank">discoverize</a></p>
                                </div>
                            </div>
                            <div class="quote-xl quote">
                                <img alt="Bildbeschreibung hier einfügen!" src="/themes/neutral/styles/img/People/man6.jpg" />
                                <div>
                                    <blockquote>
                                        Dankeschön! Mit discoverize ein 2. Standbein zu starten war eine sehr gute Entscheidung.
                                    </blockquote>
                                    <p><strong>Tom Smith</strong>, CEO in <a href="https://discoverize.com" target="_blank">discoverize</a></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
            <div class="cmg1 jq-cmg-section" id="others">
                <h2 class="cmg11 jq-scrollspy">Sonstige</h2>
                <p class="cmg12">In diesem Abschnitt finden man einige spezielle Komponenten, die das Portal attraktiver und lesbarer machen können.</p>
                <ul class="cmg263">
                    <li>Die Farbe des Headers ist standardmäßig auf die primäre Farbe eingestellt. Dies kann geändert werden indem man dem Tag <i>h3</i> mit <i>x</i> die Klasse <i>cb-factbox-ribbon--x</i> hinzufügt wird durch <i>secondary</i> oder <i>tertiary</i> ersetzt</li>
                </ul>
                <div class="cmg2 jq-component jq-cmg-subsection" id="highlightedHeader">
                    <h2 class="cmg21">Hervorgehobene Überschrift</h2>
                    <div class="cmg23 jq-element">
                        <div class="cb-factbox">
                            <h3 class="cb-factbox-ribbon">Fallstudie des Projekts</h3>
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
                                tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
                                veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip.
                            </p>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="navigation">
                    <h2 class="cmg21">Navigation</h2>
                    <ul class="cmg263">
                        <li>Um den Link in der Navigation einem Abschnitt auf der Seite zuzuordnen, muss bei bei dem a-tag des Links eine <i>id</i> gesetzt werden, die beim Abschnitt vermerkt wird. Bitte beachten: IDs müssen eindeutigt sein, d.h. sie dürfen auf der selben Seite nur 1 mal verwendet werden.</li>
                    </ul>
                    <div class="cmg23 jq-element">
                        <div class='cp-parent'>
                            <div class="cp-nav jq-sticky">
                                <ul>
                                    <li><a href="#about">Über Unser</a></li>
                                    <li><a href="#services">Services</a></li>
                                    <li><a href="#contact">Kontakt</a></li>
                                </ul>
                            </div>
                            <div class="cp-content">
                                <h2 id="about">Über uns</h2>
                                <p>Mehr Informationen</p>
                                <h2 id="services">Services Bereich</h2>
                                <p>Mehr Informationen</p>
                                <h2 id="contact">Kontaktbereich</h2>
                                <p>Mehr Informationen</p>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component jq-cmg-subsection" id="typo">
                    <h2 class="cmg21">Typographie</h2>
                    <p class="cmg262">Hier zeigen wir die die grundlegenden Möglichkeiten Texte zu formatieren.</p>
                    <h4>Überschriften</h4>
                    <p class="cmg262">Um die Größe der Überschrift zu verringern, schalten Sie die öffnenden und schließenden Tags auf <i>&lt;hx&gt;</i> und <i>&lt;/hx&gt;</i>, wo <i>x</i> ist die Zahl von 1 bis 6. Eine größere Zahl entspricht einer kleineren Schriftgröße.</p>
                    <div class="cmg23 cmg2300 jq-element">
                        <h1>h1 Überschrift - extra groß</h1>
                        <h2>h2 Überschrift - groß</h2>
                        <h3>h3 Überschrift - mittel</h3>
                        <h4>h4 Überschrift - klein</h4>
                        <h5>h5 Überschrift - extra klein</h5>
                        <h6>h6 Überschrift - super klein</h6>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
                <div class="cmg2 jq-component component-full-width jq-cmg-subsection" id="logo">
                    <h2 class="cmg21">Logos</h2>
                    <p class="cmg262">Aktualisieren Sie den href-Attributwert mit Ihrem Link.</p>
                    <p class="cmg262">Für optimale Anzeige bitte nur Logos im png-Format mit Hintergrund-Transparenz verwenden.</p>
                    <div class="cmg23 jq-element">
                        <div class="lc1">
                            <div class="lc10">
                                <h3 class="lc12">Bekannt aus</h3>
                                <div class="lc11">
                                    <a class="lc111" href="#">
                                        <img alt="Bildbeschreibung hier einfügen!" class="lc1111" src="/themes/neutral/styles/img/New-images/logos/Logo-Apothekenumschau.png" />
                                    </a>
                                    <a class="lc111" href="#">
                                        <img alt="Bildbeschreibung hier einfügen!" class="lc1111" src="/themes/neutral/styles/img/New-images/logos/Logo-BILD.png" />
                                    </a>
                                    <a class="lc111" href="#">
                                        <img alt="Bildbeschreibung hier einfügen!" class="lc1111" src="/themes/neutral/styles/img/New-images/logos/Logo-Frankfurter_Allgemeine.png" />
                                    </a>
                                    <a class="lc111" href="#">
                                        <img alt="Bildbeschreibung hier einfügen!" class="lc1111" src="/themes/neutral/styles/img/New-images/logos/Logo-Rheinische_Post.png" />
                                    </a>
                                    <a class="lc111" href="#">
                                        <img alt="Bildbeschreibung hier einfügen!" class="lc1111" src="/themes/neutral/styles/img/New-images/logos/Logo-Süddeutsche_Zeitung.png" />
                                    </a>
                                    <a class="lc111" href="#">
                                        <img alt="Bildbeschreibung hier einfügen!" class="lc1111" src="/themes/neutral/styles/img/New-images/logos/Logo-suedwestpresse.png" />
                                    </a>
                                    <a class="lc111" href="#">
                                        <img alt="Bildbeschreibung hier einfügen!" class="lc1111" src="/themes/neutral/styles/img/New-images/logos/Logo-WAZ.png" />
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cmg24">
                        <button class="cmg241 jq-copy-code icon-copy">Html kopieren</button>
                        <button class="cmg242 jq-toggle-code icon-code-solid">Code anzeigen</button>
                    </div>
                    <code class="cmg25 jq-code"></code>
                </div>
            </div>
        </div>
    </div>
</div>
