﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{30E3979A-62FC-477B-94EF-DA61CC02AC21}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Neutral</RootNamespace>
    <AssemblyName>Neutral</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkProfile />
    <UseIISExpress>false</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\lib\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.ComponentModel.DataAnnotations">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\content-management-html-bearbeiten.png" />
    <Content Include="Content\blog-default-author-image-60.jpg" />
    <Content Include="Content\blog-default-title-image.jpg" />
    <Content Include="Content\eintragsseite-mit-und-ohne-premium-922.png" />
    <Content Include="Content\favicon.ico" />
    <Content Include="Content\map-with-markers.png" />
    <Content Include="Content\medien-einbetten-soundcloud-1.png" />
    <Content Include="Content\medien-einbetten-soundcloud-2.png" />
    <Content Include="Content\medien-einbetten-vimeo-1.png" />
    <Content Include="Content\medien-einbetten-vimeo-2.png" />
    <Content Include="Content\medien-einbetten-youtube.png" />
    <Content Include="Content\multi-icons.png" />
    <Content Include="Content\premium-block-922.png" />
    <Content Include="Content\suchseite-premium-922.png" />
    <Content Include="ErrorPages\500.html" />
    <Content Include="Styles\2_elements\_button-back.scss" />
    <Content Include="Styles\3_blocks\_filepond-original-plugin-image-preview.css" />
    <Content Include="Styles\3_blocks\_filepond-original.css" />
    <Content Include="Styles\3_blocks\_flags.scss" />
    <Content Include="Styles\4_layouts\_entry-page--image-popup.scss" />
    <Content Include="Styles\angucomplete-alt.css" />
    <Content Include="Styles\bootstrap.css" />
    <Content Include="Styles\dz-logo.svg" />
    <Content Include="Styles\filepond.min.css" />
    <Content Include="Styles\fonts\open-sans-v17-latin-300.svg" />
    <Content Include="Styles\fonts\open-sans-v17-latin-600.svg" />
    <Content Include="Styles\fonts\open-sans-v17-latin-700.svg" />
    <Content Include="Styles\fonts\open-sans-v17-latin-regular.svg" />
    <Content Include="Styles\gridForSearchPage.txt" />
    <Content Include="Styles\icon-fonts\fonts\dz-manage.svg" />
    <Content Include="Styles\icon-fonts\fonts\icomoon.svg" />
    <Content Include="Styles\icon-fonts\demo-files\demo.css" />
    <Content Include="Styles\icon-fonts\demo-files\demo.js" />
    <Content Include="Styles\icon-fonts\demo.html" />
    <Content Include="Styles\icon-fonts\ie7\ie7.css" />
    <Content Include="Styles\icon-fonts\ie7\ie7.js" />
    <Content Include="Styles\icon-fonts\Read Me.txt" />
    <Content Include="Styles\icon-fonts\style.css" />
    <Content Include="Styles\icon-fonts\svgs\shopping-cart.svg" />
    <Content Include="Styles\icon-font\demo-html-template.html" />
    <Content Include="Styles\icon-font\neutral-icons.html" />
    <Content Include="Styles\icon-font\svgs\align-justify.svg" />
    <Content Include="Styles\icon-font\svgs\ampel.svg" />
    <Content Include="Styles\icon-font\svgs\arrow-left-thin-alt-2.svg" />
    <Content Include="Styles\icon-font\svgs\arrow-left-thin-alt.svg" />
    <Content Include="Styles\icon-font\svgs\arrow-left.svg" />
    <Content Include="Styles\icon-font\svgs\arrow-right-thin-alt.svg" />
    <Content Include="Styles\icon-font\svgs\arrow-right.svg" />
    <Content Include="Styles\icon-font\svgs\auszeichnung-default.svg" />
    <Content Include="Styles\icon-font\svgs\bars.svg" />
    <Content Include="Styles\icon-font\svgs\beach-volleyball.svg" />
    <Content Include="Styles\icon-font\svgs\bicycle.svg" />
    <Content Include="Styles\icon-font\svgs\blank-doc.svg" />
    <Content Include="Styles\icon-font\svgs\calendar-alt.svg" />
    <Content Include="Styles\icon-font\svgs\camera.svg" />
    <Content Include="Styles\icon-font\svgs\chevron-down.svg" />
    <Content Include="Styles\icon-font\svgs\chevron-left.svg" />
    <Content Include="Styles\icon-font\svgs\chevron-right.svg" />
    <Content Include="Styles\icon-font\svgs\chevron-thin-down.svg" />
    <Content Include="Styles\icon-font\svgs\chevron-thin-left.svg" />
    <Content Include="Styles\icon-font\svgs\chevron-thin-right.svg" />
    <Content Include="Styles\icon-font\svgs\chevron-thin-up.svg" />
    <Content Include="Styles\icon-font\svgs\chevron-up.svg" />
    <Content Include="Styles\icon-font\svgs\child.svg" />
    <Content Include="Styles\icon-font\svgs\circle-circle.svg" />
    <Content Include="Styles\icon-font\svgs\code-solid.svg" />
    <Content Include="Styles\icon-font\svgs\cog.svg" />
    <Content Include="Styles\icon-font\svgs\comment.svg" />
    <Content Include="Styles\icon-font\svgs\comments.svg" />
    <Content Include="Styles\icon-font\svgs\compare.svg" />
    <Content Include="Styles\icon-font\svgs\copy.svg" />
    <Content Include="Styles\icon-font\svgs\cutlery.svg" />
    <Content Include="Styles\icon-font\svgs\discoverize-logo.svg" />
    <Content Include="Styles\icon-font\svgs\download.svg" />
    <Content Include="Styles\icon-font\svgs\envelope-alt.svg" />
    <Content Include="Styles\icon-font\svgs\equalizer.svg" />
    <Content Include="Styles\icon-font\svgs\euro-5.svg" />
    <Content Include="Styles\icon-font\svgs\euro.svg" />
    <Content Include="Styles\icon-font\svgs\excel.svg" />
    <Content Include="Styles\icon-font\svgs\exclamation-sign.svg" />
    <Content Include="Styles\icon-font\svgs\eye.svg" />
    <Content Include="Styles\icon-font\svgs\facebook-light.svg" />
    <Content Include="Styles\icon-font\svgs\facebook-square.svg" />
    <Content Include="Styles\icon-font\svgs\flag.svg" />
    <Content Include="Styles\icon-font\svgs\frown.svg" />
    <Content Include="Styles\icon-font\svgs\gallery-dotted.svg" />
    <Content Include="Styles\icon-font\svgs\globe.svg" />
    <Content Include="Styles\icon-font\svgs\hand-shake-2.svg" />
    <Content Include="Styles\icon-font\svgs\heart-outline.svg" />
    <Content Include="Styles\icon-font\svgs\heart-full.svg" />
    <Content Include="Styles\icon-font\svgs\hexagon.svg" />
    <Content Include="Styles\icon-font\svgs\home.svg" />
    <Content Include="Styles\icon-font\svgs\hotel.svg" />
    <Content Include="Styles\icon-font\svgs\icon.png" />
    <Content Include="Styles\icon-font\svgs\image-line.svg" />
    <Content Include="Styles\icon-font\svgs\image.svg" />
    <Content Include="Styles\icon-font\svgs\info-sign.svg" />
    <Content Include="Styles\icon-font\svgs\instagram-light.svg" />
    <Content Include="Styles\icon-font\svgs\layout-footer.svg" />
    <Content Include="Styles\icon-font\svgs\layout-grid-fill.svg" />
    <Content Include="Styles\icon-font\svgs\layout-header.svg" />
    <Content Include="Styles\icon-font\svgs\layout-line.svg" />
    <Content Include="Styles\icon-font\svgs\leaf.svg" />
    <Content Include="Styles\icon-font\svgs\light-checkmark.svg" />
    <Content Include="Styles\icon-font\svgs\list-unordered.svg" />
    <Content Include="Styles\icon-font\svgs\location-arrow.svg" />
    <Content Include="Styles\icon-font\svgs\location-city.svg" />
    <Content Include="Styles\icon-font\svgs\lock.svg" />
    <Content Include="Styles\icon-font\svgs\mail-forward.svg" />
    <Content Include="Styles\icon-font\svgs\map-marker.svg" />
    <Content Include="Styles\icon-font\svgs\map-with-marker.svg" />
    <Content Include="Styles\icon-font\svgs\maximize.svg" />
    <Content Include="Styles\icon-font\svgs\meh.svg" />
    <Content Include="Styles\icon-font\svgs\minimize.svg" />
    <Content Include="Styles\icon-font\svgs\minus-sign.svg" />
    <Content Include="Styles\icon-font\svgs\minus.svg" />
    <Content Include="Styles\icon-font\svgs\my-location.svg" />
    <Content Include="Styles\icon-font\svgs\ok-sign.svg" />
    <Content Include="Styles\icon-font\svgs\ok.svg" />
    <Content Include="Styles\icon-font\svgs\pathfinder-with-text.svg" />
    <Content Include="Styles\icon-font\svgs\pathfinder.svg" />
    <Content Include="Styles\icon-font\svgs\pdf.svg" />
    <Content Include="Styles\icon-font\svgs\pen-nib-light.svg" />
    <Content Include="Styles\icon-font\svgs\pencil.svg" />
    <Content Include="Styles\icon-font\svgs\person-help.svg" />
    <Content Include="Styles\icon-font\svgs\phone-square.svg" />
    <Content Include="Styles\icon-font\svgs\phone.svg" />
    <Content Include="Styles\icon-font\svgs\pinterest.svg" />
    <Content Include="Styles\icon-font\svgs\plus-sign.svg" />
    <Content Include="Styles\icon-font\svgs\plus.svg" />
    <Content Include="Styles\icon-font\svgs\powerpoint.svg" />
    <Content Include="Styles\icon-font\svgs\print.svg" />
    <Content Include="Styles\icon-font\svgs\question-sign.svg" />
    <Content Include="Styles\icon-font\svgs\remove-sign.svg" />
    <Content Include="Styles\icon-font\svgs\remove-thin.svg" />
    <Content Include="Styles\icon-font\svgs\remove.svg" />
    <Content Include="Styles\icon-font\svgs\reply.svg" />
    <Content Include="Styles\icon-font\svgs\rocket.svg" />
    <Content Include="Styles\icon-font\svgs\rounded-checkbox.svg" />
    <Content Include="Styles\icon-font\svgs\rss.svg" />
    <Content Include="Styles\icon-font\svgs\search-plus.svg" />
    <Content Include="Styles\icon-font\svgs\search.svg" />
    <Content Include="Styles\icon-font\svgs\share--android.svg" />
    <Content Include="Styles\icon-font\svgs\share--apple.svg" />
    <Content Include="Styles\icon-font\svgs\share.svg" />
    <Content Include="Styles\icon-font\svgs\shopping-cart.svg" />
    <Content Include="Styles\icon-font\svgs\side-bar-line.svg" />
    <Content Include="Styles\icon-font\svgs\signal.svg" />
    <Content Include="Styles\icon-font\svgs\smile.svg" />
    <Content Include="Styles\icon-font\svgs\sort.svg" />
    <Content Include="Styles\icon-font\svgs\star.svg" />
    <Content Include="Styles\icon-font\svgs\star5.svg" />
    <Content Include="Styles\icon-font\svgs\sync.svg" />
    <Content Include="Styles\icon-font\svgs\table-line.svg" />
    <Content Include="Styles\icon-font\svgs\tag.svg" />
    <Content Include="Styles\icon-font\svgs\tags.svg" />
    <Content Include="Styles\icon-font\svgs\telegram.svg" />
    <Content Include="Styles\icon-font\svgs\th-list.svg" />
    <Content Include="Styles\icon-font\svgs\thumb-up.svg" />
    <Content Include="Styles\icon-font\svgs\time.svg" />
    <Content Include="Styles\icon-font\svgs\trash-o.svg" />
    <Content Include="Styles\icon-font\svgs\trash.svg" />
    <Content Include="Styles\icon-font\svgs\truck.svg" />
    <Content Include="Styles\icon-font\svgs\twitter.svg" />
    <Content Include="Styles\icon-font\svgs\upload.svg" />
    <Content Include="Styles\icon-font\svgs\user.svg" />
    <Content Include="Styles\icon-font\svgs\whatsapp.svg" />
    <Content Include="Styles\icon-font\svgs\word-doc.svg" />
    <Content Include="Styles\icon-font\svgs\wrench.svg" />
    <Content Include="Styles\icon-font\unused-icons\text.svg" />
    <Content Include="Styles\img\andrej-small.jpg" />
    <Content Include="Styles\img\bg-monitors.jpg" />
    <Content Include="Styles\img\body-bg.png" />
    <Content Include="Styles\img\Bundesland\laufen-im-saarland.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-baden-wuerttemberg-rechtenstein.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-bayern-muenchen.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-bayern-schloss-neuschwanstein.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-berlin-reichstag.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-brandenburg-cottbus.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-brandenburg.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-bremen-hafen.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-hamburg-hafen.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-hessen-frankfurt.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-mecklenburg-vorpommern-ostsee.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-mecklenburg-vorpommern-schwerin.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-niedersachsen-hannover-rathaus.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-niedersachsen-hannover.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-nordrhein-westfalen-koeln.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-rheinland-pfalz-bad-emsl.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-rheinland-pfalz-beilstein-mosel.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-sachsen-anhalt-naumburg.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-schleswig-holstein-luebeck.jpg" />
    <Content Include="Styles\img\Bundesland\laufen-in-thueringen-wartburg.jpg" />
    <Content Include="Styles\img\businesses-together.png" />
    <Content Include="Styles\img\check-white-14.png" />
    <Content Include="Styles\img\check-white-16.png" />
    <Content Include="Styles\img\close-16.png" />
    <Content Include="Styles\img\cross-white-14.png" />
    <Content Include="Styles\img\cross-white-16.png" />
    <Content Include="Styles\img\default-image-1200.jpg" />
    <Content Include="Styles\img\delete.gif" />
    <Content Include="Styles\img\discoverize-logo-32.png" />
    <Content Include="Styles\img\discoverize-logo.png" />
    <Content Include="Styles\img\facebook-logo-16.png" />
    <Content Include="Styles\img\favicon.ico" />
    <Content Include="Styles\img\fb_icon_22x22.png" />
    <Content Include="Styles\img\flags%402x.png" />
    <Content Include="Styles\img\flags.png" />
    <Content Include="Styles\img\flags.webp" />
    <Content Include="Styles\img\<EMAIL>" />
    <Content Include="Styles\img\glyphicons-halflings-white.png" />
    <Content Include="Styles\img\glyphicons-halflings.png" />
    <Content Include="Styles\img\google-logo-16.png" />
    <Content Include="Styles\img\header-left.jpg" />
    <Content Include="Styles\img\header-right.jpg" />
    <Content Include="Styles\img\header.jpg" />
    <Content Include="Styles\img\header.png" />
    <Content Include="Styles\img\hero-block-image-full-width.jpg" />
    <Content Include="Styles\img\hero-block-image.jpg" />
    <Content Include="Styles\img\hexagon-optimised.svg" />
    <Content Include="Styles\img\info-white-14.png" />
    <Content Include="Styles\img\info-white-16.png" />
    <Content Include="Styles\img\instagram-s.png" />
    <Content Include="Styles\img\jslider.plastic.png" />
    <Content Include="Styles\img\jslider.png" />
    <Content Include="Styles\img\loading-large.gif" />
    <Content Include="Styles\img\logo-bewertungs-widget.png" />
    <Content Include="Styles\img\logo.png" />
    <Content Include="Styles\img\logo-invoice.png" />
    <Content Include="Styles\img\logo-manage.png" />
    <Content Include="Styles\img\map-marker-blue.png" />
    <Content Include="Styles\img\map-marker-green.png" />
    <Content Include="Styles\img\map-marker-red.png" />
    <Content Include="Styles\img\map-marker.png" />
    <Content Include="Styles\img\map-placeholder.png" />
    <Content Include="Styles\img\marina-200.jpg" />
    <Content Include="Styles\img\minus-white-14.png" />
    <Content Include="Styles\img\minus-white-16.png" />
    <Content Include="Styles\img\New-images\banner-img.png" />
    <Content Include="Styles\img\pen-for-addthis.jpg" />
    <Content Include="Styles\img\People\gent-1.jpg" />
    <Content Include="Styles\img\People\lady-1.jpg" />
    <Content Include="Styles\img\People\lady-2.jpg" />
    <Content Include="Styles\img\People\woman-120.jpg" />
    <Content Include="Styles\img\question-white-14.png" />
    <Content Include="Styles\img\question-white-16.png" />
    <Content Include="Styles\img\reichstag-building.jpg" />
    <Content Include="Styles\img\star.gif" />
    <Content Include="Styles\img\transparent-75.png" />
    <Content Include="Styles\img\transparent.png" />
    <Content Include="Styles\img\tripadvisor-logo-16.png" />
    <Content Include="Styles\invoice.css" />
    <Content Include="Styles\portalmanagement-custom.css" />
    <Content Include="Styles\rating-widget.css" />
    <Content Include="Styles\neutral.css" />
    <Content Include="Styles\test.html" />
    <Content Include="Views\Page-Checkout.cshtml" />
    <Content Include="Views\Page-Erklärung-zur-Barrierefreiheit.cshtml" />
    <Content Include="Views\Page-Kontaktstelle-nach-DSA.cshtml" />
    <Content Include="Views\Text-Bausteine\welcome-tags.html" />
    <Content Include="Views\Text-Bausteine\welcome-image-boxes.html" />
    <Content Include="Views\_all-pages-must-be-defined-here.txt" />
    <Content Include="Views\_CanonicalAndAlternates.cshtml" />
    <Content Include="Web.config" />
    <Content Include="Views\Web.config" />
    <Content Include="Scripts\Web.config" />
    <Content Include="Styles\Web.config" />
    <Content Include="Theme.txt" />
    <Content Include="Theme.png" />
    <Content Include="Placement.info" />
    <Content Include="_app_offline.htm" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Document.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Layout.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Zone-Footer.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Contact.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ThemeImageSettings.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Orchard\Orchard.Framework.csproj">
      <Project>{2D1D92BB-4555-4CBE-8D0E-63563D6CE4C6}</Project>
      <Name>Orchard.Framework</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\Orchard.Core.csproj">
      <Project>{9916839C-39FC-4CEB-A5AF-89CA7E87119F}</Project>
      <Name>Orchard.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Modules\Teamaton.Discoverize\Teamaton.Discoverize.csproj">
      <Project>{67159370-aca5-4165-a04e-3a3f99e31869}</Project>
      <Name>Teamaton.Discoverize</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Modules\Teamaton.Search\Teamaton.Search.csproj">
      <Project>{c4623357-b983-402b-827e-14a1593615b2}</Project>
      <Name>Teamaton.Search</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\LoginStatus.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ErrorPage.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\Web.config" />
    <Content Include="Styles\icon-fonts\fonts\icomoon.eot" />
    <Content Include="Styles\icon-fonts\fonts\icomoon.ttf" />
    <Content Include="Styles\icon-fonts\fonts\icomoon.woff" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\NotFound.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\icon-fonts\selection.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_custom.scss" />
    <Content Include="Styles\_icon-fonts.scss" />
    <Content Include="Styles\_imports.scss" />
    <Content Include="Styles\_reset.scss" />
    <Content Include="Styles\_respond.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\1_base\_clearing-floats.scss" />
    <Content Include="Styles\1_base\_gradients.scss" />
    <Content Include="Styles\1_base\_layout.scss" />
    <Content Include="Styles\1_base\_links.scss" />
    <Content Include="Styles\1_base\_lists.scss" />
    <Content Include="Styles\1_base\_opacity.scss" />
    <Content Include="Styles\1_base\_rounded-corners.scss" />
    <Content Include="Styles\1_base\_shadows.scss" />
    <Content Include="Styles\1_base\_spacing.scss" />
    <Content Include="Styles\1_base\_transform.scss" />
    <Content Include="Styles\1_base\_transitions.scss" />
    <Content Include="Styles\1_base\_typography.scss" />
    <Content Include="Styles\1_base\__normalize.scss" />
    <Content Include="Styles\2_elements\_blockquotes.scss" />
    <Content Include="Styles\2_elements\_border-heading.scss" />
    <Content Include="Styles\2_elements\_buttons.scss" />
    <Content Include="Styles\2_elements\_caret.scss" />
    <Content Include="Styles\2_elements\_circle.scss" />
    <Content Include="Styles\2_elements\_flexbox.scss" />
    <Content Include="Styles\2_elements\_icons.scss" />
    <Content Include="Styles\2_elements\_image-display.scss" />
    <Content Include="Styles\2_elements\_indent-line.scss" />
    <Content Include="Styles\2_elements\_info-text.scss" />
    <Content Include="Styles\2_elements\_stripes.scss" />
    <Content Include="Styles\2_elements\_triangle.scss" />
    <Content Include="Styles\2_elements\_wrapper.scss" />
    <Content Include="Styles\3_blocks\_bars.scss" />
    <Content Include="Styles\3_blocks\_columns.scss" />
    <Content Include="Styles\3_blocks\_comment-callout.scss" />
    <Content Include="Styles\3_blocks\_crumbtrail.scss" />
    <Content Include="Styles\3_blocks\_datepicker.scss" />
    <Content Include="Styles\3_blocks\_dropdown-select.scss" />
    <Content Include="Styles\3_blocks\_forms-new.scss" />
    <Content Include="Styles\3_blocks\_gallery.scss" />
    <Content Include="Styles\3_blocks\_hexagon.scss" />
    <Content Include="Styles\3_blocks\_loading.scss" />
    <Content Include="Styles\3_blocks\_media.scss" />
    <Content Include="Styles\3_blocks\_messages.scss" />
    <Content Include="Styles\3_blocks\_pager.scss" />
    <Content Include="Styles\3_blocks\_popdrop.scss" />
    <Content Include="Styles\3_blocks\_popups.scss" />
    <Content Include="Styles\3_blocks\_quote.scss" />
    <Content Include="Styles\3_blocks\_ratings.scss" />
    <Content Include="Styles\3_blocks\_readmore.scss" />
    <Content Include="Styles\3_blocks\_score-icon.scss" />
    <Content Include="Styles\3_blocks\_tables.scss" />
    <Content Include="Styles\3_blocks\_tabs.scss" />
    <Content Include="Styles\3_blocks\_tagcloud.scss" />
    <Content Include="Styles\3_blocks\_yes-no-list.scss" />
    <Content Include="Styles\neutral.scss" />
    <Content Include="Styles\_category-icons.scss" />
    <Content Include="Styles\_icon-fonts-settings.scss" />
    <Content Include="Styles\_style-guide.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ErrorPages\Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\1_base\_sticky-footer.scss" />
    <Content Include="Styles\3_blocks\_content-management-guide.scss" />
    <Content Include="Styles\3_blocks\_map-marker-buttons.scss" />
    <Content Include="Styles\3_blocks\_premium-block.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\icon-fonts\fonts\dz-manage.eot" />
    <Content Include="Styles\icon-fonts\fonts\dz-manage.ttf" />
    <Content Include="Styles\icon-fonts\fonts\dz-manage.woff" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_fake-tables.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\rating-widget.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_variables.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_EmbedWidgetBlock.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_RatingWidgetWithoutRatings.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_RatingWidgetWithRatings.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\1_base\_color-shades.scss" />
    <Content Include="Styles\2_elements\_button-close.scss" />
    <Content Include="Styles\3_blocks\_picture-list.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_modern-homepage.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_footer.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_default-variables.scss" />
    <Content Include="Styles\_rating-widget-master.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_collapser.scss" />
    <Content Include="Styles\3_blocks\_overflow.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_EmailInformation.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_entry-cards-list.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_internet-explorer.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_filepond-custom.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_premium-products.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_flat-nav.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_MobileNavigation.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\2_elements\_grid.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\2_elements\_checkbox-custom.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_old-search-layout-tiny-images.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_MobileSearchActionBar.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_colorful-cards.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\2_elements\_switch-custom.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\2_elements\_full-width-switch.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\2_elements\_slider.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_search-page--base.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_counter-numbers.scss" />
    <Content Include="Styles\3_blocks\_factbox-ribbon.scss" />
    <Content Include="Styles\3_blocks\_hero-image-block.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_sides-block.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_icon-font-codepoints.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\icon-font\neutral-icons.eot" />
    <Content Include="Styles\icon-font\neutral-icons.ttf" />
    <Content Include="Styles\icon-font\neutral-icons.woff" />
    <Content Include="Styles\icon-font\neutral-icons.woff2" />
    <Content Include="Styles\icon-font\_icon-font.scss" />
    <Content Include="Styles\icon-font\_neutral-icons.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Page-Agb.cshtml" />
    <Content Include="Views\Page-Award.cshtml" />
    <Content Include="Views\Page-Content-Management-Guide.cshtml" />
    <Content Include="Views\Page-Datenschutzerklärung.cshtml" />
    <Content Include="Views\Page-Design-Vorlagen.cshtml" />
    <Content Include="Views\Page-Hilfe-Embedded-Media.cshtml" />
    <Content Include="Views\Page-Hilfe.cshtml" />
    <Content Include="Views\Page-Homepage.cshtml" />
    <Content Include="Views\Page-Impressum.cshtml" />
    <Content Include="Views\Page-Newsletter.cshtml" />
    <Content Include="Views\Page-Punkte.cshtml" />
    <Content Include="Views\Page-Search.cshtml" />
    <Content Include="Views\Page-StyleGuide.cshtml" />
    <Content Include="Views\Page-TermsAndConditionsPremium.cshtml" />
    <Content Include="Views\Page-Tipps-für-Bewertungen.cshtml" />
    <Content Include="Views\Page-Widget.cshtml" />
    <Content Include="Views\Page-_Premium.cshtml" />
    <Content Include="Views\Page-Über-Uns.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\fonts\open-sans-v17-latin-300.eot" />
    <Content Include="Styles\fonts\open-sans-v17-latin-300.ttf" />
    <Content Include="Styles\fonts\open-sans-v17-latin-300.woff" />
    <Content Include="Styles\fonts\open-sans-v17-latin-300.woff2" />
    <Content Include="Styles\fonts\open-sans-v17-latin-600.eot" />
    <Content Include="Styles\fonts\open-sans-v17-latin-600.ttf" />
    <Content Include="Styles\fonts\open-sans-v17-latin-600.woff" />
    <Content Include="Styles\fonts\open-sans-v17-latin-600.woff2" />
    <Content Include="Styles\fonts\open-sans-v17-latin-700.eot" />
    <Content Include="Styles\fonts\open-sans-v17-latin-700.ttf" />
    <Content Include="Styles\fonts\open-sans-v17-latin-700.woff" />
    <Content Include="Styles\fonts\open-sans-v17-latin-700.woff2" />
    <Content Include="Styles\fonts\open-sans-v17-latin-regular.eot" />
    <Content Include="Styles\fonts\open-sans-v17-latin-regular.ttf" />
    <Content Include="Styles\fonts\open-sans-v17-latin-regular.woff" />
    <Content Include="Styles\fonts\open-sans-v17-latin-regular.woff2" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_functions.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_include-media.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_sidebar.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_cookies.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_cards-grid.scss" />
    <Content Include="Styles\3_blocks\_cards-with-circle.scss" />
    <Content Include="Styles\3_blocks\_hero-entry-banner.scss" />
    <Content Include="Styles\3_blocks\_faq.scss" />
    <Content Include="Styles\3_blocks\_fb-buttons.scss" />
    <Content Include="Styles\3_blocks\_modal.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_logos.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_HeaderNavigationOld.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\2_elements\_pills.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_MainWrap.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\1_base\_browsers.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterNavigationOld.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterTopOld.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterLogoTop.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterBottom.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterClaim.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_FooterLogoFirstColumn.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_select-box.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_search-page--filters-availability.scss" />
    <Content Include="Styles\4_layouts\_search-page--filters-top.scss" />
    <Content Include="Styles\4_layouts\_search-page--filters.scss" />
    <Content Include="Styles\4_layouts\_search-page--info-over-image.scss" />
    <Content Include="Styles\4_layouts\_search-page--map.scss" />
    <Content Include="Styles\4_layouts\_search-page--results.scss" />
    <Content Include="Styles\4_layouts\_search-page--tabs.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_search-page--adblocks.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_search-page--layout.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_search-page--header.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_search-page--grid.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_availability-calendar.scss" />
    <Content Include="Styles\4_layouts\_camping-bayern-ampel.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_global-search.scss" />
    <Content Include="Styles\4_layouts\_header.scss" />
    <Content Include="Styles\4_layouts\_picture-gallery.scss" />
    <Content Include="Styles\4_layouts\_search-filters-left.scss" />
    <Content Include="Styles\4_layouts\_search-page--active-filters.scss" />
    <Content Include="Styles\4_layouts\_search-page--all-filters.scss" />
    <Content Include="Styles\4_layouts\_search-page--filters-left.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_search-page--grid-variables.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Page-Design-Testing.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_content-left-aligned.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_ratings-as-numbers.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_mobile-action-panel.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_entry-type-child-popup.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_tables-sticky-header.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_blog-slider.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\2_elements\_link-box-with-transparency.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_language-picker.scss" />
    <Content Include="Styles\4_layouts\_mini-search.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\1_base\_color-contrast.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_header-mobile.scss" />
    <Content Include="Styles\4_layouts\_header-old.scss" />
    <Content Include="Styles\4_layouts\_header-sticky.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_entry-page--refactor-this.scss" />
    <Content Include="Styles\4_layouts\_entry-page.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_premium-pricing-table.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\_default-custom-variables.scss" />
    <Content Include="Styles\_samsung-browser-fixes.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\1_base\_animations.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_entry-page--mobile-header.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_global-author-widget.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_global-redesign.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_thematica-global-template-style.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\3_blocks\_category-icons.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\4_layouts\_thematica-global-template-style-variables.scss" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_DynamicContrastStyle.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\1_base\_color-contrast-new.scss" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets. -->
  <Target Name="BeforeBuild" DependsOnTargets="ValidateContentFiles">
  </Target>
  <Target Name="ValidateContentFiles">
    <Error Condition="!Exists(%(Content.FullPath))" Text="Missing Content file [%(Content.FullPath)]" />
  </Target>
  <Target Name="AfterBuild" DependsOnTargets="AfterBuildCompiler">
    <PropertyGroup>
      <AreasManifestDir>$(ProjectDir)\..\Manifests</AreasManifestDir>
    </PropertyGroup>
    <!-- If this is an area child project, uncomment the following line:
    <CreateAreaManifest AreaName="$(AssemblyName)" AreaType="Child" AreaPath="$(ProjectDir)" ManifestPath="$(AreasManifestDir)" ContentFiles="@(Content)" />
    -->
    <!-- If this is an area parent project, uncomment the following lines:
    <CreateAreaManifest AreaName="$(AssemblyName)" AreaType="Parent" AreaPath="$(ProjectDir)" ManifestPath="$(AreasManifestDir)" ContentFiles="@(Content)" />
    <CopyAreaManifests ManifestPath="$(AreasManifestDir)" CrossCopy="false" RenameViews="true" />
    -->
  </Target>
  <Target Name="AfterBuildCompiler" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(ProjectDir)\..\$(ProjectName)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>9534</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>
          </IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>http://orchard.codeplex.com</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
  <Import Project="..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
</Project>