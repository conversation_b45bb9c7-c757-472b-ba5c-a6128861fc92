﻿@*
    beware: this file is used for all displayed pages in discoverize portal, even management pages
*@
@using System.Globalization
@using Orchard.UI.Resources
@using Teamaton.Discoverize.Feature.FeatureToggle
@using Teamaton.Discoverize.Internationalization.Resources
@using Teamaton.Discoverize.Tracking.Events
@using Teamaton.Discoverize.Utility
@{
    Script.Require("ie11-polyfills").AtHead();
    Script.Require("jQuery").AtHead();
    <!-- script for responsive design in ie: https://github.com/scottjehl/Respond -->
    Script.Require("responsive").AtHead();
    Script.Require("debounce").AtFoot();
    Script.Require("jQuery_Notifier").AtFoot();
    Script.Require("jQuery_ScrollTo").AtFoot();
    Script.Require("jQuery_Cookie").AtFoot();
    Script.Require("jQuery_Inview").AtFoot();
    Script.Require("safe-storage").AtFoot();
    Script.Require("search-near-me").AtFoot();
    Script.Require("iframe-video-resizer").AtFoot();
    Script.Require("stickyfill").AtFoot();
    Script.Require("stickyfill-addon").AtFoot();
    Script.Require("bootstrap_Tabs").AtFoot();
    Script.Require("bootstrap_Carousel").AtFoot();
    Script.Require("tn.backToTop").AtFoot();
    Script.Require("mobile-sticky-elements").AtFoot();
    Script.Require("fitty").AtFoot();
    Script.Require("faq").AtFoot();
    Script.Require("shuffle-cards").AtFoot();
    Script.Require("msg-close").AtFoot();
    Script.Require("jq-fully-responsive-cards").AtFoot();
    Script.Require("tabs-to-dropdown-transition").AtFoot();
    Script.Require("jQuery_ReadMore").AtFoot();
    Script.Require("swipable").AtFoot();
    Script.Require("viewport").AtFoot();
    Script.Require("mobile-desktop-switch").AtFoot();
    Script.Require("keep-lang").AtFoot();
    Script.Require("header-navigation").AtFoot();
    // need to execute this script before jquery.tn.searchbox-suggestions.js
    Script.Require("sticky-header").AtHead();

    if (Html.ShowDiscoverizeConsentPopup()) {
        Script.Require("cookie-consent").AtFoot();
    }

    var eventRenderer = WorkContext.Resolve<IEventRenderer>();
    RegisterLink(new LinkEntry
    {
        Type = "image/x-icon",
        Rel = "shortcut icon",
        Href = Url.HashedInTheme("/styles/img/favicon.ico")
    });
}
<!DOCTYPE html>
<!-- paulirish.com/2008/conditional-stylesheets-vs-css-hacks-answer-neither/ -->
<!--[if lt IE 7 ]> <html class="no-js ie6" lang="de"> <![endif]-->
<!--[if IE 7 ]>    <html class="no-js ie7" lang="de"> <![endif]-->
<!--[if IE 8 ]>    <html class="no-js ie8" lang="de"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!-->
<html lang="@WorkContext.CurrentLang" class="static no-js search-fullwidth">
    <!--<![endif]-->
    <head>
        <meta charset="utf-8" />
        <meta name="rendered-at" content="@(DateTime.Now.ToString("G", CultureInfo.GetCultureInfo("de")))" />
        <meta name="color-scheme" content="light only">

        <title>@Html.Title()</title>

        <!-- Always force latest IE rendering engine (even in intranet) & Chrome Frame Remove this if you use the .htaccess -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

        <!-- Mobile viewport optimized: j.mp/bplateviewport -->
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1">
        
        @*
        <!-- Allow passing an HTTP REFERER to URLs over http:// -->
        <!-- Check https://w3c.github.io/webappsec-referrer-policy/#referrer-policies for valid values -->
        <!-- Check http://caniuse.com/#feat=referrer-policy for browser support -->
        *@
        <meta name="referrer" content="origin">
        <meta name="referrer" content="origin-when-cross-origin">

        @*
        <!-- Use 114px x 114px for iOS: http://www.1stwebdesigner.com/design/snippets-html5-boilerplate/#comment-63353 -->
        <link rel="apple-touch-icon" href="/apple-touch-icon.png">
        *@
        
        @if (Html.IsFeatureEnabled<IUseRealUserManagementFeature>()) {
            <!-- Do not place any scripts above this script in the head tag -->
            <script src="https://stckjs.stackify.com/stckjs.js" 
                    data-host="https://rum.stackify.com" 
                    data-key="086K4s_o00mXULKJTpsRKw" async></script>
        }

        @Html.Partial("_CanonicalAndAlternates")

        @Display(Model.Head)
       
        <script>
            // checks if JavaScript is enabled - replaces 'static' by 'dyn' in class attribute of html root node
            (function (d) { d.className = "dyn " + d.className.substring(7, d.length); })(document.documentElement);

            //@* For syntax explanation: see http://mathiasbynens.be/notes/async-analytics-snippet *@
            function loadAsync(url) {
                const d = document, t = 'script', g = d.createElement(t), s = d.getElementsByTagName(t)[0];
                g.async = true;
                g.src = '//' + url;
                s.parentNode.insertBefore(g, s);
            }

            // environment detection
            const hostname = window.location.hostname,
                isDev = hostname === 'dz' || hostname === 'discoverize' || hostname === 'localhost',
                isStage = hostname === 'stage.discoverize.com',
                isDebug = window.location.search.indexOf('debug=true') > 0;

            // set window.DEBUG flag
            window.DEBUG = isDev || isStage || isDebug;

            // turn off console - unclear why we override the normal console (commit from Oliver on 31.03.2015 13:22)
            function noop () {}
            if (!window.DEBUG) {
                window.console_old = window.console;
                window.console = { log: noop, warn: noop, debug: noop, error: noop, info: noop };
            }
            
        </script>
    </head>
    <body class="@Context.Items["PageTypeBodyClass"]">

        @Display(Model.Body)
        @Display(Model.Tail)

        <div class="btt-container btt-container--intersecting">
            <div class="btt jq-back-to-top">
                <span class="btt1">
                    @RsCommon.ToTheTop(TR)
                </span>
            </div>
        </div>
        <div class='mnb jq-mobile-navigation-backdrop'></div>
        <div class="mobile-viewport-marker" tabindex="-1"></div>
        
        @Html.Partial("_DynamicContrastStyle")
        
        <script>
            $(function () {

                // fitty: fit text to parent container size: https://github.com/rikschennink/fitty
                window.fitty && fitty('.cb-card-visual i');

                $('.jq-search-nearby-footer').on("click", function () {
                    @eventRenderer.RenderEvent(GoogleAnalyticsEvent.SearchNearby.SearchNearFooter())
                });

                // triggered on in viewport: animate numbers count (content block "Zahlen")
                // counter js: http://www.i-visionblog.com/2014/11/jquery-animated-number-counter-from-zero-to-value-jquery-animation.html
                // trigger on in viewport js: https://github.com/protonet/jquery.inview
                $('.jq-cb-numbers-item-count')
                    .data('isAnimating', false)
                    .on('inview', function (event, isInView) {
                        const $this = $(this);
                        if (isInView && !$this.data('isAnimating')) {
                            $this.prop('counter', 0).data('isAnimating', true).animate({
                                counter: $this.text().replace(/\./g, '')
                            }, {
                                duration: 2000,
                                easing: 'swing',
                                step: function (now) {
                                    $this.text(Math.ceil(now).toLocaleString('de-DE'));
                                },
                                complete: function () {
                                    $this.data('isAnimating', false);
                                }
                            });
                        }
                    });

                // current navigation highlight
                $('.nav a[href="' + window.location.pathname + '"]').addClass("current");

                // browser sniffing for stock android and fixing rating popup display
                const nua = navigator.userAgent;
                const is_android = nua.indexOf('Mozilla/5.0') > -1 && nua.indexOf('Android ') > -1 &&
                    nua.indexOf('AppleWebKit') > -1 && nua.indexOf('Chrome') === -1;
                if (is_android) {
                    $('body').addClass('modal-android');
                }

                // track page views
                @if (Context.Items.Contains("PageType")) {
                    <text>
                    $.post("@Url.Action("TrackPageView", "Tracking", new { area = "Teamaton.Discoverize" })",
                        {
                            entryId: '@Context.Items["EntryId"]',
                            pageType: '@Context.Items["PageType"]',
                            __RequestVerificationToken: '@Html.AntiForgeryTokenValueOrchard()'
                        }
                    );
                    </text>
                }
            });
        </script>

        @if (PortalEnvironment.IsLocalDev) {
            // if image cannot be loaded locally load it from live domain
            <script>
                const liveDomain = '@Html.SuperizedDomain()';
                const localDomain = location.protocol + '//' + location.host;

                function setImageDomainsToLiveWithProtocol() {
                    $('img[src^="/img/"],img[src^="/Media/"],img[src^="/img-embedded/"]').each(function (idx, elm) {
                        const src = $(elm).attr('src');
                        const newSrc = liveDomain + src;
                        $(elm).attr('src', newSrc);
                    });
                    // Set srcset directly to live domain because we can not react to error
                    $('source[srcset^="/img/"], img[srcset^="/img/"]').each(function (idx, elm) {
                        const srcset = $(elm).attr('srcset');
                        const newSrcset = srcset.replace(/\/img\//g, liveDomain + '/img/');
                        $(elm).attr('srcset', newSrcset);
                    });
                    // Set srcset directly to live domain because we can not react to error
                    $('source[srcset^="/img-embedded/"], img[srcset^="/img-embedded/"]').each(function (idx, elm) {
                        const srcset = $(elm).attr('srcset');
                        const newSrcset = srcset.replace(/\/img-embedded\//g, liveDomain + '/img-embedded/');
                        $(elm).attr('srcset', newSrcset);
                    });
                    // Set srcset directly to live domain for static maps because we can not react to error
                    $('source[srcset^="/Media/Default/"]').each(function (idx, elm) {
                        const srcset = $(elm).attr('srcset');
                        const newSrcset = srcset.replace(/\/Media\/Default\//g, liveDomain + '/Media\/Default/');
                        $(elm).attr('srcset', newSrcset);
                    });
                }

                function registerImageErrors() {
                    $('img')
                        .off('error')
                        .one('error', function () {
                            const $this = $(this);
                            const src = $this.attr('src');
                            if (src.indexOf(localDomain) < 0) {
                                const newSrc = localDomain + src.substring(src.indexOf('/', 8));
                                $(this).attr('src', newSrc);
                            }

                            $this.siblings('source').each(function (idx, elm) {
                                const srcset = $(elm).attr('srcset');
                                const newSrcset = srcset.replace(liveDomain + '/img/', localDomain + '/img/')
                                    .replace(liveDomain + '/img-embedded/', localDomain + '/img-embedded/');
                                $(elm).attr('srcset', newSrcset);
                            });
                        });
                }

                $(function () {
                    function tryToFixImages() {
                        setImageDomainsToLiveWithProtocol();
                        registerImageErrors();
                    }

                    tryToFixImages();
                    $(document).on(
                        'search-results-updated map-popup-opened search-box-suggestions-loaded child-popup-opened',
                        tryToFixImages);
                });
            </script>
        }

        @if (Html.ShowDiscoverizeConsentPopup()) {
            <!-- Cookie Consent by https://www.FreePrivacyPolicy.com -->
            <script type="text/javascript">
                $(function () {
                    $(document)
                        .on(
                            'cc_preferencesSavePressed cc_dailogOkPressedAndCookiesEnabled ' +
                            'cc_dailogDeclinePressedAndCookiesDisabled ',
                            function () {
                                const consentLogData = {
                                    consentUrl: window.location.href,
                                    consentState: JSON.stringify(cookieconsent.consentDebugger.userConsent.acceptedLevels),
                                    __RequestVerificationToken: '@Html.AntiForgeryTokenValueOrchard()'
                                };
                                $.post('@Url.Action("Save", "CookieConsent", new { area = "Teamaton.Discoverize" })',
                                    consentLogData,
                                    function (json) {
                                        if (json.consentKey) {
                                            $.cookie('cookie_consent_derived_key_dz', json.consentKey + '_' + Date.now(),
                                                { path: '/', expires: 365 /* days */ });
                                        }
                                    },
                                    'json');
                            });

                    cookieconsent.run({
                        "notice_banner_type": "interstitial",
                        "consent_type": "express",
                        "palette": "light",
                        "language": "de",
                        "website_name": "@Html.PortalName()",
                        "cookies_policy_url": "/datenschutzerklärung",
                        "change_preferences_selector": ".open-cookie-consent-tool"
                    });
                });
            </script>
            <!-- End Cookie Consent -->
        }

        <script>
            $(function() {
                $(document).on('click', ".nav-searchbox-inner", function () {
                   $(this).addClass('nav-searchbox-inner--active');
                    $(document).on('click', function (e) {
                        if (!$(e.target).hasClass('sb1')) {
                            $('.nav-searchbox-inner--active').removeClass('nav-searchbox-inner--active');
                        }
                    });
                });

                //for testing child popup only
                //$('.jq-child-entry').first().click();

                function initReadmore() {
                    $('.readmore').each(function () {
                        const $this = $(this);
                        const actualMaxHeight = $this.height();
                        let maxHeightForReadMore = 200;
                        if (actualMaxHeight > 0) {
                            maxHeightForReadMore = actualMaxHeight;
                        }
                        $this.readmore({
                            speed: 100,
                            maxHeight: maxHeightForReadMore,
                            moreLink: '<a href="#" aria-expanded="false" class="rm11">@RsCommon.OpenReadMore(TR)</a>',
                            lessLink: '<a href="#" aria-expanded="true" class="rm12">@RsCommon.CloseReadMore(TR)</a>',
                            sectionCSS: ''
                        });
                    });
                }

                initReadmore();
                $(document).on('child-popup-opened', initReadmore);
                /*// for testing global search suggestions only*/
                /*$('.tt-input').on('blur', function (ev) { $('.tt-input').typeahead('open'); });*/
            });
        </script>

       @* scroll navigation if too many elements and show scroll option *@
        <script>
            $(function () {
                const scrollClass = 'nav-level-3--scroll';
                $('.jq-header-bar .nav-level-3').each(function (index) {
                    const $element = $(this);
                    // scenario: scrollHeight is 280 and outerHeight is 279.844 -> add 1 to outerHeight
                    const isOverflowing = $element.prop('scrollHeight') > $element.outerHeight() + 1;
                    if (isOverflowing) {
                        $element.addClass(scrollClass);
                        $('.sticky-header .nav-level-3').eq(index).addClass(scrollClass);
                    }
                })
            });


            //$(document).ready(function () {
            //    if ($('.nav-level-3').height() > 350) {
            //        $('.nav-level-3').addClass("nav-level-3--scroll");
            //    }
            //});
        </script>

        @*for testing global search suggestions  only*@
        @*<style>
            .mhs0 { opacity: 1 !important; }
        </style>*@
    </body>
</html>