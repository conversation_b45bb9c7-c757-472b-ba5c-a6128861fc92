﻿/* hundehotel theme for discoverize.com portal - created 6.05.16 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "default-variables";
@import "variables";
@import "4_layouts/_thematica-global-template-style-variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
@import "4_layouts/_thematica-global-template-style";

/* end import */

/* overwrites */

/* graphics */

body {
    position: relative;
    background: #ffffff;
    font-family: trebuchet ms, sans-serif;
}

//Cookie consent link to Datenschutzerklärung
.cc12 {
    color: $primary-lighter;
}
/* end graphics */

/* type */

h1,
h2,
h3,
h4,
h5, 
h6 {
    color: $secondary-darker;
}

/* end type */

/* layout */

.header-top {
    background: #f4f4f4;
}
.tagline {
    text-align: center;
    color: #666;
    padding: 0;
    font-size: 0.955em;
    float: left;
    clear: left;
}

.header-main {
    @include shadow-no-top-s-light;
    .home & {
        @include shadow-remove;
        @include media(">page-width") {
            background: transparent;
        }
    }
}

.logo {
    margin-bottom: 16px;
    margin-top: 16px;
}

.logo-graphic {
    width: 223px;
    @include media("<=mobile-ui") {
        height: 50px;
    }
}

// homepage

.home {
    .header {
        position: absolute;
        width: 100%;
        background: none; 
    }
    .header-main {
        background: transparent;
        box-shadow: none;
    }
    .header-top {
        background: none;
    }
}
.home.scrolling-down, .home.scrolled-down, .home.scrolling-up {
    .header-main { 
        background: #fff;
    }
}

.global-search {
    @include global-search(190px);
    padding: 6px 3px 6px 0;
    color: #ccc; 
}


@include mini-search-full-page(#fff, "img/wellness-bg-2.jpg", (#fff, 0.7));


.mini-search.mini-search {  
    min-height: 60vh;
    &:before {
        display:none;
    }
    @include media("<=mobile-ui") {
        height: 600px !important;
        align-items: start;
        padding-top: 120px;
    }

    @include media("<=479px") {
        .ms12{
            flex-grow: 1;
            justify-content: center;
            width: auto;
            
            .mc32{
                width: 100%;
                padding: 0 .25em;
            }
        }
    }
}
 
.mini-search-entry-type {
    background: none; 
    @include media("<=mobile-ui"){
        padding-bottom: 2em;
    }
}

.mini-search-entry-type {
    padding-bottom: 8px;
} 
.ms10 {
    text-shadow: 1px 1px 0.1em #fff;
    color: $secondary;
    font-size: 64px;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

.ms25 {
    background-color: $primary;
    background-image: unset;
}
.ms261 {
    font-size: 20px;
}
.ms12 {
    > label,
    > span {
        color: #fff;
        @include text-background-offset(#666);
    }
}

.ms23 {
    text-align: center;
    display: none;
}
.ms26 {
    @include hide-visually;
}

/* search page */
.premium-booked {
    /*@include stripes-close(transparentize($primary, 0.75));*/
}
.sp150 {
    font-size: 1.05em;
}
.sf61 {
    font-size: 0.9em;
}

.sf61-8, .epf61-8 { 
    @extend .icon-home;
}
.sf61-9, .epf61-9 { 
    @extend .icon-cutlery;
}
.sf61-10, .epf61-10 { 
    @extend .icon-hotel;
}
.sf61-11, .epf61-11 { 
    @extend .icon-steamroom;
}

.sf61-12, .epf61-12 { 
    @extend .icon-massage;
}
.sf61-13, .epf61-13 { 
    @extend .icon-aroma-therapy;
}
.sf61-14, .epf61-14 { 
    @extend .icon-leistungen_white;
}
.sf61-15, .epf61-15 { 
    @extend .icon-swimming-pool;
}
.sf61-16, .epf61-16 { 
    @extend .icon-location-arrow;
}

.map-marker {
    @include map-marker(30px, #fff, $primary);
}
.map-marker-primary,
.map-marker-active {
    @include map-marker(30px, darken($primary, 20), #fff, darken($primary, 30));
}
.map-marker-premium {
    @include map-marker(38px, $primary, $txt-on-primary, #fff, 2px);
    &.map-marker-active {
        @include map-marker(
            38px,
            darken($primary, 12),
            $txt-on-primary,
            darken($primary, 30),
            2px
        );
    }
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-wellnesshotels;
    &:before {
        top: 5px;
    }
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-wellnesshotels;
        &:before {
            font-size: 21px;
            left: 0px;
            top: 6px;
        }
    }
}
.map-marker-premium span.map-marker-icon {
    &:before {
        left: 2px;
        top: 7px;
        font-size: 26px;
    }
}

// award

// search page

/*.spea1 {
    top: 0;
}*/
.spea2 {
    width: 380px;
}
/*.spea12 {
    border-radius: 0 0 0 18px;
    width: 76px;
    height: 76px;
}
.spea13 {
    background: url("img/wellness-hotel-award-64.png") no-repeat;
    width: 64px;
    height: 64px;
    margin: 6px;
    display: inline-block;
    &:before {
        display: none;
    }
}
.spea21 {
    background: url("img/wellness-hotel-award-64.png") no-repeat;
    min-height: 42px;
    display: inline-block;
    content: none;
    &:before {
        display: none;
    } 
}*/

/* entry page */
/*
.epea1 {
    top: 0;
    bottom: none;
}
.epea2 {
    width: 380px;
}
.epea12 {
    border-radius: 0 0 0 18px;
    width: 76px;
    height: 76px;
}
.epea13 {
    background: url("img/wellness-hotel-award-64.png") no-repeat;
    width: 64px;
    height: 64px;
    margin: 6px;
    display: inline-block;
    &:before {
        display: none; 
    }
}
.epea21 {
    background: url("img/wellness-hotel-award-64.png") no-repeat;
    padding-left: 72px;
    min-height: 42px;
    display: inline-block;
    content: none;
    &:before {
        display: none;
    }
}*/

// end award

/* kategorie icons */
/* hotel kategorie */
// 1
.Klassifizierung_1 {
    @include category-stars(1);
}
// 2
.Klassifizierung_2 {
    @include category-stars(2);
}
// 3
.Klassifizierung_3 {
    @include category-stars(3);
}
// 3s
.Klassifizierung_4 {
    @include category-stars(3);
    &:after {
        content: "S";
    }
}
// 4
.Klassifizierung_5 {
    @include category-stars(4);
}
// 4s
.Klassifizierung_6 {
    @include category-stars(4);
    &:after {
        content: "S";
    }
}
// 5
.Klassifizierung_7 {
    @include category-stars(5);
}
// 5s
.Klassifizierung_8 {
    @include category-stars(5);
    &:after { 
        content: "S";
    }
}
.Klassifizierung_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

/* preisniveau */
// €
.Preisniveau_1 {
    @include category-euros(1);
}
// €€
.Preisniveau_2 {
    @include category-euros(2);
}
// €€€
.Preisniveau_3 {
    @include category-euros(3);
}
// €€€€
.Preisniveau_4 {
    @include category-euros(4);
}
.Preisniveau_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}
.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}

/* end kategorie icons */

.design-guide {
    border-bottom: solid 1px #ccc;
}

footer {
    background: #00798f;
    position: relative;
    border-radius: $rounded-corner-large-3 $rounded-corner-large-3 0 0;
}

.footer-top {
    @include footer-top("", lighten($secondary, 20));
    h3,
    h4 {
        color: darken($secondary-darker, 10);
    }
    h3 {
        font-size: 2em;
    }
    h4 {
        font-size: 1.6em;
    }
    .footer-top12 {
        @extend .button-primary, .button--l, .icon-arrow-right;
        margin-top: 3em;
    }
}

.footer-logo {
    padding: 30px 20px 40px;
}
.foot-logo {
    @include group;
    display: block;
    float: none;
    margin-bottom: 1em;
    .tagline {
        width: 100%;
        color: #ccc;
    }
    .logo-name {
        color: #ccc;
    }
}
.foot-nav {
    width: 20%;
    a {
        color: #fff;
    }
}

.welcome-inside:after,
.explained-inside:after {
    background: url(img/divider.png) no-repeat;
    display: block;
    height: 0;
    width: 100%;
    padding-top: calc(4% + 4em);
    padding-bottom: 4em;
    background-position: 50% 50%;
}

.pbi12 {
    &::before {
        color: $primary;
    }
    @include media("<=page-width") {
        color: $grey;
    }
}

.pbi2 .ec-name {
    font-weight: bold;
}
.blgt0 {
    padding-bottom: 0.2em;
    &::before {
        color: $primary;
    }
    @include media(">page-width") {
        font-size: 2.7em;
    }
}
.blgt1 {
    background-color: lighten($secondary, 32);
    &:hover {
        background: lighten($primary, 52);
    }
    header {
        font-size: 16px;
    }
}
.blgt33 a,
.blgt34 {
    color: $secondary-darker;
    font-weight: 700;
}
.blgt33::before {
    color: $primary;
}
.blgt34 {
    font-size: 1.2em;
}
.blgt85 {
    font-weight: 700;
}

.area10,
.prop10 {
    color: $secondary-darker;
    font-size: 2em;
    &::before {
        color: $primary;
    }
}
.area12 {
    color: $secondary-darker;
}

/* end layout */
/* end overwrites */

/* responsive design */
@import "respond";
/* below page-width */
@media only screen and (max-width: $page-width) {
    .premium-block {
        background: #fff;
    }
    .main-wrap {
        background-image: none;
    }
}
@media only screen and (max-width: 960px) {
    .nav {
        & > li {
            width: auto;
        }
        & > li:last-child a {
            border-right: none;
        }
        a {
            color: $link-color;
        }
    }
}
/* below 768 */
@media only screen and (max-width: 767px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.1em;
        padding: 0;
    }
    .mini-search legend {
        margin-bottom: 0;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

// Cookies
.cc_dialog {
    @include cookies(
        $type-of-graphic: logo,
        $graphic: 'https://wellness-hotel.info/themes/wellnesshotelfinder/styles/img/logo-wellnesshotelfinder-(hash474441043).png'
    );
}

// Facebook button in top nav
.fb-like {
    @include fb-buttons($width: 115px);
}