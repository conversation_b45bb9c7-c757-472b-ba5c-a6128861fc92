// portal management styling

/* import */
$button-size: 1.1em;
@import "import";

/* end import */

/* global mixins */ 

@mixin primary-font {  
    font-family: "Open Sans", sans serif; 
}
@mixin span-select-alignment { 
    select {
        margin-bottom: 3px;
        padding-bottom: 3px;
    }
}
@mixin position-sticky($top: 70px, $margin-bottom: 0px) {
    position: -webkit-sticky;
    position: sticky;
    top: $top; 
    &.no-sticky {
        position: relative;
        top: 0;
    }
    @media screen and (-ms-high-contrast: none), (-ms-high-contrast: active) {
        margin-top: -$top;
        margin-bottom: $margin-bottom;
        position: relative;
        &.no-sticky {
            position: relative;
        }
    }
}

@mixin header-box {
    background: #444;
    border-radius: 12px;
    margin-top: -3px;
    color: $neutral;
    a {
        color: $neutral;
    }
}
/* end global mixins */

body {
    padding-top: 0; 
    background: #fefefe;
    font-family: 'open sans', Arial, Helvetica, sans-serif;
}

html.mobile-modal-open {
    .zone-messages  {
        z-index: 1 !important;
    }
}

/* move this to seperate component files */

/* end move */

/* general */

/* display */

.jq-dropdown-parent {
    position: relative;
}

.hide {
    display: none;
}
.hide.active {
    display: block;
}
body [id^="collapsed"] {
    display: none;
}

.stand-out {
    z-index: 100;
    position: relative;
}
.stand-out-underlay:before {
    z-index: 1;
    content: ".";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #333;
    @include opacity(70);
}

%hide-text {
    text-indent: -99999px;
    display: inline-block;
}

.zone-content {
    @include group;
}

@mixin fixed-left($width: 150px) {
    float: left;
    width: $width;
    margin-left: -$width;
    position: relative;
    left: $width;
}

@mixin fluid-right($spacing-left: 150px) {
    float: left;
    padding-left: $spacing-left;
    @include border-box;
    width: 100%;
}

.space-t {
    margin-top: 1.2em;
}

%border-box {
    -webkit-box-sizing: border-box; /* Safari/Chrome, other WebKit */
    -moz-box-sizing: border-box; /* Firefox, other Gecko */
    box-sizing: border-box; /* Opera/IE 8+ */
}

.draggable-item {
    cursor: move;
}
.ui-sortable-helper {
    @include shadow;
    background: #f5f5f5;
    border: solid 1px $neutral;
    @include opacity(80);
}
.ui-sortable-placeholder td {
    border: dashed 5px #aaa;
    visibility: visible !important;
}

.modal {
    padding-top: 50px;
    box-sizing: border-box;
    height: 100vh;
}

/* end display */

/* loading */

.loading-fullscreen {
    height: 100%;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 11111;
    overflow: hidden;
    display: none;
}
.loading-fullscreen .loading-layer {
    background: #fff;
    height: 100%;
    width: 100%;
    top: 0;
    position: fixed;
    @include opacity(80);
}

.loading {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 11111;
    overflow: hidden;
    display: none;
}
.loading .loading-layer {
    background: #fff;
    height: 100%;
    width: 100%;
    position: absolute;
    @include opacity(80);
}
.loading-img {
    @include rounded;
    @extend .info;
    border: solid 2px #eee;
    display: inline-block;
    margin-top: 15px;
    padding: 50px 25px 25px 100px;
    position: relative;
    left: 50%; 
    min-height: 30px;
    font-size: 3em;
}
/* end loading */

/* gradient */

@mixin gradient($color-top, $color-bot) {
    background-color: $color-bot;
    background-image: linear-gradient(to bottom, $color-top, $color-bot);
}

/* end gradient */

/* buttons */

.btn-link {
    text-shadow: none;
}
.btn-warning {
    color: #333;
    text-shadow: None;
}

.btn-icon {
    padding: 0.2em 0.13em 0.2em 0.2em !important;
    font-size: 1.2em !important;
}

.btn-flat-blue {
    border-radius: 2px;
    padding: 5px 6px 3px 6px;
    background: $blue;
    color: #fff;
    display: inline-block;
}

.resize-vertical {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: move;
    text-align: center;
    div {
        @extend .icon-resize-vertical;
    }
}

/* end buttons */

/* icons */

h2 i { 
    margin-top: 10px; 
}

.nav-stacked {
    a:hover {
        color: $blue-dark;
    }
    .icon-chevron-thin-right {
        float: right;
        margin-right: -6px;
        margin-top: 2px;
        @include opacity(50);
    }
    .active .icon-chevron-thin-right {
        @include opacity(100);
    }   
}

.caret-black {
    @extend .caret;
    border-top-color: #333;
}

%circle {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
}

.no-info {
    @extend .icon-question-sign;
    color: #666;
}
.available {
    @extend .icon-ok-sign;
}
.available:before {
    color: $green;
}
.not-available {
    @extend .icon-minus-sign;
}
.not-available:before {
    color: $yellow;
}

/* end icons */

/* images */

.thumbnail {
    border: solid 1px $neutral;
    background: #fff;
}
.content-page img {
    height: auto;
}

/* end images */

/* clearing floats */

.group:before,
.group:after {
    content: "";
    display: table;
}
.group:after {
    clear: both;
}
@include only_ie11('.group', (zoom: 1));
@include only_ie11('.db001', (zoom: 1));
@include only_ie11_above('.group', (zoom: 1));

.clearb {
    clear: both;
}
.clearl {
    clear: left;
}
.clearr {
    clear: right;
}

/* end clearing floats */

/* dropdown */
.dropdown-menu {
    li {
        padding: 0;
        margin: 0;
        a,
        button {
            padding: 8px 12px;
        }
        &:first-child a {
            padding-top: 12px;
        }
    }
    button {
        color: #333;
        width: 100%;
        text-align: left;
        display: block;
    }
    button:hover {
        color: #ffffff;
        text-decoration: none;
        background-color: #0088cc;
    }
}

/* end dropdown */

/* collapse */

.collapse-trigger .icon-collapse {
    @extend .icon-chevron-thin-up;
    @include opacity(80);
}
.collapse-trigger.collapsed .icon-collapse {
    @extend .icon-chevron-thin-down;
}
 
/* end collapse */

/* pager */

.pagination {
    ul {
        padding-left: 0;
    }
    a {
        background: #f5f5f5;
    }
    a.active,
    a:hover,
    a:focus {
        background: #fff;
        color: $blue-dark;
        font-weight: bold;
    }
}

/* end pager */

/* boxes */

.box1of1 {
    @include layout-box(
        $items-per-line: 1
    )
}
.box1of2 {
    @include layout-box(
        $items-per-line: 2
    )
}
.box1of3 {
    @include layout-box(
        $items-per-line: 3
    )
}
.box1of4 {
    @include layout-box(
        $items-per-line: 4 
    )
}

.bx {
    @include border-box;
    padding: 0 12px;
    float: left;
}

.bx100 {
    @extend .bx;
    float: none;
    width: 100%;
}
.bx33 {
    @extend .bx;
    width: 33.3333%;
}
.bx66 {
    @extend .bx;
    width: 66.6666%;
}
.bx75 {
    @extend .bx;
    width: 75%;
}
.bx50 {
    @extend .bx;
    width: 50%;
}
.bx25 {
    @extend .bx;
    width: 25%;
}
.bx20 {
    @extend .bx;
    width: 20%;
}
.bx16 {
    @extend .bx;
    width: 16.6667%;
}

/* end boxes */

/* forms */

.legend,
legend,
fieldset h3,
fieldset h4 {
    margin: 1em 0 0.8em 0;
    font-size: 1.75em;
    border-bottom: solid 1px #e5e5e5;
    padding: 0.5em 0 0.2em 0;
}
fieldset legend:first-child {
    margin-top: 0;
}
fieldset h3 {
    font-size: 1.5em;
}
fieldset h4 {
    font-size: 1.3em;
    padding-bottom: 0.1em;
}
legend span {
    display: inline-block;
    margin: 0.8em 0 0.5em 0;
}
legend.legend-hide {
    position: absolute;
    margin: -10012px 0 0 -10012px;
}
form {
    margin: 0;
}
form label {
    font-weight: bold;
    display: block;
}
textarea {
    @include border-box;
    @include primary-font;
    width: 100%;
}
.form input,
.form select {
    margin-bottom: 2px;
}

.radio {
    display: block;
    label {
        font-weight: normal;
    }
}
textarea.input-xlarge {
    width: 100%;
}
.form-horizontal .control-label {
    width: 170px;
}
.form-horizontal .controls {
    @include group;
    margin-left: 180px;
    max-width: 650px;
    @include media("<=768px") {
        max-width: 100%;
    }
}
.controls + .controls {
    margin-top: 1em;
}
p + .control-group {
    margin-top: 1em;
}

.input-max {
    @include border-box;
    width: 100%;
    height: auto !important;
}
.label-full {
    width: 100%;
}
.form-horizontal .form-actions {
    padding-left: 180px;
    @include media("<=570px") {
        padding-left: 0;
    }
}
.form-horizontal .form-actions-regular {
    @extend .form-actions;
    padding-left: 0;
}
.form-horizontal .form-actions-214 {
    padding-left: 214px;
}
fieldset.no-legend legend {
    display: none;
}
.form {
    @extend .well;
    @extend .form-horizontal;
}
.form-actions-inline {
    margin-top: 2em;
}

input[type="radio"] {
    float: left;
    margin-right: 0.3em;
}
.input-xs {
    width: 3em;
}
.primary-secondary-action {
    @extend .btn;
    @extend .btn-info;
    margin-right: 1em;
}
.secondary-action {
    @extend .btn;
}

.form-actions-central {
    text-align: center;
}

/* end forms */

/* popups */

.open-modal {
    @extend .icon-share;
}

.modal {
    &:before {
        @include loader;
        content: "";
        position: absolute;
        top: 80px;
        left: calc(50% - 3em);
        display: block;
    }
}
.modal-content {
    background: #f5f5f5;
    text-align: left;
    padding: 16px;
    max-width: 1120px;
    margin: 0 auto;
    div[class^="validation-"] {
        width: 100%;
        max-width: 100%;
    }
}
.modal-content form {
    margin-bottom: 0;
    text-align: left;
    padding: 0;
    border: none;
    box-shadow: none;
}
.modal-content .form-actions {
    margin-bottom: 0;
    padding-bottom: 0;
}

.info-icon {
    @extend .icon-info-sign;
    @include opacity(80);
    margin-left: 0.3em;
}
.tooltip {
    border: solid 2px $neutral;
    background: #fff;
    @extend %shadow-no-top;
    @include rounded;
    padding: 8px 8px 6px 8px;
    display: none;
    min-width: 150px;
    font-size: 1em;
    max-width: 320px;
    width: auto;
    h2 {
        margin-top: 0.35em;
    }
}

.login52 {
    @extend .icon-question-sign;
}

/* end popups */

/* messages */

.zone-messages {
    @include group;
    z-index: 1000 !important; 
    .validation-summary-errors {
        @extend %rounded-b;
        @include border-box;
        display: inline-block;
    }
}
.message {
    @include flex;
    @include rounded;
    padding: 0.5em 1em;
    font-size: 1.15em;
    &:not(:first-child) {
        margin-top: 8px;
    }
}
.message-Information {
    @extend .alert;
    @extend .alert-success;
}
.message-Warning {
    @extend .alert;
    @extend .alert-block;
}
.message-Error {
    @extend .alert;
    @extend .alert-error;
}
.message-error-block {
    @extend .alert;
    @extend .alert-error;
}

.message-warning-box {
    @extend .alert;
    @extend .alert-block;
    @include rounded;
}

input.input-validation-error {
    border-color: #b94a48;
}
.validation-error {
    background: #f2dede;
    padding: 5px 5px 3px 5px;
    position: relative;
    top: -5px;
    left: -5px;
    input[type="checkbox"] {
        outline: solid 1px #b94a48;
    }
}

.validation-summary-errors {
    @extend .alert;
    @extend .alert-error;
}
.validation-summary-errors ul {
    list-style: disc inside none;
    padding-top: 0.5em;
    margin-bottom: 0.4em;
}
.validation-summary-errors li {
    padding-top: 0.5em;
}
.validation-summary-errors li:first-child {
    padding-top: 0;
}

fieldset .validation-summary-errors {
    margin-top: 1em;
    margin-bottom: 1em;
    @include rounded;
}

.field-validation-error {
    @extend .alert;
    @extend .alert-error;
    @include rounded;
    display: inline-block;
    margin: 0.3em 0;
    padding: 0.3em 0.3em 0.2em 0.3em;
}

/* end messages */

/* tables */

.table {
    border-top: none;
    table {
        border: solid 1px #ccc;
    }
    thead {
        background: #efefef;
        font-weight: bold;
    }
    * {
        background-clip: padding-box;
    }
}
.table-bordered thead {
    border: solid 1px #ccc;
}
.table-bordered thead td {
    font-weight: bold;
    background: $neutral;
    border-color: #ccc;
}
.well .table td {
    background: #fff;
    border-color: #ccc;
}
.well .table th {
    background: $neutral;
    border-color: #ccc;
}
.table-form {
    background: #f5f5f5;
}
table {
    th,
    td {
        &.text-right {
            text-align: right;
        }
    }
}

/* end tables */

/* suggestions */

@mixin break-this {
    overflow-wrap: break-word;
    word-wrap: break-word;
    -ms-word-break: break-all;
    word-break: break-word;
    -ms-hyphens: auto;
    -moz-hyphens: auto;
    -webkit-hyphens: auto;
    hyphens: auto;
}

.tt-dropdown-menu {
    @include ddbox-white;
    z-index: 1015;
    min-width: 320px;
    padding: 0;
    margin-top: -2px;
}
.tt-suggestion {
    @include break-this;
    display: block;
    padding: 7px 8px 4px;
    border-bottom: solid 1px $neutral;
    &:last-child {
        border-bottom: none;
    }
    &.tt-cursor,
    &:focus {
        background: $primary; 
        color: $txt-on-primary;
    }
}
.suggestion-text {
    @extend .icon-arrow-right;
}
.suggestion-area {
    @extend .icon-globe;
}
.suggestion-property {
    @extend .icon-tag;
}
.suggestion-entry {
    @include group;
    line-height: 1.4;
    img {
        @include rounded-remove;
        display: block;
        float: left;
        margin-right: 7px;
        border: solid 1px $neutral;
        padding: 0 !important;
    }
    span {
        display: block;
        margin-top: 4px;
    }
}
.tt-input[disabled] {
    background-color: #eeeeee !important;
}

/* end suggestions */

/* general */

/* global layout */

html,
body {
    height: 100%;
}
body {
    position: relative;
}
.wrap {
    position: relative;
    min-height: 100%;
    width: 100%;
}
.main-wrap {
    @include group;
    clear: both;
    height: auto;
    min-height: 100%;
    width: 100%;
    position: relative;
    @include media(">mobile-ui") {
        &:before {
            background-color: #2e2e2e;
            // background-image: url("./img/side-nav-bg-christmas.jpg");
            background-repeat: no-repeat;
            width: 220px;
            height: 100vw;
            min-height: 100%;
            position: fixed;
            left: 0;
            top: 0;
            content: "";
            z-index: -1;
            line-height: 20px;
        }
    }
    @include media("<=mobile-ui") {
        top: 78px;
    }
}
.sb1 {
    @include group;
    @include media("<=mobile-ui") {
        width: 100%;
    }
}
.sb2 {
    @include media(">mobile-ui") {
        position: fixed;
        width: 220px;
        bottom: 0;
    }
}
.mc1 {
    @include group;
    @include border-box;
    padding: 1em 1.5em 3em 1.5em;
    min-height: calc(100vh - 78px);
    @include media(">mobile-ui") {
        float: left;
        width: calc(100% - 220px);
    }
    @include media("<=480px") {
        padding-left: 0;
        padding-right: 0;
    }
    display: flex;
    flex-direction: column;
}
.mc9 {
    text-align: left;
    margin-bottom: 0;
    clear: both;
    a {
        margin-right: 12px;
    }
}

.btt-container {
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 1px;
}
.btt-container--intersecting .btt {
    opacity: 0;
    visibility: hidden;
}
.btt {
    @include shadow;
    border: solid 1px transparentize($blue, 0.3);
    @include rounded;
    background-color: transparentize($blue, 0.25);
    box-sizing: border-box;
    height: 70px;
    padding: 0 0px 0px 2px;
    min-height: 50px;
    width: 50px;
    z-index: 1;
    position: fixed;
    bottom: 102px;
    right: 25px;
    &:hover {
        cursor: pointer;
        background: $blue;
        border: solid 1px darken($blue, 5);
    }
    @include media("<=mobile-ui") {
        bottom: 65px;
    }
}
.btt-clicked {
    background-color: rgba($primary, 0.9);
}
.btt1 {
    @extend .icon-chevron-thin-up;
    color: $txt-on-primary;
    display: block;
    font-size: 12px;
    line-height: 1.2;
    margin-left: -3px;
    text-align: center;
    &:before {
        font-size: 30px;
        margin-left: 11px;
    }
}

.btt--reposition {
    right: 335px;
}

/* accept legal changes */
.alc11,
.alc12 {
    @extend .icon-share;
    margin-left: 5px;
    display: inline-block;
}
.alc19 {
    @extend .btn, .btn-success, .btn-large;
}

/* end global layout */


.nav0--eo{
    top: 78px !important;
}

.nav0 {
    @include media(">mobile-ui") {
        @include position-sticky($top: 0px, $margin-bottom: 44px);
        float: left;
        margin-bottom: 44px;
        width: 220px;
    }

    @include media("<=mobile-ui") {
        & {
            @include shadow(-3px, 3px, 3px, 1px, rgba(0, 0, 0, 0.25));
            @include rounded-remove;
            @include complex-transition(
                opacity 300ms ease 0s,
                transform 300ms ease 0s
            );
            will-change: opacity, transform;
            background: #2e2e2e;
            border: solid 1px darken(#2e2e2e, 8);
            border-right: none;
            box-sizing: border-box;
            max-width: 310px;
            opacity: 0;
            width: 310px;
            margin: 0;
            position: fixed;
            left: 100%;
            transform: translateX(100%);
            overflow-x: hidden;
            z-index: 100;
            li {
                border-bottom: solid 1px darken(#2e2e2e, 8);
                float: none;
                &.nav-mobile-login,
                &.nav-mobile-close {
                    display: block;
                }
                &:first-child a {
                    border-left: none;
                }
                &:last-child {
                    border-bottom: none;
                }
            }
            & > li:last-child a {
                border-right: none;
            }
        } 
        &.nav-open {
            opacity: 1;
            transform: translateX(-100%);
            max-height: calc(100vh - 88px);
            /* for mobile nav adjustment in smaller resolutions*/
            @include media("height<=320px") {
                height: 200px;
            }
            .hd10 {
                display: block;
                float: none;
                text-align: left;
                padding-top: 8px;
            }
        }
    }
}
.nav-mobile {
    @include media(">mobile-ui") {
        display: none;
    }
}
.navm2 {
    @include border-box;
    @include button;
    float: left;
    padding: 0;
    margin: 0 0 0 8px;
    width: 54px;
    height: 54px;
}
.navm21 {
    @extend .icon-bars;
    color: $txt-on-primary;
    display: block;
    font-size: 2.55em;
    margin: 0px 0 -6px;
    height: 40px;
    line-height: 1px;
    &:before {
        margin-right: 0;
    }
}
.navm22 {
    font-size: 0.75em;
    display: block;
    font-family: verdana;
    text-align: center;
}
.mnb {
    display: none;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: $header-height;
    background: #000;
    opacity: 0.5;
}
.mnb.mnb-open {
    display: block;
    z-index: 25;
}

/* end header */

/* sidebar */

.nav1,
.snav1 {
    list-style: none;
    display: block;
    background: #333;
    margin: 0;
    padding: 0;
}
.nav1 a {
    padding: 11px 8px 10px 34px; 
    display: block;
    font-size: 1.1em;
    border-bottom: solid 1px #1a1a1a;
    border-top: solid 1px #444;
    color: $neutral;
}
.nav1 a:hover,
.nav1 .current {
    background: #444;
    text-decoration: none;
    border-top: solid 1px #595959;
}
.nav1 li {
    position: relative;
    padding: 0;
    ul li {
        font-size: 0.9em;
    }
}
.nav1 .current:after {
    @include media(">mobile-ui") {
        width: 0px;
        height: 0px;
        border-top: 12px inset transparent;
        border-bottom: 12px inset transparent;
        border-right: 12px solid #fff;
        content: "";
        top: 10px;
        right: -1px;
        z-index: 2;
        position: absolute;
    }
}
.nav1 i {
    @include opacity(70);
    margin-top: 2px;
}
.nav1 a:before {
    text-align: left;
    font-size: 0.9em;
    width: 20px;
    display: block;
    float: left;
    margin-left: -22px;
    margin-top: 2px;
}

.nav11 {
    @extend .icon-home;
}
.nav12 {
    @extend .icon-wrench;
}
.nav121 {
    @extend .icon-chevron-thin-down;
    margin-right: 0;
    margin-left: 0.5em;
    font-size: 0.9em;
}
.nav13 {
    @extend .icon-th-list;
}
.nav131 {
    @extend .icon-inbox;
}
.nav133 {
    @extend .icon-star;
}
.nav134 {
    @extend .icon-user; 
}
.nav135 {
    @extend .icon-share;
}
.nav132 {
    @include circle-counter(
        $color: #333,
        $background-color: darken($neutral, 10),
        $font-size: 1.4em
    );
    position: initial;
    margin-left: 0.5em;
    margin-top: 3px;
    vertical-align: top;
}
.nav136 {
    @extend .icon-question-sign; 
}
.nav14 {
    @extend .icon-align-justify; 
}
.nav142 {
    @extend .icon-file;
}
.nav1422 {
    @extend .icon-side-bar-line;
} 
.nav143 {
    @extend .icon-map-marker;
}
.nav144 {
    @extend .icon-tags;
}
.nav145 {
    @extend .icon-copy;
}
.nav146 {
    @extend .icon-layout-footer !optional; 
}
.nav1461 {
    @extend .icon-image;
}
.nav1462 {
    @extend .icon-info-sign;
}
.nav147 {
    @extend .icon-layout-header !optional;
} 
.nav15 {
    @extend .icon-envelope; 
}
.nav155 { 
    @extend .icon-edit;
}
.nav16 {
    @extend .icon-tags;
} 
.nav21 {
    @extend .icon-th-large;
}
.nav17 {
    @extend .icon-globe;
}
.nav171 {
    @extend .icon-cog;
}
.nav1712 {
    @extend .icon-lock;
}
.nav172 {
    @extend .icon-star;
}
.nav173 {
    @extend .icon-pencil;
}
.nav174 {
    @extend .icon-align-justify;
}
.nav175 {
    @extend .icon-share;
}
.nav176 {
    @extend .icon-user;
}
.nav177 {
    @extend .icon-time;
}
.nav178 {
    @extend .icon-sync;
}
.nav179 {
    @extend .icon-ampel !optional;
}
.nav18 {
    @extend .icon-rocket;
}
.nav181 {
    @extend .icon-user;
}
.nav19 {
    @extend .icon-inbox;
}
.nav191 {
    @extend .icon-plus-sign;
}
.nav192 {
    @extend .icon-pencil;
}
.nav194 {
    @include circle-counter(
        $color: #333,
        $background-color: darken($neutral, 10),
        $font-size: 1.4em
    );
    position: initial;
    margin-left: 0.5em;
    margin-top: 3px;
    vertical-align: top;
}
.nav195 {
    @include circle-counter(
        $color: #333,
        $background-color: darken($neutral, 10),
        $font-size: 1.5em
    );
    margin-left: 0.5em;
    vertical-align: top;
    position: initial;
}

/* entry owner */
.nav-eo11 {
    @extend .icon-home;
}
.nav-eo12 {
    @extend .icon-cog;
}
.nav-eo13 {
    @extend .icon-th-list;
}
.nav-eo14 {
    @extend .icon-th-list;
}
.nav-eo15 {
    @extend .icon-rocket;
}
.nav-eo16 {
    @extend .icon-star;
}
.nav-eo17 {
    @extend .icon-question-sign;
}
.nav-eo18 { 
    @extend .icon-calendar-alt;
}

.snav1 {
    background: #222;
    position: static;
}
.snav1 a {
    font-size: 1.1em;
    padding-left: 45px;
    border-bottom: solid 1px #000;
    border-top: solid 1px #333;
}

.sb2 a {
    background: #3b3b3b;
    display: block;
    font-size: 1em;
    color: $neutral;
    padding: 13px 5px 11px 5px;
    border-top: solid 1px #494949;
    &:hover {
        background: #494949;
        border-top: solid 1px #595959;
    }
}
.sb20 {
    padding: 0;
    display: flex;
}
a.sb23,
a.sb24 {
    @include flex(1 1 auto);
    font-size: 0.9em;
    padding: 13px 4px 11px 6px;
}

a.sb25{
    background:#eee;
    border: none;
    border-bottom: solid 1px #ddd;
    border-top: solid 1px #ddd;
    padding: 4px; 
    display: none;
    &:hover{
        border-bottom: solid 1px #ddd;
        border-top: solid 1px #ddd;
        background: lighten(#f5f5f5, 4);
    }
}

a.sb24 {
    padding-left: 6px;
    padding-right: 10px;
}
.sb21,
.sb23 {
    @extend .icon-bullhorn;
}
.sb24 {
    @extend .icon-file;
}

/* end sidebar */
  
/* main content */

/* header icons */

.bs001 {
    @extend .icon-cog;
}
.es001 {
    @extend .icon-envelope;
}
.es00001 {
    .ee0040 {
        float: none;
        margin-bottom: 1em;
        margin-left: 0;
        display: inline-block;
    }
}

.cf001 {
    @extend .icon-th-list;
}
.md001 {
    @extend .icon-align-justify;
}
.rdr001 {
    @extend .icon-share;
}
.etp001 {
    @extend .icon-th-large;
}

/* end header icons */

/* dashboard */

.db002 {
    @extend .tabs;
    li {
        width: 33.333%;
        text-align: center;
    }
    li:last-child a {
        margin-right: 0;
    }
    li.active a {
        background: #f5f5f5;
    }
    a {
        font-size: 1.5em;
    }
    :nth-child(1) a {
        @extend .icon-bar-chart;
    }
    :nth-child(2) a {
        @extend .icon-rocket;
    }
    :nth-child(3) a {
        @extend .icon-line-chart;
        &:before {
            margin-right: 12px;
        }
    }
    @include media("<=680px") {
        font-size: 1.65vw;
    }
}

.db003 {
    padding: 19px;
    background: #f5f5f5;
    margin-bottom: 3em;
}

.db001 {
    @extend .icon-home;
}

/* bewertungs widget */
.db211 {
    @extend .icon-star;
    text-align: center;
}
.db212 {
    margin: 2em auto;
    width: 250px;
}
.db213 {
    @extend .icon-share;
}
.db22 {
    @extend .icon-th-list;
}
.db23 {
    float: right;
    margin-left: 20px;
    @extend .icon-plus;
    padding-top: 6px;
}
.db230 {
    @extend .btn;
    @extend .btn-success;
    float: left;
    @extend .icon-plus;
}
.db24 {
    @extend .icon-tags;
}
.db25 {
    @extend .icon-arrow-right;
    float: right;
}
.db250 {
    @extend .icon-arrow-right;
    float: right;
    padding-top: 6px;
}
.db26 {
    @extend .icon-arrow-right;
}
.db27 {
    @extend .icon-pencil;
    @extend .btn;
    @extend .btn-success;
}

.db31 {
    @extend %bar;
    margin-bottom: 1em;
}
.db33 {
    @extend %list-left-align;
}
.db340 {
    display: inline-block;
    @include media("<=768px") {
        width: 100%;
        margin-bottom: 3px;
    }
}
.db34 {
    @extend .info;
    text-align: center;
    // add entry //
    .aeb2 {
        display: inline-block;
        position: relative;
        top: -3px;
        margin-left: 5px;
    }
    .aeb1,
    .aeb3 {
        @include hide-text;
        @extend .btn-small;
        padding-right: 18px;
        &:before {
            margin-top: 3px;
        }
    }
    // end add entry //
}
.db35 {
    @extend .icon-pencil;
    @extend .btn;
    @extend .btn-success;
    position: relative;
    top: -3px;
    margin-left: 12px;
}

#statistics-graph {
    @include svg-graph(#fff);
    svg {
        height: initial;
    }
}

.db41 {
    @extend .icon-minus;
    float: left;
    color: darken($blue, 8);
}
.db42 {
    @extend .icon-minus;
    float: right;
    color: darken(#e76e03, 3);
}

/* Statistiken */
.mtr100 {
    @extend .icon-bar-chart;
    text-align: center;
}
.mtr102 {
    text-align: right;
    @include span-select-alignment();
}
.mtr103 {
    color: $txt-color-light;
    margin-right: 5px;
    display: inline-block;
}
.mtr104 {
    width: 100px;
}

.metrics-increase {
    color: $green;
    font-weight: bold;
} 
.metrics-decrease {
    color: $red;
}

.mtr21 {
    display: flex;
    padding-left: 0;
    list-style: none;
    @include primary-font;
    clear: both;
    margin-top: 8px;
    li {
        box-sizing: border-box;        
        flex: 1 0 auto;
        display: flex;
        flex-direction: column;
        padding: 0;
        position: relative;
        text-align: center;
        border: solid 1px $neutral;
        background: lighten($neutral-one, 4);
        border-right: none;
        width: 0; 
    }
    border-right: solid 1px $neutral;
    @include media("<=640px"){
        flex-direction: column;
        li {
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            width: auto;
            border-bottom: none;
            
            .mtr2216, .mtr2226, .mtr2236, .mtr2246, .mtr2256, .mtr2266, .mtr2276 {
                text-align: left;
                align-self: center;
            }
            .mtr24 {
                text-align: right;
            }
        }
        border-bottom: solid 1px $neutral;
    }
}
.mtr2216 {
    @extend .icon-file;
}
.mtr2226 {
    @extend .icon-envelope;
}
.mtr2236 {
    @extend .icon-home;
}
.mtr2246 {
    @extend .icon-arrow-right-thin-alt;
}
.mtr2256 {
    @extend .icon-eye;
}
.mtr2266 {
    @extend .icon-phone;
}
.mtr2276 {
    @extend .icon-heart-full;
}

.mtr224 { 
    padding: 2px 4px 0 4px;
}

.mtr24,
.mtr25,
.mtr2216, .mtr2226, .mtr2236, .mtr2246, .mtr2256, .mtr2266, .mtr2276 {
    @include flex(1 0 auto);
}

.mtr2216, .mtr2226, .mtr2236, .mtr2246, .mtr2256, .mtr2266, .mtr2276 {
    margin-top: 8px;
}

.mtr24 {
    @include flex-order(2);
    font-size: 2.5em;
}
.mtr25 {
    @include flex-order(3);
    display: flex;
}
.mtr250 {
    font-size: 1.1em;
}
.mtr251 {
    color: $txt-color-light;
    display: block;
    font-size: 0.8em;
    text-align: center;
}
.mtr252,
.mtr253 {
    @include border-box;
    border-top: solid 1px $neutral;
    flex: 1 0 auto;
    padding: 4px 2px 2px;
}
.mtr252 {
    border-right: solid 1px $neutral;
}
.mtr2216, .mtr2226, .mtr2236, .mtr2246, .mtr2256, .mtr2266, .mtr2276 {
    @include flex-order(1);
    color: $txt-color-light;
    &:before {
        font-size: 1.25em;
        position: relative;
        top: 2px;
    }
}

.mtr3,
.mtr4 {
    @extend .bx50;
    padding: 0;
    @include media("<=640px") {
        width: 100%;
    }
}
.mtr3 {
    padding-right: 8px;
}
.mtr4 {
    padding-left: 8px;
}

.mtr32,
.mtr42,
.mtr52 {
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
    thead th {
        text-align: right;
    }
}

.mtr31 {
    @extend .icon-file;
    text-align: center;
}
.mtr32 tr:nth-child(7) td {
    border-top: solid 2px $neutral;
}
.mtr33 {
    @extend .text-right;
}

.mtr36 {
    font-size: 0.85em;
    position: relative;
    top: -1px;
    color: $txt-color-light;
    margin-left: 3px;
    min-width: 36px;
    display: inline-block;
}
.mtr34 {
    padding-right: 42px;
}

.mtr41 {
    @extend .icon-user;
    text-align: center;
}
.mtr42 {
    .mtr34 {
        padding-right: 0;
    }
}

.mtr5 {
    clear: both;
    padding-top: 1em;
}
.mtr51 {
    @extend .icon-rocket;
    text-align: center;
    padding-top: 0.5em;
}
.mtr511 {
    @extend .info;
    text-align: center;
    margin-bottom: 12px;
}
.mtr512 {
    text-align: right;
}

.mtr52 {
    margin-top: 1.6em;
}
.mtr522 {
    @extend .text-right;
}
.mtr523 {
    @extend .text-right;
    .metrics-increase {
        font-size: 1.5em;
        display: inline-block;
        padding: 4px 4px 3px 4px;
    }
}
.mtr525 {
    @extend .info;
    text-align: center;
}
.mtr526 {
    margin-right: 20px;
}

.mtr541 {
    @extend .icon-stats-dots;
    text-align: center;
    padding-top: 1.6em;
    margin-top: 1.6em;
    border-top: solid 1px $neutral;
}
.mtr542 {
    @extend .info;
    text-align: center;
}
.mtr543 {
    text-align: right;
    @include span-select-alignment();
}
.mtr544 {
    color: $txt-color-light;
    margin-right: 5px;
    display: inline-block;
    &:nth-child(3) {
        margin-left: 20px;
    }
}
.mtr545 {
    margin-bottom: 0;
    width: 120px;
}
.mtr545 {
    width: 200px;
}

.mtr547 {
    @include svg-graph(#fff);
}
.mtr548 {
    @include graph-legend;
}

.mtr55 {
    @include graph-legend;
}
.mtr551 {
    @include graph-legend-item;
    color: #666;
    &:before {
        @include circle-icon(#fff, #fff, 13px, 2px, $blue);
    }
    &:after {
        background: $blue;
    }
}

// user management page

.um1 {
    @extend .icon-user;
}
.um25 {
    @extend .btn;
}
.um35 {
    @extend .btn;
    @extend .btn-warning;
    @extend .icon-trash;
}
.um41 {
    margin: 0;
    padding-left: 16px;
}
.um65 {
    margin-top: 16px;
    label {
        display: inline;
    }
}
.um68 {
    @extend .btn;
    @extend .btn-warning;
    @extend .icon-trash;
}

// end user management

/* Google Analytics Graphs*/
.select-container {
    text-align: right;
    margin-bottom: 0px;
    @include span-select-alignment();
}
.ga-graph {
    @include svg-graph($blue);
}

/* end Statistiken */

/* seo statistics */

.color {
    height: 10px;
    width: 100%;
}

$ctr: #ff9900;
$clicks: #4d90fe;
$impressions: #dd4b39;
$position: #109618; 

.positive-value {
    color: greenyellow;
}

.negative-value {
    color: orangered;
}

.seo11 {
    @extend .icon-line-chart;
    text-align: center;
    margin-bottom: 12px;
}
.seo12 {
    @extend .info;
    text-align: center;
    margin-bottom: 24px;
}

.seo2 {
    @extend .crumbtrail;
    float: left;
    margin: 5px 8px 0 0;
}

.seo40 {
    border-bottom: solid 1px $neutral;
    display: flex;
    padding-left: 0;
    list-style: none;
    @include primary-font;
    clear: both;
    margin-top: 12px;
    li {
        @include flex(1 0 auto);
        @include border-box;
        display: flex;
        flex-direction: column;
        width: 25%;
        position: relative;
        text-align: center;
        border: solid 1px $neutral;
        background: lighten($neutral-one, 4);
        border-right: none;
    }
    li:last-child {
        border-right: solid 1px $neutral;
    }
    li {
        padding: 0;
        border-bottom: solid 5px $neutral;
        margin-bottom: 0;
        background: #fff;
    }
    strong {
        font-size: 2.5em;
        margin-bottom: 8px;
    }
    .ctr {
        border-bottom: solid 5px $ctr;
        .seo42 {
            @extend .icon-lightbulb;
        }
    }
    .clicks {
        border-bottom: solid 5px $clicks;
        .seo42 {
            @extend .icon-share;
        }
    }
    .impressions {
        border-bottom: solid 5px $impressions;
        .seo42 {
            @extend .icon-eye;
        }
    }
    .position {
        border-bottom: solid 5px $position;
        .seo42 {
            @extend .icon-list-ol;
        }
    }
}

.seo42 {
    color: $txt-color-light;
    margin-top: 8px;
}
.seo441,
.seo451,
.seo461 {
    @include border-box;
    width: 33.33333%;
    float: left;
    border: solid 1px $neutral;
    border-left: none;
    padding: 4px 2px 2px 2px;
}
.seo451 {
    border-right: none;
}

.seo440,
.seo450,
.seo460 {
    font-size: 1.1em;
}

.seo442,
.seo452,
.seo462 {
    color: $txt-color-light;
    display: block;
    font-size: 0.8em;
    text-align: center;
}

.seo5 {
    @include svg-graph(#fff);
    margin: 2em 0;
    .clicks {
        stroke: $clicks;
        fill: $clicks;
    }
    .impressions {
        stroke: $impressions;
        fill: $impressions;
    }
    .ctr {
        stroke: $ctr;
        fill: $ctr;
    }
    .position {
        stroke: $position;
        fill: $position;
    }
}

.seo60 {
    @include graph-legend;
}
.seo61 {
    margin: 0 8px;
    input {
        position: relative;
        top: -2px;
    }
}
.seo62 {
    @include graph-legend-item;
    color: #666;
    &.clicks:before {
        @include circle-icon(#fff, #fff, 13px, 2px, $clicks);
    }
    &.impressions:before {
        @include circle-icon(#fff, #fff, 13px, 2px, $impressions);
    }
    &.ctr:before {
        @include circle-icon(#fff, #fff, 13px, 2px, $ctr);
    }
    &.position:before {
        @include circle-icon(#fff, #fff, 13px, 2px, $position);
    }
    &.clicks:after {
        background: $clicks;
    }
    &.impressions:after {
        background: $impressions;
    }
    &.ctr:after {
        background: $ctr;
    }
    &.position:after {
        background: $position;
    }
}

.seo31 {
    @extend .icon-file;
    text-align: center;
}
.seo32 {
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
    width: 100%;
    td {
        cursor: pointer;
    }
}
.seo325 {
    @extend .text-right;
}

.seo39 {
    @extend .btn-link, .icon-arrow-right;
}

.seo90 {
    text-align: center;
}
.seo99 {
    @extend .btn, .btn-success, .btn-small, .icon-cog;
}

/* end seo statistics */


/* entry owner statistics */

.meo11 {
    text-align: center;
    @extend .icon-bar-chart;
    &:before {
        position: relative;
        top: 2px;
        margin-right: 12px;
    }
}
.meo111 {
    text-align: center;
}
.meo12 {
    @extend .info;
    text-align: center;
}

.meo2 {
    text {
        @include primary-font;
        fill: $txt-color;
    }
}

.meo2 {
    @include group;
    display: flex;
    @include flex-wrap(wrap);
    overflow: hidden;
}
.meo20 {
    @include border-box;
    @include rounded(2px, 2px, 2px, 2px);
    @include shadow(1px, 1px, 2px, 0, rgba(0, 0, 0, 0.15));
    @include flex-order(10);
    float: left;
    background: #fff;
    border: solid 1px #ccc;
    padding: 0;
    margin: 0 20px 20px 0;
    &:nth-child(1) .meo202 {
        @extend .icon-file;
    }
    &:nth-child(2) .meo202 {
        @extend .icon-globe;
    }
    &:nth-child(3) .meo202 {
        @extend .icon-envelope;
    }
    &:nth-child(4) .meo202 {
        @extend .icon-phone-square;
    }
    &:nth-child(5) .meo202 {
        @extend .icon-eye;
    }
    &:last-child {
        margin-right: 0;
    }
}
.meo201 {
    @include rounded(2px, 2px, 0, 0);
    background: $neutral-five;
    color: #fff;
    text-align: center;
    border-bottom: solid 1px darken($neutral-five, 8);
}
.meo202 {
    padding-bottom: 0;
}
.meo204 {
    color: #fff;
    font-style: italic;
}

// testing branch
.meo20 {
    @include shadow-remove;
    @include flex(1 0 auto);
    width: 100%;
    margin-right: 0;

    .meo22 {
        border-left: solid 1px #ccc;
    }

    .meo22,
    .meo228 {
        @include rounded(0, 0, 2px, 0);
    }

    .meo228 {
        display: none;
    }

    .meo229 {
        display: block;
    }
}
.meo-active1 {
    @include flex-order(1);
}
.meo-active2 {
    @include flex-order(2);
}
.meo-active3 {
    @include flex-order(3);
}
.meo-active4 {
    @include flex-order(4);
}
.meo-active5 {
    @include flex-order(5);
}

.meo210 {
    display: flex;
    align-items: stretch;
    justify-content: space-between;
    flex-wrap: wrap;
}
.meo21,
.meo22 {
    @include border-box;
    float: left;
    position: relative;
}

.meo21 {
    @include svg-graph(#fff);
    position: relative;
    overflow: hidden;
    z-index: 1;
    margin-bottom: 0px;
    border: 0px;
    @include media(">640px") {
        min-width: 400px;
        width: calc(100% - 241px);
    }
}
.meo211 {
    float: left;
}
.meo212 {
    display: none;
    @extend .icon-remove-sign;
    float: right;
    font-size: 2em;
    color: $neutral-dark;
    margin-right: -35px;
    margin-top: -21px;
    span {
        @include hide-text;
    }
    &:hover {
        background: none;
        color: $orange;
    }
}
.meo213 {
    @include graph-legend;
}
.meo2131 {
    position: relative;
    @include graph-legend-item;
    &.meo-premium:before {
        @include circle-icon(#fff, #fff, 13px, 2px, $primary);
    }
    &.meo-standard:before {
        @include circle-icon(#fff, #fff, 13px, 2px, $neutral-medium);
    }
    &.meo-my-entry:before {
        @include circle-icon(#fff, #fff, 13px, 2px, $blue);
    }
    &.meo-premium:after {
        background: $primary;
    }
    &.meo-standard:after {
        background: $neutral-medium;
    }
    &.meo-my-entry:after {
        background: $blue;
    }
}
.meo2131:first-child {
    margin-right: 24px;
}

.meo22 {
    @include rounded(0, 0, 2px, 2px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 2;
    background: #fafafa;
    width: 240px;
    @include media(">640px") {
    }
}
.meo221 {
    float: left;
}

.meo223 {
    @include primary-font;
    font-size: 0.9em;
    text-align: center;
    margin: 16px 8px;
    clear: both;
}
.meo2231 {
    display: block;
    &:before {
        background: $primary;
        height: 12px;
        width: 12px;
        content: "";
        display: inline-block;
        margin-right: 5px;
        position: relative;
        top: 1px;
    }
    &.meo-premium:before {
        background: $primary;
    }
    &.meo-standard:before {
        background: $neutral-medium;
    }
    &.meo-my-entry:before {
        background: $blue;
    }
}

.meo224 {
    clear: both;
    float: left;
    margin: 12px;
    margin-bottom: -40px;
}
.meo225 {
    @include primary-font;
    clear: right;
    float: right;
    padding-top: 2em;
    margin: 12px 16px 12px 0;
}
.meo2251 {
    @extend .icon-arrow-right;
    transform: rotate(270deg);
    display: block;
    text-align: center;
    line-height: 1;
    margin-bottom: 4px;
    font-size: 4em;
    &:before {
        margin-right: 0;
    }
    &.metrics-decrease {
        transform: rotate(45deg);
        color: $red;
    }
    &.metrics-unknown {
        transform: rotate(0deg);
    }
}
.meo2252 {
    font-size: 1.5em;
    text-align: center;
    &.metrics-unknown {
        font-size: 1em;
        max-width: 75px;
    }
}

.meo228,
.meo229 {
    @include rounded(0, 0, 2px, 2px);
    @extend .icon-chevron-thin-left;
    clear: both;
    display: block;
    border-top: solid 1px #ccc;
    background: #e9e9e9;
    text-align: center;
    padding: 6px 8px 4px;
    &:hover {
        background: #fff;
        color: $blue-dark;
    }
    &:before {
        float: left;
        font-size: 1.2em;
        position: relative;
        top: 2px;
    }
}
.meo2282,
.meo2292 {
    @extend .icon-stats-dots;
    display: block;
    text-align: center;
    &:before {
        margin-right: 5px;
    }
}
.meo229 {
    display: none;
    @extend .icon-chevron-thin-right;
}

.meo-my-entry-circle,
.meo-premium-circle,
.meo-standard-circle {
    fill: #fff;
    stroke-width: 2px;
}
.meo-my-entry-circle {
    stroke: $blue;
}
.meo-premium-circle {
    stroke: $primary;
}
.meo-standard-circle {
    stroke: darken($neutral-medium, 10);
}

.lines {
    fill: none;
    stroke-width: 2px;
    &.meo-premium {
        stroke: $primary;
    }
    &.meo-standard {
        stroke: darken($neutral-medium, 10);
    }
    &.meo-my-entry {
        stroke: $blue;
    }
}

.filling-area {
    @include opacity(10);
    &.meo-premium {
        fill: $primary;
    }
    &.meo-standard {
        fill: $neutral-medium;
        opacity: 0.3;
    }
    &.meo-my-entry {
        fill: $blue;
    }
}

.bar {
    .meo-premium {
        fill: $primary;
    }
    .meo-standard {
        fill: $neutral-medium;
    }
    .meo-my-entry {
        fill: $blue;
    }
    // rect {stroke-width:2px;}
    //   	.meo-premium {fill: transparentize($primary,0.2); stroke:$primary;}
    //   	.meo-standard {fill: transparentize($neutral-medium,0.2); stroke:$neutral-medium;}
    // .meo-my-entry {fill: transparentize($blue,0.2); stroke:$blue;}
    .value text {
        font-weight: bold;
    }
}

.tick {
    line {
        stroke: $txt-color-light;
        stroke-dasharray: 5, 5;
    }
}

.legendGroup {
    text {
        text-anchor: start;
    }
    .meo-premium-line {
        stroke: $primary;
        stroke-width: 2px;
    }
    .meo-standard-line {
        stroke: $neutral-medium;
        stroke-width: 2px;
    }
    .meo-my-entry-line {
        stroke: $blue;
        stroke-width: 2px;
    }
}

.meo8 {
    background: #eee;
    padding: 12px;
    margin-top: 1.5em;
    border: solid 1px $neutral;
    p {
        @extend .info;
    }
}
.meo81 {
    @extend .icon-info-sign;
    text-align: center;
}
.meo821 {
    @extend .icon-file;
}
.meo822 {
    @extend .icon-globe;
}
.meo823 {
    @extend .icon-envelope;
}
.meo824 {
    @extend .icon-phone-square;
}
.meo825 {
    @extend .icon-eye;
}

.meo9 {
    @include group;
    padding-top: 12px;
    margin-top: 2em;
    clear: both;
    text-align: center;
    margin-top: 2em;
    border-top: solid 1px $neutral;
}
.meo91 {
    @extend .icon-rocket;
}
.meo92 {
    @extend .btn, .btn-success, .btn-large, .icon-rocket;
    margin-bottom: 0.5em;
    margin-top: 1em;
}
.meo94 {
    @extend .icon-list-ol;
    margin-top: 20px;
    display: block;
}

/* end entry owner statistics */

/* end dashboard */

#map_canvas {
    border: solid 1px #999;
}

.el000{
    display: flex;
    align-items: center;

    .aeb5{
        margin-top: 15px;
        width: initial;
        text-indent: initial;
    }
}

.el001 {
    @extend .icon-th-list;
    margin-right: 2em;
    float: left;
    @include media("<=640px") {
        margin-right: 0;
        float: none;
    }

    &--lang{
        @extend .icon-globe;
    }
}
.el11 {
    max-width: 100%;
}
.el110 {
    @extend .form;
    @extend .form-search;
    margin-right: 12px;
    clear: both;
    margin-right: 0;
}
.el111 {
    @include primary-font;
    font-size: 1.25em;
    float: left;
    a {
        padding-left: 12px;
        padding-right: 12px;
    }
    .active a {
        color: #555;
    }
    li:nth-child(1) a {
        @extend .icon-tags;
    }
    li:nth-child(2) a {
        @extend .icon-camera;
    }
}

.el1190 {
    position: relative;
}
.el119 {
    @include circle-icon-width-expanding(
        $bg-color: #fff,
        $fg-color: darken($primary,15),
        $size: 32px,
        $border-width: 2px,
        $border-color: darken($primary,10)
        );
    overflow-wrap: normal;
    font-weight: bold;
    top: -29px;
    right: -20px;
    position: absolute;
}
.has-no-pictures .el119 {
    border: 2px solid $red;
    color: $red;
}

.elt11{
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
}

.elt112{
    margin-left: 6px;
}

.el112,
.elt112 {
    @extend .btn;
    margin-right: 1em;
}

.elt112{
    min-width: fit-content;
}

body
.elt111{
    width: 260px;
    margin-bottom: 0
}

.el113,
.elt1121 {
    @extend .icon-search;
    @include opacity(75);
}
.el114 {
    margin-top: 8px;
}
.el1140 {
    @extend .icon-cog;
    white-space: nowrap;
    vertical-align: middle;
    margin-right: 1em;
}

.elt116{
    display: flex;
    align-items: center;

    .controls{
        margin-left: 0;
    }

    .control-label{
        width: 150px;
    }
}

.elt115{
    display: flex;
    flex-direction: column;
    width: 150px;

    .controls{
        margin-left: 0;
    }

    .control-label{

    }

    select{
        max-width: 100%;
    }
}

.elt119{
    display: flex;
    flex-wrap: wrap;
    margin-top: 16px;
    width: 100%;
}
// test
.elt115,
.elt116,
.el115,
.el116 {
    @extend .checkbox;
    font-weight: normal;
    margin: 3px 1em 0 0;
}
.el116 {
    font-weight: bold;
    margin-right: 0;
}
.el12 {
    @extend .table, .table-striped, .table-bordered;
    clear: both;
    margin-right: 12px;
    // fixed headers
    @include tables-sticky-header($background-color: #ddd, $border-color: #ccc, $shadow-start-color: rgba(0,0,0,0.1));
    // between mobile and desktop
    @include media("<=page-width"){
        overflow-x: visible;
    }

    /* responsive table */
    @include media("<=880px") { 
        thead,
        tbody td {
            display: none;
        }

        tbody {
            display: block;
        }

        tbody td::before {
            content: attr(data-colname) ":";
            font-weight: bold;
        } 

        tr {
            display: flex;
            flex-wrap: wrap;
            td {
                flex-grow: 1;
            }
        }

        .row-expanded td {
            display: block;
        }

        tbody tr th {
            width: 36px;
            position: relative;

            .custom-checkbox-label {
                margin-right: 0;
            }
        }

        .responsive-table-primary-data {
            display: block;
            padding-right: 48px;
        }
    }

    .responsive-table-show-secondary-data {
        @extend .icon-chevron-thin-down;
        position: absolute;
        right: 8px;
        top: 10px;
        display: none;
        padding: 0;
        width: 40px;
        height: 40px;
        border: none;
        outline: 0;
        background: 0 0;

        @include media("<=880px") {
            display: block;
        }
    }

    .row-expanded .el126 {
        @extend .icon-chevron-thin-up;
    }

    .screen-reader-text {
        @include hide-text;
    }
}
.el130 {
    padding-right: 2em;
    position: relative;
    float: left;
}
.el1300 {
    display: inline-block;
    width: 2em;
    height: 1em;
    position: absolute;
    right: 0;
    top: -10px;
}
.el131:before {
    @extend .icon-caret-up;
    vertical-align: sub;
    position: relative;
    z-index: 1000;
}
.el132:before {
    @extend .icon-caret-down;
    position: relative;
    top: -6px;
}
.el131:before,
.el132:before {
    padding-left: 0.6em;
}
.el710 {
    position: relative;
}
.el42 {
    position: relative;
}
.el425 {
    @extend .text-right;
}
.el426 {
    @extend .text-right;
    font-size: 1.5em;
}

.el2 {
    margin-top: 2.5em;
    margin-bottom: 6em;
}
.el200 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-info;
}
.el201 {
    @extend .icon-caret-down;
    color: #fff;
}
.el21 {
    @include group;
}
.el23,
.el24 {
    @extend .icon-download-alt;
}
.el271,
.el281 {
    @extend .btn-link;
    @extend .icon-trash;
}
.el275 {
    @extend .text-right;
}
.el281 {
    border-top: solid 1px $neutral;
    padding-top: 5px;
}
.el25 {
    text-align: center;
    clear: both;
    margin-top: 2em;
}
.el251 {
    @extend .btn-link;
    @extend .icon-undo;
}

/* Add Entry type */

.etp1 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-large;
    @extend .icon-plus;
    margin-bottom: 1.4em;
}

.etp100 {
    flex-wrap: wrap;
}
.etp100.tabs > li.active a {
    background: $neutral-one;
    color: $txt-color;
}
.etp10 {
    
    .tab-content {
        background: none;
        border: none;
    }
}

/* Eintragsstatistiken */
.el27 {
    white-space: nowrap;
}

.el3 {
    margin: 1.9em 0 2em 0;
    float: left;
}

.el3 > .aeb2 > a{
  @extend .btn;
  @extend .btn-success;
  @extend .btn-large;
}

.el30 {
    display: flex;
    list-style: none inside none;
    padding: 0;
}
.el380 {
    display: inline-block;
    margin-left: 8px;
}
.el38 {
    @include button-inverted-icon-only;
    @extend .btn, .btn-large, .icon-sync;
}

// add entry button //

.aeb1 {
    @extend .btn, .btn-success, .btn-large, .icon-plus;
}
.aeb2 {
    position: relative;
    display: inline-block;
    margin-right: 8px;
}
.aeb3 {
    @extend .btn, .btn-success, .btn-large, .icon-plus, %icon-chevron-thin-down-after;

    &::after {
        margin-right: 0;
        margin-left: 0.3em;
    }
}
.aeb4 {
    @extend .pull-right;
    @include media("<=640px") {
        &.dropdown-menu {
            left: 0;
            right: auto;
        }
        .ee001 &.dropdown-menu {
            left: auto; 
            right: 0;
        }
    }
}
.aeb41 {
    @extend .icon-plus;
}

.aeb5 {
    @include button-inverted-icon-only;
    @extend .btn, .btn-large, .icon-upload;

    &--txt{
        width: initial;
        text-indent: initial;
    }
}

// end add entry button //

/* entry owner entry list */
.el51 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-large;
    @extend .icon-plus;
    @extend %font-icon-xl;
    margin-bottom: 1em;
    margin-top: 1.5em;
    float: left;
}
.el52 {
    clear: both;
    text-align: right;
    margin-right: 12px;
    margin-bottom: 3px;
    label {
        @extend .info;
        font-weight: normal;
    }

    @include media("<=768px") {
        margin-top: 1em;
        margin-bottom: 1em;
        float: right;
        clear: none;
    }
}

.el71 {
    display: block;
    padding: 6px 2px;
    margin: -6px -2px;
}
.el711 {
    @extend .btn, .btn-success, .btn-mini;
    float: right;
}
.el712 {
    float: left;
    margin-right: 5px;
}
.el74 {
    @extend .icon-rocket;
    color: $green;
}
.el75 {
    @extend .icon-rocket;
    color: $blue-dark;
}
.el76 {
    @extend .icon-rocket;
    color: $txt-color;
}
.el77 {
    @extend .icon-rocket;
    color: $red;
}
.el74,
.el75,
.el76,
.el77 {
    float: left;
    position: relative;
    top: 2px;
}

.el4 {
    display: inline-block;
    text-align: left;
}
.table td.el43 {
    padding-bottom: 0.2em;
    @include media("<=830px") {
        text-align: right;
    }
    &:before {
        content: none;
    }
}
.el40 li {
    border-bottom: solid 1px $neutral;
    &:last-child {
        border-bottom: none;
    }
}
.el44 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-info;
    @extend .btn-icon;
}
.el440 {
    @include media(">830px") {
        display: none;
    }
}
.el441 {
    @extend .icon-edit;
}
.el442 {
    @extend .icon-envelope;
}
.el443 {
    @extend .icon-share;
}
.el444 {
    @extend .icon-globe;
}
.el445 {
    @extend .icon-rocket;
    background: $green;
}
.el446 {
    @extend .icon-rocket;
    @extend .btn-link;
    &:before {
        color: $red;
    }
}
.el447 {
    @extend .icon-star;
}
.el448 {
    @extend .icon-ampel !optional;
}
.el449 {
    @extend .icon-eye;
}

.deactivated {
    color: #999;
}
.deactivated .el44 {
    @extend .btn;
    @extend .btn-mini;
}
.deactivated .el44 .caret {
    @extend .caret-black;
}
.deactivated .el45 {
    @extend .icon-chevron-thin-down;
}
.el45 {
    @extend .icon-chevron-thin-down;
    margin: 0;
}
.el451 {
    @extend .icon-camera;
}
.el46 {
    @extend .btn;
    @extend .btn-info;
    @extend .dropdown-toggle;
    @extend .btn-mini;
}
.el46 span {
    @extend .caret;
}
.el47 {
    border-top: solid 1px $neutral;
    margin-top: 5px;
}
.el471,
.el472 {
    @extend .icon-rocket;
}
.el473 {
    @extend .icon-sync;
}
.el473,
.el481,
.el482,
.el483,
.el4831,
.el484,
.el489 {
    @extend .btn;
    @extend .btn-link;
    padding: 5px 0 0 0;
    margin: 0;
    font-size: 1em;
}
.el481 {
    @extend .icon-remove-sign;
}
.el482 {
    @extend .icon-ok-sign;
}
.el483 {
    @extend .icon-trash;
}
.el4831 {
    @extend .icon-trash;
    background: $orange;
}
.el484 {
    @extend .icon-exclamation-sign;
}
.el485 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-undo;
}
.el486 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-rocket;
}
.el487 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-plus-sign;
}
.el489,
.el4890 {
    @extend .icon-star;
}
.el49 {
    @extend .icon-trash;
    @include opacity(70);
}

.el6 {
    @extend .info;
    @include group;
}
.el61 {
    float: left;
}
.el62 {
    float: right;
    margin-right: 12px;
}
.el62 a {
    padding: 0 5px;
}

/* completeness */

.cmp10 {
    @extend %bar-small;
}
.cmp16 {
    @extend %badge-100-medium;
    float: left;
    margin-right: 1em;
}
.cmp17 {
    @extend %list-left-align;
}

.cmp21 {
    @extend .icon-share;
}

/* end completeness */

tr.deactivated {
    &,
    th,
    td {
        font-style: italic;
        background-color: #eee !important;
    }
}

.lgd1 {
    @extend .text-xl;
    margin-bottom: 20px;
}

/* vertical nav */

.property-left,
.group-nav {
    float: left;
    width: 190px;
    margin-right: -190px;
}
.property-left {
    @include position-sticky($top: 100px, $margin-bottom: 20px);
    @include media("<mobile-ui"){
        @include position-sticky($top: 2px, $margin-bottom: 20px);
    }
}
.property-nav {
    position: relative;
}
.group-nav {
    @include position-sticky($top: 10px);
    margin-top: 1.2em;
}

.vertical-nav-list {
    @extend %shadow-small;
    list-style: none;
    width: 190px;
    margin: 0 0 2em 0;
    padding-left: 0;
    background: $neutral-six;
    a {
        padding: 0.5em 0.5em 0.5em 0;
        display: inline-block;
    }
    li {
        padding: 0;
    }
}
.properties-form {
    @include fluid-right(200px);
}
.group-content {
    @include fluid-right(210px);
}

.pee0 {
    @include media("<=830px") {
        display: none;
        background-color: #e5e5e5;
        box-shadow: 3px 3px 5px 1px $neutral-darker;
        border: 1px solid #ccc;
        align-items: center;
        width: calc(100% + 16px);
        margin: 0 -8px;
        z-index: 10;
        top: 138px;
        @include complex-transition(transform 300ms ease 0s);
        will-change: transform;

        &:before {
            content: attr(data-divname);
            font-weight: bold;
            padding: 0 0.5em;
            width: 20%;
        }
        &.scrolling-down {
            transform: translateY(-140px);
        }
        &.stopped-scrolling {
            transform: translateY(0);
        }
    }
}

.pee1 {
    @include media("<=830px") {
        width: 100%;
        padding-left: 0;
        .form {
            padding: 0px 12px; 
        }
    }
}

.pee121 {
    margin-top: 5px;
    @include primary-font;
    font-size: 1.25em;
    float: left;
    margin-right: 12px;
}
.pee122 {
    @extend .btn;
    @extend .btn-info;
    @extend .icon-time;
}
.pee124 {
    margin-top: 5px;    
}

.pee13 {
    @extend .icon-info-sign;
    &:last-of-type {
        margin-bottom: 24px;
    }
}

.pee02 {
    @include media("<=830px") {
        width: 80%;
    }
}

.pee2 {
    @extend .vertical-nav-list, .nav-tabs, .nav-stacked, .nav;
    @include media("<=830px") {
        height: 36px;
        width: 100%;
        overflow: hidden;
        position: relative;
        margin-bottom: 0;
        li {
            transform: translateY(-100%);
            position: absolute;
            top: 0;
            transition: transform 0.3s ease-in-out;
            width: 100%;
        }
        .active {
            transform: translateY(0%);
            a {
                border-color: #08c;
            } 
        }
        li:first-child > a {
            border-radius: 0;
        }
        .active ~ li {
            transform: translateY(100%);
        }
        &--open {
            height: calc(100vh - 200px);
            overflow-y: scroll;
            & li,
            & .active ~ li {
                transform: translateY(0);
                position: static;
            }
        }
    }
}
.pee21 {
    @include media("<=830px") {
        display: none;
    }
}

/* end vertical nav */

/* custom forms */
.cfm2 {
    @include border-box;
    background: #fff;
    width: 100%;
}
.cfm21 {
    float: left;
}
.cfm24 {
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
    clear: both;
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
}
.cfm24 td {
    width: calc(33.3333% - 63px);
}
.cfm24 td:nth-child(4) {
    width: 190px;
}
.cfm242 {
    @extend .btn;
    @extend .btn-mini;
    margin-right: 0.3em;
}
.cfm243 {
    @extend .icon-pencil;
    @include opacity(70);
}
td.cfm2440 {
    text-align: right;
}
.cfm27 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-success;
    @extend .icon-plus;
    @extend %font-icon-xl;
    margin: 26px 0 1em 1.6em;
    float: left;
}
.cfm28 {
    @extend .icon-share;
    @extend %font-icon-xl;
    margin: 26px 0 1em 1.6em;
    float: right;
}
.cfm272 {
    @extend .icon-fullscreen;
    opacity: 0.8;
    padding: 12px 6px 5px;
    margin: 16px 8px 0 8px;
}
.cfm33 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-warning;
}
.cfm41 {
    @extend .form-actions;
    border-top: 0px;
    margin: 0 auto;
    width: 290px;
}
.form .cfm41 {
    padding: 0;
}

.cfm52.controls {
    margin-left: 170px;
}
/* end custom forms*/

/* properties */

.prp001 {
    @extend .icon-tags;
}

.prp17 {
    @extend .form;
    clear: both;
    margin-bottom: 0;
}

// move group
.prp20:hover .prp272 {
    opacity: 1;
}

// tagline for premium
.tgl12 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-rocket;
    min-width: 220px;
}
.tgl13 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-plus-sign;
    min-width: 220px;
}

.prp21 {
    float: left;
}
.prp221 {
    @extend .icon-pencil;
    @include opacity(80);
}

.prp225 {
    float: left;
    margin-top: 25px;
    margin-left: 24px;
}

.prp24 {
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
    clear: both;
    border-collapse: collapse;
}
.prp240 {
    border: solid 1px $neutral;
}
.prp241 {
    font-weight: bold;
}
.property--premium {
    @extend .icon-rocket;
}
.property--useful {
    @extend .icon-tag; 
} 
.property--very-useful {
    @extend .icon-tags;
}
.property--top {
    @extend .icon-star;
} 
.property--po-only {
     @extend .icon-lock; 
}
.prp242 {
    @extend .btn;
    @extend .btn-mini;
    margin-right: 0.3em;
}
.prp243 {
    @extend .icon-pencil;
    @include opacity(70);
}
.prp244 {
    @extend .icon-sync;
}
.prp27 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-success;
    @extend .icon-plus;
    @extend %font-icon-xl;
    margin: 38px 0 1em 1.6em;
    float: left;
}
.prp272 {
    opacity: 0;
    display: inline-block;
    @extend .icon-fullscreen;
    padding: 12px 6px 5px;
    margin: 24px 8px 0 8px;
}
.prp28 {
    float: right;
    margin: 26px 0 1em 1em;
}
.prp281 {
    @extend .btn;
    @extend .btn-mini;
    @extend .dropdown-toggle;
}
.prp29 {
    @extend .btn;
    @extend .btn-link;
}
.prp291 {
    @extend .icon-trash;
    @include opacity(80);
}

.prp30 {
    @include group;
    padding-bottom: 200px;
}

.prp32 {
    @extend .btn;
    @extend .btn-mini;
}
.prp33 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-warning;
}
.prp33 i {
    margin-right: 0;
}
.prp34 {
    @extend .icon-resize-vertical;
    @include opacity(0);
    float: right;
}
.prp351 {
    @extend .icon-chevron-thin-up;
    @include opacity(70);
    float: right;
}
.prp36 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-info;
}
.prp361 {
    @extend .icon-cog;
    margin: 0;
}
.prp37 {
    margin-top: 1em;
    padding: 0.5em;
    background: darken(#f5f5f5, 5%);
    border: 1px solid darken(#f5f5f5, 10%);
    margin-bottom: 2em;
}
.prp39,
.prp38 {
    width: 95%;
}
.prp40 {
    margin-bottom: 1em;
    @extend %icon-resize-vertical-after;
    &:after {
        margin-left: 12px;
    }
}
.prp40:last-child {
    margin-bottom: 0;
}

.prp53 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-ok-sign;
}
.prp54 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-remove-sign;
}

.prp65 {
    @extend .icon-share;
    margin-top: 5px;
    display: inline-block;
}

.prp95 {
    margin-top: 3em;
}
.prp951 {
    @extend .icon-cog;
    @include opacity(80);
}

tr.jq-moveable:hover {
    cursor: move;
}
tr.jq-moveable:hover button {
    cursor: pointer;
}
tr.jq-moveable:hover .prp34 {
    @include opacity(80);
}

.prp42,
.prp43 {
    display: inline-block;
    margin-right: 8px;
}

.prp52 {
    @extend .form;
    text-align: center;
}

.prp63 {
    @extend .form;
}

.prp71 {
    @extend %dropdown-style;
}
.prp711 {
    @extend %dropdown-text;
}
.prp712 {
    @extend %dropdown-style-icon;
}
.prp72 {
    @extend .mcbox;
    padding: 0;
    li {
        border-top: solid 1px $neutral;
    }
    li:first-child {
        border: none;
    }
    p {
        padding-left: 12px;
        padding-right: 12px;
    }
}

/* rabat codes */
.rbc11 {
    float: left;
}
.rbc12 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-success;
    @extend .icon-plus;
    @extend %font-icon-xl;
    margin: 10px 0 1em 1.6em;
    float: left;
}
.rbc2 {
    @extend .btn;
    @extend .btn-mini;
    margin-right: 0.3em;
}
.rbc3 > label {
    float: left;
    font-weight: normal;
    margin-right: 2em;
    width: calc(50% - 1em);
    &:nth-child(2n) {
        margin-right: 0;
    }
}

// partner affiliate image
.pta11 {
    margin: 1em 0;
}
.pta12 {
    @include info;
    display: inline-block;
    margin-right: 8px;
}
.pta13 {
    display: inline-block;
    max-width: 120px;
    max-height: 50px;
}
.pta21 {
    padding-top: 5px;
}

.pta31 {
    margin-top: 8px;
    font-weight: 100;
}

// embed media

.mem3 {
    @extend .fake-row;
    padding: 16px 2px 8px 3px;
}
.mem30 {
    @include group;
    @include border-box;
    margin-bottom: 0;
    &:not(:first-child) {
        padding-top: 0;
        border-top: none;
    }
}
.mem31 {
    width: 150px;
    float: left;
    margin-right: 12px;
}
.mem33 {
    width: calc(100% - 150px - 12px - 32px - 8px - 10px);
    display: block;
    float: left;
}
.mem32 {
    @extend .btn;
    @extend .btn-warning;
    @extend .btn-mini;
    @extend .icon-trash;
    float: right;
    width: 32px;
    margin: 3px 0 0 8px;
    padding-left: 9px;
    &::before {
        margin-right: 0;
    }
    span {
        @extend %hide-text;
    }
}
.mem40 {
    @include group;
    padding-bottom: 4px;
}
.mem400 {
    @include group;
    margin-bottom: 10px;
}
.mem4 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-mini;
    @extend .icon-plus;
    margin-top: 1em;
    float: left;
}
.mem42 {
    @extend .icon-question-sign;
    float: right;
}
.mem43 {
    @extend .select-auto;
    display: block;
    clear: both;
}

/* end properties */

/* keywords */

.kw001 {
    @extend .icon-globe;
}

/* end keywords */

/* user images */

.uis001 {
    @extend .icon-camera;
}
.ui1 {
    @extend .icon-camera;
}
.ui24 input {
    width: 400px;
    max-width: 100%;
    padding-right: 30px;
}
.ui25 {
    @include freetext-clear;
}

.ui4 {
    display: inline-block;
}
.table td.ui43 {
    padding-bottom: 0.2em;
}
.ui4500 {
    min-width: 250px;
}
.ui40 li {
    border-bottom: solid 1px #ddd;
    &:last-child {
        border-bottom: none;
    }
}
.ui44 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-info;
    @extend .btn-icon;
}
.ui459 {
    @extend .info;
    margin-left: 8px;
    display: block;
}

.deactivated .ui44 {
    @extend .btn;
    @extend .btn-mini;
}
.deactivated .ui44 .caret {
    @extend .caret-black;
}
.deactivated .ui45 {
    @extend .icon-chevron-thin-down;
}
.ui45 {
    @extend .icon-chevron-thin-down;
    margin: 0;
}

.ui451 {
    @extend .info;
    margin-left: 11px;
}

.ui47 {
    border-top: solid 1px #ddd;
    margin-top: 5px;
}
.ui481,
.ui482,
.ui483,
.ui489 {
    @extend .btn;
    @extend .btn-link;
    padding: 5px 0 0 0;
    margin: 0;
    font-size: 1em;
}
.ui481 {
    @extend .icon-remove-sign;
}
.ui482 {
    @extend .icon-ok-sign;
}
.ui483 {
    @extend .icon-trash;
}
.ui4831 {
    @extend .icon-trash;
    background: $orange;
}
.ui489 {
    @extend .icon-home;
}

.ui71 {
    border: solid 1px $neutral;
}
.ui711 {
    width: auto;
    max-width: 100%;
}
.ui72 {
    @extend .icon-arrow-right;
    margin-top: 20px;
    font-size: 0.85em;
    display: block;
}
.ui73 {
    font-size: 1em;
}

.ui770 {
    position: relative;
}
.ui76,
.ui77,
.ui78,
.ui79 {
    font-size: 2em;
    display: block;
    &:before {
        margin-right: 0;
    }
}
.ui76 {
    @extend .icon-minus-sign;
    color: $red;
}
.ui77 {
    @extend .icon-ok-sign;
    color: $green;
}
.ui78 {
    @extend .icon-question-sign;
    color: $orange;
}
.ui79 {
    @include hide-text;
    @extend .icon-star-empty;
}

/* end user images */

/* premium income, transactions */

.po001 {
    @extend .icon-rocket;
}
.po1 {
    @extend .form;
    @extend .form-search;
    @extend .group;
    clear: both;
    margin-bottom: 32px;
}
.po12,
.po13 {
    float: left;
    margin-right: 16px;
}

.po2 {
    @extend .wrapper-dark;
    @include group;
    margin-bottom: 24px;
}
.po21 {
    @include group;
    @include primary-font;
    text-align: center;
    strong {
        font-size: 1.35em;
        margin-left: 1em;
        position: relative;
        top: 2px;
    }
}

.po3,
.po4 {
    @include border-box;
    @include box-left-50(32px);
    margin-bottom: 32px;
    h2,
    h3,
    p {
        text-align: center;
    }
}
.po31 {
    @include primary-font;
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
    margin-right: 12px;
    clear: both;
    font-size: 1.15em;
    td {
        text-align: right;
    }
    tfoot {
        background: #eee;
        font-size: 1.3em;
    }
}
.po32 {
    @extend .info;
    margin-bottom: 24px;
}
.po45 {
    @extend .wrapper;
    @include group;
    padding-bottom: 0;
}
.po46 {
    @include group;
    @include primary-font;
    text-align: center;
    strong {
        font-size: 1.35em;
        margin-left: 1em;
        position: relative;
        top: 2px;
    }
}
.po71 {
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
    margin-right: 12px;
    clear: both;
    td:nth-child(4) {
        text-align: right;
    }
}

.po8 {
    clear: both;
    h2 {
        text-align: center;
    }
}

/* end premium income */

/* redirects */

.rdr002 {
    @extend .table, .table-striped, .table-bordered;
    max-width: 600px;
    thead tr td {
        background: #efefef !important;
    }
}
.rdr1 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-success;
    @extend .icon-plus;
    @extend %font-icon-xl;
    margin-bottom: 1em;
    float: left;
}
.rdr2 {
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
    clear: both;
    border-collapse: collapse;
}
.rdr220 {
    border: solid 1px $neutral;
}
.rdr223 {
    @extend .btn;
    @extend .btn-mini;
    margin-right: 0.3em;
}
.rdr224 {
    @extend .icon-pencil;
    @include opacity(70);
}
.rdr225 {
    display: inline-block;
    margin-right: 8px;
}
.rdr226 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-warning;
    i {
        margin-right: 0;
    }
}
input[type="text"].rdr321 {
    width: 100%;
    @extend %border-box;
    height: 28px;
}

/* end redirects */

/* metadata */

.md01 {
    @extend .icon-globe;
}
.md21 {
    @extend %list-clean;
    @include group;
    margin-top: 0;
}
.md22 {
    float: left;
}
.md23 {
    @include button-flat($secondary, $txt-on-secondary);
    @extend .icon-arrow-up;
    margin-right: 5px;
    &:before {
        font-size: 0.85em;
        position: relative;
        top: -1px;
    }
}

.md25 {
    margin-top: 3em;
}

.md31 {
    @extend .input-xlarge;
}
.md310 {
    @extend .input-full-width;
}

.md311 {
    @extend .btn;
    @extend .btn-warning;
    @extend .btn-small;
    @extend .icon-undo;
}
.md32 {
    @extend .input-xlarge;
}
.md34 {
    clear: both;
}
.md35 {
    margin-top: -10px;
}
.md36 {
    font-weight: bold;
    color: $green;
}
.md38 {
    font-size: 1.4em;
    position: relative;
    top: 1px;
    font-weight: bold;
}

.md34,
.md35 {
    max-width: 650px;
}

.md39 {
    @extend .message-Error;
}

.md4 {
    background: #fff;
    padding: 16px 12px 12px 12px;
    border: solid 1px $neutral;
    width: 632px;
}
.md41,
.md42,
.md43,
.md44 {
    font-family: arial, sans-serif;
    line-height: 1.4;
}
.md41 {
    border: none;
    margin: 0;
    padding: 0;
    line-height: 1;
}
.md42 {
    color: #1a0dab;
    font-size: 18px;
    line-height: 1.35;
    &:hover {
        text-decoration: underline;
        background: #fff;
        color: #1a0dab;
    }
}
.md43 {
    color: #006621;
    font-size: 14px;
}
.md44 {
    color: #545454;
    max-height: 36px;
    text-overflow: ellipsis;
}
.md45 {
    @extend .info;
    margin-top: 4px;
}

.md51 {
    @extend .message-Error;
}

/* end metadata */

/* grundeinstellungen - basic settings */

.bs1 {
    legend {
        margin-top: 2em;
    }
}

/* end grundeinstellungen */

/* edit entries */

// premium marketing box
.dropdown-selected a {
    @extend .option-selected;
}

.pm1 {
    @extend .well;
    @include group;
    margin-bottom: 0;
    .modal-dialog {
        width: 90%;
    }
    .modal-header {
        padding: 0;
    }
    .modal-body {
        @include group;
        padding-left: 0;
        padding-right: 0;
    }
}

.pm2 {
    text-align: center;
    padding-bottom: 1.5em;
    padding-top: 1em;
}
.pm20 {
    @extend .well-light;
    text-align: center;
    margin: 1.5em 0 0 0;
    .pm23,
    .pm24 {
        position: relative;
        top: 1px;
    }
}
.pm201 {
    font-size: 1.5em;
}
.pm202 {
    @extend .info;
}
.pm200 {
    @extend .icon-share;
}
.pm23 {
    font-size: 1.5em;
    @include primary-font;
    font-weight: bold;
}
.pm231 {
    @extend .info;
    font-size: 0.7em;
}
.pm24 {
    @include primary-font;
    @extend .icon-long-arrow-up;
    color: $green;
    display: inline-block;
    font-size: 1.1em;
}

.pm3 {
    @include bx(25%, 0 12px 0 12px);
    @extend .well-light;
    padding-top: 1em;
    padding-bottom: 1em;
    @include media("<=480px") {
        width: 100%;
    }
}
.pm30 {
    float: left;
    border: solid 1px $neutral;
    margin: 0 8px 8px 0;
}

.pm34 {
    text-align: center;
}
.pm340 {
    @extend .info;
    text-align: center;
}
.pm341 {
    @extend .btn;
    @extend .btn-large;
    @extend .btn-info;
    @extend .icon-rocket;
    width: 100%;
    @include border-box;
}
.pm36 {
    @extend .icon-share;
}
.pm39 {
    text-align: center;
    clear: both;
}
.pm391 {
    @extend .icon-undo;
}

.pm4 {
    @include bx(75%, 0 12px 0 0);
    @include media("<=480px") {
        padding-right: 0;
        width: 100%;
    }
    h2 {
        padding-top: 0.5em;
        padding-bottom: 0.3em;
    }
}
.pm41 {
    a {
        @include group;
    }
    span {
        margin-top: 8px;
        display: inline-block;
        @include media("<=480px") {
            line-break: anywhere;
        }
    }
}
.pm411,
.pm412,
.pm413 {
    float: left;
    margin-right: 5px;
}
.pm411 {
    @extend %badge-premium-small;
}
.pm412 {
    @extend %badge-rating-small;
}
.pm413 {
    @extend %badge-100-small;
}
.pm420 {
    margin: 1em 0 2em 0;
}
.pm42 {
    @include primary-font;
    font-size: 1.2em;
}
.pm43 {
    @extend .icon-rocket;
}
.pm44 {
    @include primary-font;
    @extend .icon-long-arrow-up;
    color: $green;
    font-size: 1.2em;
    margin-left: 3px;
}
.pm46 {
    @extend .icon-share;
}

.pm5 {
    @extend .table;
    @extend .table-bordered;
    margin-bottom: 2em;
}
.pm50 {
    color: $red;
}
.pm51 {
    @extend .icon-rocket;
}
.pm52 {
    color: $green;
}
.pm55 {
    @extend .icon-share;
}
.pm56 {
    @include primary-font;
    margin-left: 8px;
    font-size: 1.1em;
    @extend .icon-long-arrow-up;
    color: $green;
}
.pm57,
.pm58 {
    @include primary-font;
    font-size: 1.5em;
    position: relative;
    top: 1px;
}
.pm59 {
    @extend .info;
}

.pm63 {
    @extend .well-light;
    text-align: center;
}
.pm632 {
    @extend .icon-bullhorn;
}
.pm69 {
    @extend .icon-share;
}
.pm694 {
    @extend .icon-star;
}
/* bewertungs widget */
.pm6950 {
    margin: 1.5em auto;
    width: 250px;
}
.pm695 {
    @extend .icon-share;
}

// end premium marketing box

.ee000 {
    @extend .icon-pencil;
    box-sizing: border-box;
    float: left;
    padding-left: 1.6em;
    text-indent: -1.6em;
    max-width: calc(100% - 240px);
    &:before {
        text-indent: 0;
    }

    @include media("<=640px") {
        max-width: 100%;
    }
}
.ee0001 {
    float: right;
}

.ee0011 {
    @extend .message-Error;
    margin-bottom: 1em;
}
.ee0012 {
    @extend .message-Information;
    margin-bottom: 1em;
}

.ee001 {
    float: right;
    margin-top: 2em;
    position: relative;
    @include media("<=640px") {
        margin-top: 0;
        margin-bottom: 1em;
    }
    // add entry for EO //
    .aeb3 {
        @include button-inverted-icon-only;
        margin-right: 0;
        margin-left: 0px;
    }
    @include media("<=480px") {
        margin-right: 8px;
    }
}
.ee004 {
    float: left;
    margin-right: 1em;
    & + .aeb2 {
        float: left;
    }
}

// translate entry content switch
.ee0040 {
    float: left;
    margin-right: 16px;
    background: #eee;
    border: solid 1px #ddd;
    border-radius: 8px;
    padding: 0 4px 4px 4px;
    margin-left: 16px;
} 
.ee0401 {
    @extend .info;
    font-size: 0.8em;
}

.ee002 {
    @extend .icon-wrench;
    @include button-inverted-icon-only($secondary, $txt-on-secondary);
}
.ee003 {
    min-width: 250px;
}
.ee0031 {
    @extend .icon-globe;
}

.mobile-viewport-marker {
    height: 100vh;
    width: 0;
    position: fixed;
    @include media("<=page-width") {
        font-weight: 1000;
    }
    @include media("<=mobile-ui") {
        font-weight: 960;
    }
}

.ee00 {
    @extend .well;
    @include group;
    padding-bottom: 8px;
    clear: both; 
    @include media("<=mobile-ui") {
        @include complex-transition(transform 300ms ease 0s);
        will-change: transform;
        padding: 8px;
        &.sticky-entry-status.scrolling-down, &.sticky-entry-status.stopped-scrolling-down {
            transform: translateY(-80px);
        }
        .es1.es1 {
            margin-bottom: 5px;
        }
        .es2 {
            display: none;
        }
        .es3 {
            padding-top: 0;
        }
    }
    @include media("<=830px") {
        &.sticky-entry-status {
            box-sizing: border-box;
        }
    }
    &.sticky-entry-status {
        position: fixed;
        top: 0;
    }
}

.sticky-entry-status {
    display: none;
    
}
.show-sticky-entry-status .sticky-entry-status {
    display: block;
}

body.scrolling-up, body.stopped-scrolling-up {
    .sticky-entry-status {
        @include media("<mobile-ui"){
            transform: translateY(78px);
            transition: transform 300ms ease 0s;
        }
        
    }
}

.ee01 {
    @extend .bx66;
    margin-bottom: 0.8em;
    @include media("<=830px") {
        width: 100%;
    }
}

// premium status
.ps000 {
    @extend .bx33;
    @extend .icon-rocket;
    margin-bottom: 1em;
    text-align: right;
    padding-right: 0;
    display: block;
    @include media("<=830px") {
        width: 100%;
    }
}
.prime-status {
    @include group;
    @extend .wrapper-light;
    padding-top: 12px;
    margin-bottom: 1em;
    clear: both;
}
.ps21 {
    @extend .icon-rocket;
    padding: 0 0 6px 0;
    text-align: center;
}
.ps22 {
    @extend .icon-pencil;
}
.ps221 {
    @extend .icon-flag;
}
.ps23 {
    @extend .icon-share;
}
.ps31 {
    @extend .btn;
    @extend .btn-info;
    @extend .icon-rocket;
    float: right;
    display: block;
}
.ps4,
.ps5 {
    @extend .bx50;
    img {
        height: auto;
    }
}
.ps81 {
    @extend .icon-trash;
}
.ps82 {
    @extend .icon-undo;
}
// popup individuelle konditionen
.ps9 .ps21 {
    border-bottom: solid 1px $neutral;
    text-align: left;
    margin-bottom: 1em;
}
.ps92 {
    @extend .table;
    @extend .table-bordered;
    @extend .table-striped;
    td:nth-child(2) {
        text-align: right;
    }
}

.ps9500 {
    padding-bottom: 1em;
    padding-top: 0.5em;
    border-bottom: solid 1px $neutral;
}
.ps951 {
    @extend .btn, .btn-success;
}
.ps952 {
    @extend .btn;
}
.ps953 {
    @extend .btn, .btn-warning;
}
.ps954 {
    @extend .btn, .btn-danger;
}
.ps991 {
    text-align: right;
    border: none;
}

// Suchseiten Position verbessern
.ps35 {
    clear: both;
    text-align: center;
}
.ps350 {
    @extend .icon-rocket;
    padding: 5px;
    display: inline-block;
    &:hover,
    &:hover .ps352 {
        text-decoration: none;
        background: $blue;
        color: #fff;
    }
}
.ps351 {
    @include primary-font;
    font-size: 1.2em;
    position: relative;
    top: 1px;
}
.ps352 {
    @include primary-font;
    @extend .icon-long-arrow-up;
    color: $green;
    font-size: 1.2em;
    position: relative;
    top: 1px;
}

.ee110 {
    float: right;
    @include group;
    @include media("<=480px") {
        padding-right: 8px;
    }
}
.ee10 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-mini;
    float: right;
    margin: 0.5em 0 0 1em;
}
.ee100 {
    @extend .icon-plus;
}
.ee11 {
    float: right;
    margin-top: 0.5em;
    margin-left: 1em;
}
.ee111 {
    @extend .icon-share;
}
.ee13 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-small;
    text-align: center;
    @include border-box;
    width: 100%;
}
.ee130 {
    @extend .icon-plus;
}
.ee140 {
    text-align: center;
    margin-top: 16px;
    @include media("<=830px") {
        display: none;
    }
}
.ee141 {
    @extend .icon-star;
}
.ee21 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-mini;
}
.ee211 {
    @extend .icon-plus;
} 
.ee22 {
    @extend .btn;
    @extend .btn-warning;
    @extend .btn-mini;
    display: block;
    margin-top: 1em;
}
.ee225 {
    @extend .btn;
    @extend .btn-warning;
    @extend .btn-mini;
}

.ee24{
    display: block;
    margin-top: 1em;
}

.ee221 {
    @extend .icon-trash;
    @include opacity(80);
}

body .cf-single-date {
    width: 65px;
}

.ee413 input.ee23,
.ee413 input.ee25 {
    @extend .input-xs;
    width: 100%;
    max-width: 8ch;
}

textarea.ee23{
    min-width: 200px;
    resize: vertical;
}

.ee32 {
    @extend .icon-share;
    @include opacity(80);
}
.ee41 {
    @extend .table;
    @extend .table-bordered;
    @extend .table-striped;
    td {
        vertical-align: middle;
    }
}

.design-Event{
    .controls{
        max-width: 800px;
    }

    .ee41{
        border: none;
    }
}

.ee4110 {
    display: flex;
    border-bottom: solid 1px #ddd;
    padding: 12px;
    width: calc(100% + 24px);
    margin: 0 -12px;
    background: #eaeaea;
    box-sizing: border-box;

    .ee22 {
        margin-bottom: 2px;
        align-self: flex-end;
    }
}

.ee411 {
    display: flex;
    align-items: flex-end;

    .ee42 {
        margin-right: 16px;
    }
}

.ee419{
    @extend .wrapper-dark;
    padding-top: 0;
    margin-bottom: 32px;
    padding-bottom: 0;
}

.ee412{
    max-width: 200px;
    border: 1px solid #ccc;
    margin: 0 8px 32px 0;
    padding: 8px;

    ::-webkit-resizer {
        display: none;
    }

    .ee24{
        &:first-of-type{
            margin-top: 0;
        }
    }
}

.ee4121{
    display: flex;
    flex-direction: column;

    input{
        max-width: 100%;
        width: 100%;
    }
}

.ee41211{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;

    .ee26{
        margin: 0 8px;
    }
}

.ee4111{
    margin-right: 16px;

    .ee23{
        max-width: 150px;
    }
}

.ee41111{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;

    .ee26{
        margin: 0 8px;
        align-self: center;
    }
}

.ee4131{
    border: 0;
    margin-right: 16px;
}

.ee41310{
    display: flex;
    align-items: center;
}

td.ee410 {
    padding: 0px;

    & > div {
        display: flex;
    }
}
.ee42 {
    width: auto;
}
.ee43 {
    margin: 0 0 .75em 0;
    display: flex;
    align-items: center;
    & > * {
        margin-right: 8px;
    }

    .ee24,
    .ee22{
        margin-top: initial;
    }
}
.ee412 {
    padding: 8px;
}
.ee413 {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding-bottom: 8px;
}
.ee44 {
    float: left;
    margin-top: 0.4em;
}
.ee51 {
    margin-bottom: 1em;
    @extend .info;
}

.ee55 {
    @extend .icon-share;
}

.ee71 {
    @extend .icon-floppy-o;
}
.ee72 {
    @extend .icon-exclamation-sign;
}
.ee73 {
    @extend .icon-floppy-o;
}
.ee74 {
    @extend .icon-trash;
}

.ee81 {
    float: left;
}
.ee800 {
    margin: 1em 0;
}
.ee812 {
    @extend .icon-plus-sign;
    @extend .btn-link;
    float: left;
    &:before {
        color: $green;
    }
}

/* images for premium only */
.ee84 {
    @include wrapper;
}
.ee842 {
    @extend .icon-share;
}
.ee843 {
    @extend .btn, .btn-info, .btn-large, .icon-rocket;
}

.ee99 {
    @extend .form-actions;
    @extend .form-actions-214;
    @include media("<=570px") {
        .ee990 & {
            padding-left: 20px;
        }
    }
}
.ee99 .field-validation-error {
    margin-top: -4px;
}
.ee991 {
    @extend .icon-remove-sign;
    @extend .btn-link;
    &:before {
        color: $orange;
    }
}
.ee992 {
    @extend .icon-ok-sign;
    @extend .btn-link;
    &:before {
        color: $green;
    }
}
.ee999 {
    float: right;
}

// affiliate / partner

.eea12 {
    display: block;
}
.eea13,
.eea14 {
    display: block;
    max-width: 120px;
    max-height: 50px;
    margin: 8px 0;
}

// compound list 

.mcl0 {
    @extend .wrapper-dark;
    margin-bottom: 1em;
    padding-top: 4px;
    padding-bottom: 0;
    position: relative;
    top: 0;
    transition: .7s;
}

.mcl0--no-transition{
    transition: initial;
}

.mcl14 {
    margin-right: 16px;
}
.mcl13 {
    @extend .btn, .btn-mini, .btn-warning, .icon-trash;
    float: right;
    margin-top: 4px;
}
.mcl15, .mcl16  {
    @extend .btn, .btn-mini, .btn-neutral;
    display: inline-block;
    margin-right: 4px;
    padding-left: 10px;
    span {
        @extend %hide-text;
    }
}
.mcl15  {
    @extend .icon-arrow-up;
}
.mcl16  {
    @extend .icon-arrow-down;
}

.mcl18 {
    @extend .btn, .btn-link, .icon-trash;
}
.mcl51 {
    @extend .btn, .btn-success, .btn-small, .icon-plus;
    margin-bottom: 8px;
}

.document-type-pdf {
    @extend .icon-pdf;
}
.document-type-excel {
    @extend .icon-excel;
}
.document-type-powerpoint {
    @extend .icon-powerpoint;
}
.document-type-doc {
    @extend .icon-word-doc;
}
.document-type-other {
    @extend .icon-blank-doc;
}

.document-type-pdf,
.document-type-excel,
.document-type-powerpoint,
.document-type-doc,
.document-type-other {
    &::before {
        font-size: 1.5em;
        padding: 3px 0;
    }
    &:hover::before {
        background: lighten($primary, 5);
    }
}

.agbs {
    margin-bottom: 1em;
}

/* entry status */

.entry-status {
    @include group;
    clear: both;
}

.es1 {
    @extend %bar;
    clear: both;
    margin-bottom: 1.2em;
}
.es13 {
    @extend %list-left-align;
}
.es14 {
    @extend %badge-100-medium;
    float: left;
    margin-right: 1em;
}

.es2 {
    @include group;
    @include media("<=mobile-ui") {
        .is_stuck & {
            display: none;
        }
    }
}

// badge tooltip layout
@mixin resize-badge(
    $size: 41px,
    $icon-size-change: 0.9,
    $icon-top-change: 42%
) {
    margin-top: 0;
    width: $size/1.1;
    &:before {
        height: $size * 1.1;
        font-size: $size;
    }
    &:after {
        font-size: $size;
    }
    i {
        width: $size/1.1;
        font-size: $size * $icon-size-change/1.75;
    }
    i:before {
        @include translate(0, $icon-top-change);
    }
}

.es202 {
    font-size: 1.4em;
    margin-top: 0.6em !important;
}
.es203 {
    clear: both;
    margin-top: 1.2em;
}

.es21,
.es212 {
    @extend %badge-image-medium;
    float: left;
    margin-right: 1em;
    &:hover {
        background: none;
    }
}
.es21 {
    @include media("<=328px") {
        @include resize-badge(
            $size: 31px,
            $icon-size-change: 0.8,
            $icon-top-change: 55%
        );
    }
}
.es210 {
    @extend .btn;
    @extend .btn-success;
    @extend .icon-camera;
    @include border-box;
    width: 100%;
}
.es22,
.es222 {
    @extend %badge-description-medium;
    float: left;
    margin-right: 1em;
}
.es22 {
    @include media("<=328px") {
        @include resize-badge(
            $size: 31px,
            $icon-size-change: 0.9,
            $icon-top-change: 60%
        );
    }
}
.es23,
.es232 {
    @extend %badge-properties-medium;
    float: left;
    margin-right: 1em;
}
.es23 {
    @include media("<=328px") {
        @include resize-badge(
            $size: 31px,
            $icon-size-change: 0.9,
            $icon-top-change: 45%
        );
    }
}
.es24 {
    @extend %badge-100-large;
    float: left;
    margin-top: -11px;
    margin-right: 1em;
    @include media("<=414px", ">328px") {
        @include resize-badge($icon-size-change: 1.1, $icon-top-change: 30%);
    }
    @include media("<=328px") {
        @include resize-badge(
            $size: 31px,
            $icon-size-change: 1.1,
            $icon-top-change: 30%
        );
    }
}
.es242 {
    @extend %badge-100-medium;
    float: left;
    margin-right: 1em;
}
.es25 {
    @extend %badge-rating-large;
    float: right;
    margin-right: 1em;
    margin-top: -11px;
    @include media("<=414px", ">328px") {
        @include resize-badge($icon-size-change: 0.95, $icon-top-change: 38%);
    }
    @include media("<=328px") {
        @include resize-badge(
            $size: 31px,
            $icon-size-change: 0.95,
            $icon-top-change: 38%
        );
    }
}
.es250 {
    margin-left: 52px;
}
.es252 {
    @extend %badge-rating-medium;
    float: left;
    margin-right: 1em;
}
.es26 {
    @extend %badge-premium-large;
    float: right;
    margin-top: -11px;
    &:hover {
        background: none;
    }
    @include media("<=414px", ">328px") {
        @include resize-badge($icon-size-change: 0.75, $icon-top-change: 62%);
    }
    @include media("<=328px") {
        @include resize-badge(
            $size: 31px,
            $icon-size-change: 0.8,
            $icon-top-change: 60%
        );
    }
}
.es260 {
    margin-left: 52px;
}
.es261 {
    @extend .btn;
    @extend .btn-info;
    @extend .icon-rocket;
    @include border-box;
    width: 100%;
}
.es262 {
    @extend %badge-premium-medium;
    float: left;
    margin-right: 1em;
}

.es3 {
    @include group;
    margin-top: 4px;
    @include media("<=414px") {
        margin-top: 14px;
    }
}
.es31,
.es32 {
    float: left;
    display: flex;
    align-items: center;
    border-radius: 14px;
    padding-right: 14px;
}
.es310 {
    display: inline-block;
    margin-right: 0.5em;
    .custom-switch__label {
        margin: 0;
    }
    .custom-switch__label::before {
        margin: 0;
    }
}
.es34 {
    @extend .icon-share;
    display: none;
    margin-left: 12px;
    float: right;
}

/* end entry status */

.gc00 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-success;
    margin-left: 12px;
    margin-top: 2px;
}
.gc12 {
    border: solid 1px #bbb;
}
.gc14 {
    @extend .alert, .alert-info;
}
.gc14,
.gc15,
.gc16 {
    padding-top: 0;
}
.geo-location10 {
    @extend .icon-map-marker;
}

/* map marker */

@mixin map-marker(
    $size: 30px,
    $color: $blue,
    $txt-color: #fff,
    $border-color: darken($blue, 8),
    $top-offset: 1px
) {
    @include border-box;
    @include rounded(50%, 50%, 50%, 5%);
    @include rotate(-45deg);
    width: $size;
    height: $size;
    margin-top: -($size * 0.707) - $top-offset;
    border: solid 2px $border-color;
    background: lighten($color, 4);
    cursor: pointer;
    position: absolute;
    z-index: 10;
    top: -($size/2);
    left: -($size/2);

    &:hover {
        background: lighten($color, 10);
        border-color: lighten($border-color, 4);
        z-index: 120 !important;
    }
    span {
        @include rotate(-315deg);
        color: $txt-color;
        display: block;
        width: $size;
        text-align: center;
    }
    span:before {
        position: relative;
        font-size: $size/1.35;
    }
}

.map-marker {
    @include map-marker;
    span {
        @extend .icon-flag;
    }
    span:before {
        top: 5px;
        left: 3px;
        font-size: 18px;
    }
}

/* end map marker */

// checkbox list i work here - dienstleister
.cbl0 {
    border-bottom: solid 1px $neutral;
    max-width: 420px;
}
.cbl1,
.cbl-child {
    list-style: none inside none;
    padding: 0;
    margin-top: 0;
    li {
        border: solid 1px $neutral;
        border-top: none;
        border-right: none;
        padding: 0;
    }
    li:last-child {
        border-bottom: none;
    }
    label {
        margin-left: 5px;
        padding-top: 5px;
        display: inline-block;
    }
    input[type="checkbox"] {
        margin-top: -2px;
        margin-left: 5px;
    }
}
.cbl1 {
    background: lighten($neutral-one, 2);
    margin-top: 0;
    margin-bottom: 0;
    border: solid 1px $neutral;
    border-bottom: none;
    width: 450px;
    & > li {
        border-left: none;
    }
}
.cbl110 {
    display: block;
    border-bottom: solid 1px $neutral;
}
.cbl13 {
    padding-top: 7px;
    display: inline-block;
    padding-bottom: 7px;
}

.cbl-child {
    padding-left: 28px;
}
.cbl14 {
    @extend .icon-chevron-thin-down;
    color: $blue-dark;
    text-decoration: underline;
    margin-left: 12px;
    padding-top: 7px;
    float: right;
    padding-right: 8px;
}
.cbl14:hover {
    cursor: pointer;
    background: #4cad4c;
    color: #fff;
}
.cbl14.ko-expanded {
    @extend .icon-chevron-thin-up;
}

// end checkbox list

/* end entries */

/* messages */

.msg001 {
    @extend .icon-envelope;
}

.msg00 {
    float: left;
    margin-right: 24px;
}
.msg03 {
    @extend .icon-search;
    @extend .btn;
    vertical-align: top;
    margin-left: 10px;
}

.msg30 {
    @extend .table, .table-striped, .table-bordered;
    clear: both; 
}

.msg12 {
    @extend .table;
    @extend .table-bordered;
    td,
    th {
        background: #fff !important;
    }
}

.msg121{
    margin-bottom: 18px;
}

.msg121{
    background-color: #fff;
    display: flex;
    margin: 0px;
}

body
.msg1211{
    @extend .icon-exclamation-sign;
    @extend .error;
    border: none;
    background: #f2dede !important;
    min-width: 150px;
}

body
.msg1212{
    border-radius: 0px;
    border: 0px;
}

.msg14{
    @extend .btn;
    display: block;
    margin: auto;
}

.msg20 {
    display: block;
    text-align: center;
}
// allgemein
.msg21 {
    @extend .icon-wrench;
}
// Anfrage
.msg22 {
    @extend .icon-envelope;
}
// Anfrage ohne Eintragsbetreiber -> an Portalbetreiber
.msg23 {
    @extend .icon-envelope;
    padding: 2px 3px 1px 1px;
    background-color: #f89406;
    background-position: -238px -119px;
}
// Eintrag vorgeschlagen
.msg24 {
    @extend .icon-plus-sign;
}
// Anfrage an Portal Betreiber
.msg25 {
    @extend .icon-envelope;
}
// Premium Buchung, Verlängerung
.msg26 {
    @extend .icon-rocket;
}
// Bewertung
.msg27 {
    @extend .icon-star;
}
// Fragen und Antworten - posts
.msg28 {
    @extend .icon-question-sign;
}

.msg32 {
    width: 50%;
}

.msg92 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-small;
    @extend .icon-floppy-o;
    margin: 1em 0 0.6em 0;
}

.set-password1 {
    @extend .btn;
    @extend .btn-info;
    @extend .icon-lock;
}

/* end messages */

/* Besucher - Bilder */
.bb001 {
    @extend .icon-camera;
}
.bb12 {
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
}
.bb13 {
    background-color: $neutral-darkest;
    box-sizing: border-box;
    color: #fff;
    display: inline-block;
    padding: 4px 10px 4px;
    width: 100%;
}

// Fragen und Antworten

.mqaa1 {
    @extend .icon-question-sign;
}
.mqaa15 {
    margin-top: 16px;
}
.mqaa17 {
    margin-top: 0;
}

.mqaa50 {
    tbody td {
        vertical-align: middle;
    }
}

// Antworten
.mqaa50 {
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
}
.mqaa51 {
    border-bottom: none !important;
    border-top: none !important;
    background: none !important;
    min-width: 16px;
}

.mqaa521,
.mqaa522,
.mqaa523 {
    @extend .icon-comment;
}
.mqaa521 {
    color: $green;
}
.mqaa523 {
    color: $orange;
}

.mqaa53 {
    font-style: italic;
}

.mqaa54 {
    @extend .icon-user;
    display: block;
}
.mqaa55 {
    @extend .icon-smile-o;
    display: block;
}
.mqaa56 {
    @extend .icon-ok-sign;
    white-space: nowrap;
    &:before {
        color: $green;
    }
}
.mqaa57 {
    @extend .icon-minus-sign;
    white-space: nowrap;
    &:before {
        color: $orange;
    }
}

// Fragen
.mqaa601,
.mqaa602,
.mqaa603 {
    @extend .icon-question-sign;
    font-size: 1.5em;
}
.mqaa601 {
    color: $green;
}
.mqaa603 {
    color: $orange;
}
.mqaa61 {
    font-style: italic;
}
.mqaa62 {
    @include opacity(65);
    @extend .icon-share;
    margin-left: 8px;
    &:before {
        margin-right: 0;
    }
    &:hover,
    &:focus {
        @include opacity(100);
    }
}
.mqaa63 {
    @extend .icon-smile-o;
    display: block;
}
.mqaa64 {
    @extend .icon-ok-sign;
    white-space: nowrap;
    &:before {
        color: $green;
    }
}
.mqaa65 {
    @extend .icon-minus-sign;
    white-space: nowrap;
    &:before {
        color: $orange;
    }
}

// actions
.mqaa701 {
    display: flex;
    @include justify-content(space-between);
}
.mqaa71,
.mqaa72,
.mqaa73,
.mqaa74,
.mqaa711,
.mqaa721,
.mqaa731 {
    width: 26px;
    height: 26px;
    @include border-box;
    margin: 0 6px 0 0;
    float: left;
    &:last-child {
        margin-right: 0;
    }
}

.mqaa71,
.mqaa72,
.mqaa73,
.mqaa74 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-icon;
    display: inline-block;
    span {
        @extend %hide-text;
    }
}
.mqaa71 {
    @extend .btn-success;
    @extend .icon-ok;
}
.mqaa72 {
    @extend .btn-warning;
    @extend .icon-remove;
    float: right;
}
// Anzeige Ablehnen Button, wenn bereits angenommen
.mqaa74 {
    @extend .icon-remove;
}
.mqaa73 {
    @include fix-outline;
    @extend .btn-success;
    @extend .icon-envelope;
}

.mqaa711,
.mqaa721,
.mqaa731 {
    display: inline-block;
    font-size: 2em;
    padding: 0;
    &:before {
        margin: 0;
        width: auto;
    }
    span {
        @extend %hide-text;
    }
}
.mqaa711 {
    @extend .icon-ok;
    color: $green;
}
.mqaa721 {
    @extend .icon-remove;
    color: $orange;
}
.mqaa731 {
    @extend .icon-envelope;
    color: $green;
}

// entry owner view
.mqaa78 {
    @extend .btn;
    @extend .btn-success;
    @extend .icon-pencil;
    white-space: nowrap;
}
.mqaa781 {
    margin-top: 8px;
    margin-left: 28px;
}

// entry owner answer question form
.mqaa785 {
    font-style: italic;
}

// action popdrop
.mqaa81 {
    white-space: normal;
    width: 320px;
    text-align: center;
}
.mqaa811 {
    @extend .icon-ok;
    @extend .info;
    padding-top: 16px;
    &:before {
        color: $green;
    }
}
.mqaa812 {
    @extend .icon-envelope;
}

.mqaa83,
.mqaa84,
.mqaa85 {
    @extend .btn;
    @extend .btn-success;
    @include border-box;
    display: block;
    width: 100%;
    margin-bottom: 8px;
}
.mqaa83 {
    @extend .icon-user;
}
.mqaa84 {
    @extend .icon-smile-o;
}
.mqaa85 {
    @extend .icon-user;
}
.mqaa850 {
    @extend .icon-smile-o;
}
.mqaa831,
.mqaa841,
.mqaa851 {
    @extend .icon-ok;
    display: block;
    margin-bottom: 8px;
}

.mqaa89 {
    @extend .info;
    margin-top: 12px;
}

/* end Fragen und Antworten */

/* premium */

.prm001 {
    @extend .icon-rocket;
}
.prm002 {
    @extend .icon-ok-sign;
}

.prm20 {
    font-size: 1.1em;
}

.prm32 {
    text-align: right;
}
.prm82 {
    @extend .icon-trash;
}
.prm41 {
    @extend .table;
    @extend .table-bordered;
}
.prm60 {
    margin-top: 2em;
}
.prm601 {
    font-weight: normal;
}

.prm605 {
    @extend .wrapper-light;
    display: none;
}
.prm606 {
    @extend .icon-bullhorn;
}
.prm61 {
    @extend .primary-action;
    @extend .btn-warning;
}

.prm81 {
    @extend .btn;
    @extend .btn-small;
    @extend .btn-info;
    @extend .icon-rocket;
}
.prm87,
.prm88 {
    @extend .info;
}

.rbcv1,
.rbcv2 {
    box-sizing: border-box;
    border-radius: 4px;
    width: 409px;
    max-width: 100%;
    padding: 0.4em 0.6em;
    margin: 0.3em 0 0 180px;
}
.rbcv1 {
    @extend .alert-success;
}
.rbcv2 {
    @extend .alert-error;
}

/* edit premium */

.eprm005 {
    @extend .icon-bullhorn;
}
.eprm006 {
    @extend .icon-share;
    margin-left: 0.6em;
}
.eprm51 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-mini;
    @extend .icon-plus-sign;
}
.eprm53 {
    @extend .btn-link;
    @extend .icon-trash;
    margin-left: 1em;
    font-size: 0.56em;
}

.eprm6 {
    @extend .well-light;
    @include group;
    margin-top: 12px;
}
.eprm7 {
    @extend .wrapper-light;
    float: left;
    margin-right: 2em;
    margin-top: 1em;
}
.eprm72 {
    @extend .btn-link;
    @extend .icon-trash;
    margin-left: 1em;
    font-size: 0.65em;
}
.eprm73 {
    @extend .btn-link;
    @extend .icon-flag;
    margin-left: 1em;
    font-size: 0.65em;
}
.eprm74 {
    @extend .icon-flag;
    margin-left: 1em;
    font-size: 0.65em;
}
.eprm75 {
    resize: vertical;
    float: left;
    width: 210px;
    margin-right: 5px;
}
.eprm76 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-warning;
    margin-top: 0px;
    display: block;
    float: left;
}
.eprm80 {
    clear: both;
    padding-top: 1em;
    margin-top: 1em;
}
.eprm81 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-mini;
    @extend .icon-plus-sign;
}
.eprm99 {
    max-width: 70%;
}

/* end edit premium */

.bp12:before {
    @extend .icon-rocket;
    @include circle-icon(#555, #fff, 2em);
    line-height: 1;
}
.bp2 {
    @extend .well;
    @include group;
}
.bp21 {
    @extend .bx66;
    @include media("<=480px") {
        width: 100%;
    }
}
.bp210 {
    margin-top: 1em;
}
.bp211 {
    @extend .icon-share;
}
.bp22 {
    @extend .bx33;
    @extend .wrapper-light;
    padding: 12px;
    font-style: italic;
    margin-top: 12px;
    @include media("<=480px") {
        width: 100%;
    }
}
.bp221:before {
    @extend .icon-lock;
    @include circle-icon(#555, #fff, 2em);
}
.bp3 {
    text-align: center;
    margin: 0.8em 0 1.2em 0;
    strong {
        font-size: 2em;
        position: relative;
        top: 0.13em;
        font-weight: bold;
    }
}
.bp35 {
    @include primary-font;
    text-align: center;
    font-size: 1em;
}
.bp36 {
    text-align: center;
    margin-bottom: 2em;
}

.bp6 {
    @extend .form-horizontal;
    margin-top: 1em;
    .angucomplete-holder {
        display: block;
        input {
            @extend .input-xlarge;
        }
    }
    .form-actions {
        @include media("<=480px") {
            padding: 17px 0;
        }
    }
}
.bp41 {
    padding: 0;
}
.bp41:nth-child(2),
.bp41-suggested {
    .bp42 {
        padding-top: 10px;
        padding-bottom: 6px;
    }
}
.bp42 {
    margin-bottom: 0;
}
.bp43 {
    font-size: 19px;
    margin-top: 0;
    padding-bottom: 0.4em;
    padding-top: 0.5em;
}
.bp434 {
    display: block;
    font-size: 0.9em;
}
.bp45 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-large;
}
.bp75 {
    @extend .btn;
    @extend .btn-large;
    @extend .btn-success;
    width: 100%;
}

@media only screen and (max-width: 570px) {
    /* forms */
    .form-horizontal .control-label {
        float: none;
    }
    .form-horizontal .controls {
        margin-left: 0;
        margin-top: 0.2em;
    }
    // 	.vi01 {top: 35px;}
    // 	.validation-success .vi01 {top: 30px;}
    // /* user picture upload form */
    // 	.upuf400 .form-actions {padding-left: 0px;}
    // 	.upuf1:before {left: 50px;}
    // 	.upuf1:after {left: 49px;}
}

/* end premium */

/* suggested entries */
 
.se001 {
    @extend .icon-plus-sign;
}
.se31 {
    @extend .icon-user2;
    font-weight: bold;
    margin-bottom: 0;
}
.se32 {
    @extend .icon-user;
    font-weight: bold;
    margin-bottom: 0;
}

.se99 {
    @extend .btn;
    @extend .btn-warning;
    @extend .icon-trash-o;
    margin-top: 12px;
}

/* end suggested entries */

/* blog */

// blog post list
.mblg1 {
    @extend .icon-th-list;
    float: left;
    margin-right: 2em;
}
.mblg12 {
    @extend .icon-share;
    float: right;
    margin: 3em 0 0.5em 1em;
}
.mblg24 {
    @extend .primary-action;
    @extend .icon-plus;
    float: left;
    margin: 1.6em 0;
}

.mblg26 {
    @extend .well;
    clear: both;
}

.mblg45,
.mblg46 {
    font-size: 1.2em;
    position: relative;
    top: 2px;
}
.mblg45 {
    @extend .icon-lightbulb;
    color: $txt-color;
}
.mblg46 {
    @extend .icon-ok-sign;
    color: $green;
}

.mblg63,
.mblg64,
.mblg65 {
    @extend .btn;
    @extend .btn-link;
    padding: 5px 0 0 0;
    margin: 0;
    font-size: 1em;
}
.mblg63 {
    @extend .icon-remove-sign;
}
.mblg64 {
    @extend .icon-ok-sign;
}
.mblg65 {
    @extend .icon-trash;
}

// blog post schreiben
.mblgp1 {
    @extend .icon-pencil;
    float: left;
}
.mblgp100 {
    position: relative;
    top: 12px;
}
.mblgp10 {
    @extend .icon-share;
    float: right;
    margin: 3em 0 0.5em 1em;
}
.mblgp2 {
    @extend .well;
    @extend .form-horizontal;
    clear: both;
}
.mblgp22,
.mblgp26 {
    @extend .input-xlarge;
}
.mblgp23 {
    @extend .icon-share;
    display: inline-block;
}
.mblgp24 {
    @include border-box;
    max-width: 650px;
    border: solid 1px $neutral;
    display: block;
    margin-bottom: 0.5em;
}
.mblgp270 {
    padding-top: 1em;
}
.mblgp27 {
    max-width: 650px;
    margin-bottom: 2em;
}
.mblgp28 {
    @extend .input-date;
}
.mblgp30 {
    @extend .help-block;
    margin-top: 2em;
}
.mblgp33 {
    @extend .icon-lightbulb;
    margin-left: 0.5em;
}
.mblgp34 {
    @extend .icon-ok-sign;
    margin-left: 0.5em;
    &:before {
        color: $green;
    }
}

// hide h1 tag in editor
.manage-blog-post .mce_h1 {
    display: none;
}

// blog settings
.mblgs1 {
    @extend .icon-pencil;
}

/* end blog */

/* ratings */

.edr001 {
    @extend .icon-star;
}
.edr4300 {
    padding-bottom: 2em;
}
.edr30 {
    @extend .table, .table-striped, .table-bordered;
    width: 450px;
}
.edr430 {
    .edr4311,
    .edr439 {
        margin-top: 1em;
        margin-bottom: 1em;
    }
}
.edr430,
.edr435 {
    td:last-child {
        width: 50px;
    }
}
.edr4311 {
    @extend .resize-vertical;
}
.edr432 {
    float: left;
    margin: 0;
    position: relative;
    top: -1px;
}
.edr439 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-warning;
    margin-left: 12px;
}
.edr4390 {
    @extend .btn;
    @extend .btn-mini;
    margin-left: 12px;
}

.rat001 {
    @extend .icon-star;
}
.rat002 {
    @extend .icon-pencil;
}
.rat003 {
    @extend .btn;
}
.rat004 {
    margin-top: 16px;
}
.rat005 {
    @extend .icon-star;
}
.rat11 {
    @extend .table-responsive;
}
.rat12 {
    @extend .table, .table-striped, .table-bordered;
    clear: both;
    margin-right: 12px;
}
.rat22 {
    @extend .icon-ok-sign;
    color: $green;
}
.rat23 {
    @extend .icon-question-sign;
    color: $orange;
}
.rat22:before,
.rat23:before {
    display: inline;
}
.rat24 {
    @extend .icon-minus-sign;
    color: $red;
}
.rat25 {
    font-size: 1.5em;
    position: relative;
    top: 2px;
    padding-right: 2px;
}
.rat311 {
    font-size: 1.5em;
    color: $orange;
    display: block;
    text-align: right;
    &.larger-than-5 {
        color: $green;
    }
}

.rat42 {
    @extend .icon-pencil;
}
.rat71 {
    @extend .form;
    @extend .form-horizontal;
}
.rat72 {
    @extend .table;
    @extend .table-bordered;
    tbody th {
        width: 20%;
    }
}
.rat722 {
    @extend .icon-share;
}
.rat723,
.rat724,
.rat725 {
    display: inline-block;
}
.rat724 {
    @extend .btn;
    @extend .btn-info;
    @extend .icon-inbox;
    margin: 0 12px 0 24px;
}
.rat725 {
    @extend .icon-ok;
    @extend .info;
    display: none;
    &.visible {
        display: inline-block;
    }
}
.rat73 {
    padding: 0 !important;
}
.rat74 {
    font-weight: bold;
}
.rat75 {
    border-bottom: none !important;
    border-left: none !important; 
    border-top: none !important;
    tr:first-child td {
        border-top: none !important;
    }
    td:first-child {
        border-left: none !important;
        font-weight: bold;
    }
}
.rat81 {
    @extend .icon-file;
}
.rat81:before {
    width: 0.4em;
}

.rat99 {
    @extend .btn-link;
    @extend .icon-trash;
}
.rat991 {
    @extend .btn-link;
    @extend .icon-download-alt;
}

/* end ratings */

/* email settings */

    .ems25 { 
        @extend .info;
        margin-top: 16px;
    }

/* end email settings */

/* register link */

.rlnk12 {
    @extend .icon-globe;
}
.rlnk31 {
    @extend .icon-envelope;
    @extend .primary-action;
}

/* end register link */

/* area and property content blocks */

.ab001 {
    @extend .icon-download;
}
.ab00 {
    float: left;
}
.ab0002 {
    margin-top: 1em;
}
.ab0001 {
    clear: both;
}
.ab10,
.ab00,
.ab000 {
    @extend .icon-map-marker;
}
.ab21 {
    @extend .icon-question-sign;
}
.ab31 {
    td:nth-child(2) a {
        @extend .icon-map-marker;
        font-weight: bold;
    }
}

.ab41 {
    @extend .form;
    max-width: 1300px;
}
.ab42 {
    margin-bottom: 1em;
}
.ab52 {
    @extend .icon-map-marker;
}

.ab610 {
    margin-right: 5px;
}
.ab61 {
    @extend .icon-flag;
    @extend .btn-flat-blue;
}
.ab63,
.ab64 {
    @extend .text-right;
}
.ab67 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-flag;
    font-size: 1em;
}
.ab68 {
    @extend .btn;
    @extend .btn-link;
    @extend .icon-remove-sign;
    background: $orange;
    font-size: 1em;
}

.area-complete {
    @extend .icon-ok-sign;
    color: $green;
}
.area-incomplete {
    @extend .icon-minus-sign;
    color: $orange;
}

.cp10 {
    @extend .icon-tags;
}

/* end area content blocks */

/* improved entries */

.ie001 {
    @extend .icon-pencil;
}
.ie12 {
    @extend .icon-pencil;
}
.ie21 {
    clear: both;
    @extend .table;
    @extend .table-striped;
    @extend .table-bordered;
}
.ie21 strong {
    display: none;
}

.ie3 {
    @extend .well;
    @include group;
}
.ie31,
.ie32 {
    @extend .bx50;
}
.ie4 {
    @extend .form-actions;
    text-align: center;
}
.ie42 {
    @extend .btn;
    @extend .btn-large;
    @extend .btn-success;
    @extend .icon-ok;
    margin-right: 2em;
    margin-bottom: 1em;
}
.ie43 {
    @extend .btn;
    @extend .btn-large;
    @extend .btn-warning;
    @extend .icon-trash;
    margin-bottom: 1em;
}

.ie91 {
    @extend .icon-undo;
}

/* end improve entries */

.geo-location10 {
    display: inline-block;
}

.pane {
    @extend .tab-content;
    @include group;
    overflow: visible;
    padding-top: 16px;
}

.master-save {
    position: fixed;
    left: 242px;
    right: 0;
    bottom: 0;
    padding: 12px;
    text-align: center; 
    background: rgba(0, 0, 0, 0.4);
    z-index: 1212;
    @include media("<=830px") {
        left: 0;
    }
    @include media("<=mobile-ui"){
        display: none;
    }
}
.ms1 {
    @extend .primary-action;
}

/* hide form controls when editing new entry without name saved */
.pic-upload.form-disabled {
    .pane > *,
    .ee99 {
        display: none;
    }
    .pane,
    .pic00 {
        display: block;
    }
}

.pic0000 {
    padding-left: 19px;
    padding-right: 19px;
}

.pic00 {
    @extend .message-warning-box;
}
.pic002 {
    @extend .icon-arrow-right;
    display: inline-block;
}

.pic11 {
    float: left;
}
.pic12 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-mini;
    margin: 2.3em 0 0 2em;
}
.pic14 {
    @extend .btn;
    @extend .btn-success;
}
.pic13 {
    @extend .icon-plus;
}

.pic20 {
    clear: both;
    min-width: 0;
}
.pic22 {
    @extend .table,
        .table-striped,
        .table-bordered,
        .table-responsive,
        .table-form;
    clear: both;
    border-collapse: collapse;
    @include media("<=480px") {
        thead {
            display: none;
        }
    }
}
.table-responsive {
    overflow-x: auto;
    overflow-y: initial;
    width: 100%;
}
/* foto credits */
.pic24 {
    float: right;
    display: block;
    margin: 5px 0;
    font-size: 0.9em;
}
.pic240 {
    @extend .icon-chevron-thin-down;
    margin-left: 4px;
}
.pic242 {
    @include group;
    clear: both;
    margin-bottom: 8px;
}
.pic243 {
    width: 120px;
    float: left;
    padding-top: 4px;
}
.pic2431 {
    font-weight: normal;
}
.pic244 {
    width: calc(100% - 140px);
    float: right;
}

// picture categories
.pic245 {
    margin-top: 8px;
    margin-bottom: 16px;
}
form input.pic2452 {
    display: none;
}

.pic25 {
    width: 100%;
    @include border-box;
    min-height: 120px;
}
.pic27 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-warning;
}
.pic28 {
    @extend .icon-trash;
}
.pic29 {
    @extend .icon-fullscreen;
    @include opacity(0);
    float: right;
}
tr.draggable-item:hover .pic29 {
    cursor: move;
    @include opacity(80);
}
.pic22 .ui-sortable-placeholder td {
    height: 100px; 
}

.pic-link .thumbnail {
    height: auto;
    border: solid 1px $neutral;
}

.pic32 {
    @extend .table;
    @extend .table-bordered;
    @extend .table-form;
    margin-top: 2em;
}
.pic35 {
    margin: 0 2em 1em 0;
    padding-bottom: 1em;
    display: block;
    float: none;
}
.pic32 .pic25 {
    max-width: 300px;
}

.pic501,
.pic502,
.pic503 {
    @include media("<=480px") {
        box-sizing: border-box;
        display: block;
        float: left;
        width: 100%;
    }
}

td.pic501 {
    @include media("<=480px") {
        border-bottom-width: 0;
    }
}

td.pic502 {
    @include media("<=480px") {
        border-width: 0 1px;
        padding: 0 8px;
    }
}

td.pic503 {
    @include media("<=480px") {
        border-top-width: 0;
        padding-bottom: 16px;
        text-align: right;
    }
}

.pic50 {
    margin-right: 8px;
    margin-bottom: 4px;
    max-width: 320px;
    display: inline-block;
}
.pic510 {
    display: block; 
    float: none;
    clear: both;
    padding-top: 8px;
}
.pic51,
.pic52,
.pic53 {
    @extend .info;
    display: none;
}
.pic51 {
    margin-bottom: 4px;
}
.pic51, .pic521 {
    position: relative;
}
.pic521 .tooltip {
    font-weight: normal;
}

.pic5:nth-child(1) .pic51 {
    display: block;
}
.pic5:nth-child(2) .pic52 {
    display: block;
}
.pic5:nth-child(3) .pic53 {
    display: block;
}
.pic55 {
    @extend .icon-rocket;
    display: inline-block;
}

.pic42 {
    margin-top: 1em;
}

/* statische content block seiten */

.do-not-allow-h1 .mce_h1 {
    display: none;
}

.scb001 {
    @extend .icon-edit;
}
.scb002 {
    @extend .well;
}
.scb003 {
    @extend .icon-share;
}
.scb11 {
    float: left;
}
.scb12 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-success;
    @extend .icon-plus;
    @extend %font-icon-xl;
    margin: 28px 0 1em 1.6em;
    float: left;
}
.scb20 {
    @extend .table;
    @extend .table-bordered;
    @extend .table-striped;
}
.scb2 {
    padding-bottom: 12px;
    margin-bottom: 12px;
    border-bottom: solid 1px $neutral;
}
.scb220 {
    display: flex;
    flex-wrap: wrap;
}
.scb22, .scb221 { 
    @extend .btn;
    @extend .btn-success;
    @extend .btn-small;
    margin: 0 0.5em 0.5em 0;
}
.scb23 {
    @extend .icon-edit;
}
.scb24 {
    display: inline-block;
    margin: 0 8px;
}
.scb25 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-warning;
    display: inline;
} 
.scb3 {
    text-align: right;
}
.scb31 {
    @extend .icon-chevron-thin-down;
}
.scb32 {
    @extend .icon-chevron-thin-up; 
    display: none;
}
.scb35 {
    @extend .well;
}
.scb358 {
    @extend .icon-edit;
}
.scb36 {
    @extend .icon-exclamation-sign;
}
.scb40 {
    @extend .form;
    max-width: 900px;
}
.scb400 {
    padding-top: 12px;
    float: left;
}
.scb401 {
    float: right; 
    margin-bottom: 5px;
    .ee0040 {
        float: right;
        margin-top: 8px;
        margin-right: 0;
    }
}
.scb41 {
    margin-bottom: 12px;
}
.scb42 {
    margin-right: 0;
    display: block;
}
.scb43 {
    @extend .icon-share;
    @include opacity(80);
}
.scb44 {
    @extend .btn;
    @extend .btn-success;
    margin-right: 25px;
}
.scb45 {
    @extend .icon-undo;
    @include opacity(80);
}
.scb46,
.scb47,
.scb48 {
    box-sizing: border-box;
    border-radius: 4px;
    width: 409px;
    max-width: 100%;
    padding: 0.4em 0.6em;
    margin: 0.3em 0 0 180px;
}
.scb46 {
    @extend .alert-success;
}
.scb47 {
    @extend .alert-error;
}
.scb48 {
    @extend .alert;
}

.lct-inactive{
    .lct21131{
        @extend .icon-ok-sign;
        color: $txt-color-light;
    }
}

.lcb0 {
    display: flex;
}
.lcb1,
.lcb2 {
    @include flex(1);
}
.lcb1 {
    @extend .btn, .btn-warning;
    margin-right: 12px;
}
.lcb2 {
    @extend .btn, .btn-info;
    margin-left: 12px;
}
.lcb3 {
    @extend .btn;
}

.imp32 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-large;
}
.imp321 {
    @extend .icon-download-alt;
}
.imp42 {
    @extend .message-error-block;
}

.exp21 {
    margin-left: 4em;
}
.exp91 {
    @extend .icon-undo;
}

.sup21 {
    @extend .input-max;
}

.mceLayout {
    height: auto !important;
}
.mceIframeContainer {
    display: block;
    max-height: 70vh;
    overflow-y: auto;
}

/* end statische content block seiten */

/* text and content pages */

.text-page {
    max-width: 750px;
}
@include custom-checkbox(1.5em, $green, darken($green, 15%));
label.custom-checkbox-label {
    font-size: 0.9em;
    &::after {
        left: 2px;
    }
}
input[type="checkbox"].custom-checkbox-input {
    display: none;
}
.checkbox {
    padding-left: 0px;
}

/* learning center */

/* end learning center */

/* header navigation edit */

.hne-nav-wrapper {
    @include group;
    margin: 4px 0;
    background: #f9f9f9;
    border: solid 1px #ccc;
    padding: 8px;
    position: relative;
    z-index: 2;
    border-radius: 4px;
}

.hne-node-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    &:not(:empty) {
        position: relative;
    }
    .ui-sortable-placeholder {
        position: relative;
        min-height: 20px;
        width: 100%;
        z-index: 2222;
        top: 100%;
        bottom: 0;
        display: block;
        left: 0;
    }

}

.custom-navigation-list .ui-sortable-placeholder {
    border: dashed 5px #aaa;
    visibility: visible !important;
}

.custom-navigation-list {
    @include list-clean; 
    max-width: 1200px;
    .nav-level-1, .nav-level-2, .nav-level-3 {
        position: relative;
    }
    .nav-level-2 {
        margin-left: 36px;
    }
    .nav-level-3 {
        margin-left: 72px;
    }
}
 
.hne1 {
    @extend .icon-layout-header !optional;
}

.hne2, .hne3 {
    @extend .well;
}

.hne31{
    margin-bottom: 32px;
}

.hne400 {
    padding: 8px 16px 16px 32px;
    position: relative;
    cursor: move;

    &:not(:last-child) {
        border-bottom: 1px solid #ccc;
    }

    &:hover {
        background: #eee;
        .hne41:before{
            display: block;
        }
    }
}

.hne41 {
    @extend .icon-resize-vertical;
    position: relative;

    &:before {
        left: 0;
        top: 50%;
        transform: translate(-100%,-50%);
        font-size: 1.1em;
        position: absolute;
        display: none;
    }
}

.hne3--hd {
    .hne40 tbody tr:not(:first-child) td:nth-child(2) {
        padding-left: 36px;
    }
}

.hne40 {
    @extend .table, .table-striped, .table-bordered;
    max-width: 1200px;
    td:first-child {
        width: 24px;
        vertical-align: middle;
    }
    td:nth-last-child(2){
        width: 200px;
    }
    td:last-child {
        width: 24px;
    }
    tbody {
        input, select {
        margin-bottom: 2px;
        }
    }
}

.hne403 {
    @extend .icon-resize-vertical;
    
}

.hne42 {
    @extend .btn;
    @extend .btn-warning;
    @extend .btn-mini;
    @extend .icon-trash;
    margin-left: 12px;
}
.hne45 {
    float:left;
    padding-right: 16px;
}
.hne451 {
    display: inline-block;
    margin-left: 8px;
    padding-top: 6px;
}
.hne4512 {
    display: inline-block;
    padding-left: 18px;
    margin-bottom: 0;
    margin-top: 4px;
}
.hne48 {
    @extend .btn;
    @extend .btn-warning;
    @extend .btn-mini;
    @extend .icon-trash;
}
.hne49 {
    @extend .btn;
    @extend .btn-success;
    @extend .icon-plus;
    margin-top: 16px;
}
.hne5 {
    @extend .btn;
    @extend .btn-success; 
    @extend .btn-small;
    @extend .icon-plus;
    margin-top: 15px;
    margin-bottom: 48px;
}

.hne6 {
    .info {
        display: inline;
    }
    
}

.hne615 {
    font-size: 1.2em;
    margin-top: 16px;
    float: left;
    margin-right: 8px;
}

.hne616 {
    position: relative;
    top: 10px;
}

.hne650 {
    float: right;
    margin-top: 3px;
}
.hne651, .hne652 {
    @extend .btn;
    @extend .btn-mini;
    @extend .btn-success;
    @extend .icon-plus;
}

.hne91 {
    @extend .primary-action;
}
.hne92 {
    @extend .icon-share;
    margin-left: 12px; 
    position:relative;
    top: 4px;
}

// footer navigation edit
.fne1 {
    .hne6 {
        border-bottom: solid 1px $neutral;
        margin-bottom: 24px;
        padding-bottom: 24px;
    }

    .nav-level-1 > div > .hne-nav-wrapper {
        border: none;
        background: none;
        display: flex;

        &:before, &:after {
            display: none;
        }

        .hne402 {
            margin-right: 16px;
            position: relative;
            top: 18px;
        }

        fieldset {
            margin-right: 32px;

            input {
                position: relative;
                top: 15px;
            }

            .help-block {
                position: relative;
                top: 10px;
                font-size: 0.9em !important;
            }
        }

        .hne650 {
            position: relative;
            top: 12px;
        }
    }

    .nav-level-2 {
        margin-left: 0;
    }
}



/* end header navigation edit */

/* end main content */

/* footer */

.ft1{
    display: flex;
    align-items: center;
    min-width: 100%;
    margin: 32px -1.4em -3em;
    box-sizing: border-box;
    padding: 1em;
    background: $neutral-one;
    margin-top: auto;
    @include media("<=page-width"){
        padding-bottom: 72px;
    }
}

.ft11{
    display: flex;
    flex-direction: column;
    @include media("<=page-width"){
        padding-left: 12px;
        padding-right: 12px;
    }
}

.vn14 {
    position: relative;
    top: -3px;
    margin: 0 0 0 2px;
    height: 18px;
    width: 91px;
}
.vn15 {
    @extend %hide-text;
}

/* end footer */

/* cookie consent */

.cookie-consent {
    @extend %shadow-top;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 4px 4px 2px 8px;
    border-top: solid 1px darken($blue, 5);
    bottom: 0;
    left: 0;
    right: 0;
    position: fixed;
    z-index: 2000000;
    display: none;
    p {
        padding-top: 6px;
        text-align: center;
        max-width: 1200px;
        margin: 0 auto;
    }
}
.cookie-consent-button {
    @extend .btn;
    @extend .btn-info;
    @extend .btn-large;
    max-width: 125px;
    width: 33%;
    text-align: center;
    margin-bottom: 5px;
    margin-left: 8px;
    font-size: 0.9em;
    padding: 5px 12px;
}

/* end cookie consent */

/* Action bar */
$action-panel-bottom-height: 52px;
@mixin action-panel-bottom {
    @include shadow-t;
    box-sizing: border-box;
    display: flex;
    height: $action-panel-bottom-height;
    background: $primary;
    margin: 0;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1041;
    
}

@mixin action-panel-button($color: $primary, $txt-color: $txt-on-primary) {
    @include linear-gradient(lighten($color, 5), darken($color, 5));
    flex: 1 1 20%;
    border-top: solid 1px lighten($color, 5);
    border-left: solid 1px lighten($color, 5);
    border-right: solid 1px darken($color, 30);
    border-bottom: solid 1px darken($color, 30);
    color: $txt-color;
    box-sizing: border-box;
    height: $action-panel-bottom-height;  
    float: left;
    padding: 1px 4px 0 4px;
    position: relative;
    text-align: center;

    &:hover {
        @include linear-gradient(lighten($color, 15), darken($color, 1));
    }

    &:last-child {
        border-right-width: 0px;
    }

    a {
        color: $txt-color;
    }

    & > span {
        @include media("<=380px") {
            display: inline-block;
            padding-top: 3px;
        }
    }

    i {
        display: block;
        margin: 0 auto;
        font-size: 1.6em;
        position: relative;
        padding-top: 2px;
        line-height: 1;
    }

    strong {
        display: inline-block;
        font-size: 1.2em;
        position: relative;
        top: -1px;
    }

    @include media("<=380px") {
        font-size: 13px;
    }
}

.mab0 {
    @include media(">mobile-ui") {
        display: none;
    }
    @include media("<=mobile-ui", "height>480px") {
        @include action-panel-bottom;
    }
}
.mab1,
.mab2,
.mab3,
.mab4,
.mab5,
.mab6 {
    @include action-panel-button;
    padding: 5px 0 0;
    &.current {
        @include linear-gradient(lighten($primary, 1), darken($primary, 15));
    }
    @include media("<=360px") {
        display: inline-block;
        font-size: 0px;
        padding-top: 12px;
        width: 100%;
        i {
            font-size: 24px;
        }
        i:before {
            width: 100%;
            text-align: center;
        }
    }
}
.mab6 {
    display: none;
    flex-basis: 50%;
}

.mab0--save-entry {
    .mab1,
    .mab5,
    .mab3 {
        display: none;
    }
    .mab6 {
        display: block;
    }
}

.mab11 {
    @extend .icon-pencil;
}
.mab51 {
    @extend .icon-th-list; 
}
.mab21 {
    @extend .icon-camera;
}
.mab31 {
    @extend .icon-star;
}
.mab41 {
    @extend .icon-share;
}
.mab61 {
    @extend .icon-floppy-o;
}

/* end Action bar */

/* learning center */

// Controls
.lct1 {
    @include sidebar-controls;

    right: 0;
    bottom: 0;
    float: right;
    z-index: 890;
}

.lct11 {
    @include shadow-no-bottom;
    @include rounded(5px, 5px, 0, 0);
    width: 100%;
    padding: 0;
    position: relative;
    background-color: transparent;
    border: none;
    color: $txt-on-primary;
    background: $blue;

    &:hover {
        background: $blue-3;
        background-image: none;
    }

    &:before {
        right: 10px;
        top: 50%;
        position: absolute;
        transform: translateY(-50%);
        font-size: 1.25em;
    }

    &:focus {
        outline: none;
    }
}

.lct12 {
    @extend .icon-info-sign;
    word-wrap: break-word;
    margin: 0;
    padding: 10px 35px 10px 16px;
    text-align: left;
    font-size: 1.3em;

    &:before {
        margin-right: 8px;
    }
}

.lc30{
    width: 100%;
    display: flex;
}

.lc31 {
    @extend .icon-arrow-right;
    background-color: transparent;
    border: none;
    color: $txt-on-primary;
    background: $blue;

    &:hover {
        background: $blue-3;
        background-image: none;
    }
}

.lc32,
.lc31{
    border-radius: 0;
    margin: 0;
    width: 50%;
    padding: 8px 16px;
}

html
body
.lc32 {
    border: none;
    transition: .3s;
    background: $neutral-two;
    border-radius: 0px;

    &:hover{ 
        background: darken($neutral-two, 10%);
    }
}

.lct121, .lct21111 {
    @include circle-counter(
        $color: #fff,
        $background-color: $red,
        $font-size: 1.5em
    );
    position: relative;
    top: -1px;
    margin-left: 5px;
}

.lct-count--empty {
    @include circle-counter(
        $color: #666,
        $background-color: #ccc,
        $font-size: 1.5em
    );
    position: initial;
}

.lct21130 {
    display:none;
}

.lct2 .nav-dropdown--active {
    .lct21130, .lct2113 {
        display: block;
    }
}

.lct21136 {
    @extend .info; 
    display: block;
    font-size: 0.9em;
}

.lct21137 {
    @extend .lc31;
    @extend .icon-rounded-checkbox;
    width: 100%;
}

.lct21138 {
    @extend .blog-btn--secondary;
    @extend .icon-chevron-thin-down;
    padding: 12px;
    display:block;
    text-align: center;
    width: 100%;
}

.lct1--active {
    top: 100px;
    position: fixed;

    .lct11 {
        &:before {
            transform: rotate(180deg);
            top: 40%;
        }
    }
}

// Sidebar
html .lct2--active {
    display: block;
}

@media only screen and (max-width: 640px) {
    .lct1,
    .lct2--active {
        display: none;
    }
}

.lct2 {
    @include sidebar(
        // Components elements
        $sidebar-nav-selector: ".lct21",
        $sidebar-nav-el-selector: ".lct211",
        $sidebar-nav-link-selector: ".lct2111, .lct21131",
        $sidebar-nav-controls-selector: ".lct2112",
        $sidebar-nav-sub-selector: ".lct2113",
        $sidebar-nav-sub-el-selector: ".lct21134",
        $sidebar-nav-content-selector: ".lct21132",
        // Sidebar wrapper styles
        $sidebar-wrapper-position: sticky,
        $sidebar-wrapper-float: right,
        $sidebar-wrapper-overflow-y: hidden,
        $sidebar-wrapper-padding-top: 0,
        $sidebar-wrapper-bg-color: $neutral-one,
        // Sidebar nav styles
        $sidebar-nav-padding-right: 0,
        $sidebar-nav-padding-left: 0,
        // ###############################
        // Sidebar nav-el styles
        $sidebar-nav-el-border: 1px solid $neutral-medium,
        // Sidebar navsub--el styles
        // $sidebar-nav-sub-el-border: 1px solid $neutral-medium,
        // Sidebar nav-controls styles
        $sidebar-nav-controls-color: $blue-dark,
        $sidebar-nav-controls-bg-color: darken($neutral-light, 3),
        $sidebar-nav-controls-bg-color--hover: $blue-dark,
        $sidebar-nav-controls-color--hover: $txt-on-primary,
        // Sidebar nav content
        $sidebar-nav-content-bg-color: $txt-on-primary,
        // Sidebar nav-link default styles
        $sidebar-nav-link-bg: darken($neutral-light, 3),
        $sidebar-nav-link-color: $blue-dark
    );
    @include shadow-left;
    display: none;
    border: 1px solid $neutral;
    $top: 40px;
    z-index: 1010;

    // Nav
    .lct21 {
        margin: 0;
    }

    // Buttons
    .lct2112 {
        padding-bottom: 13px;
    }

    // Sub nav
    .lct2113 {
        margin-top: 0;
        // Buttons in subnav
        .lct2112 {
            background-color: lighten($neutral-one, 2);
            padding-bottom: 13px;

            &:hover {
                background-color: $blue-dark;
            }
        }
    }

    .lct21132{
        color: #555;
    }

    // Links in subnav
    .lct2113 .lct21131 {
        font-size: 14px;
        background-color: lighten($neutral-one, 2);
    }

    .lct21134{
        display: flex;
        align-items: stretch;
    }
}

.lct21133 {
    @extend .icon-share;
}

// Styles tip in sidebar
.blog-btn--secondary {
    @include button-as-link;
    color: $blue-dark;
    display: inline-block;

    &:hover {
        background: $blue-dark;
        color: $txt-on-primary;
    }
}
.blog-link {
    display: block;
    padding: 10px 0;
    margin: 15px 0 0 0; 
}

// Modifier used for main content when sidebar is active
.zone--narrow {
    width: calc(100% - 340px); 
    float: left;
}

.mc1--diactivate-right-padding {
    padding-right: 0;
}

/* end learning center */

// Cookies
.cc_dialog {
    @include cookies(
        $graphic: 'img/logo.png', 
    );
}

// Public documents start
.pd11 {
    @extend .btn;  
    @extend .btn-warning;
}
.pd10 {
    @extend .btn;
    @extend .btn-info;
    margin-left: 15px;
}
// Public documents end

// hero images

.hb1 {
    @extend .icon-image;
}
.hb5000 { 
    border-bottom: solid 1px $neutral;
    padding-bottom: 2em;
}
.hb500 {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
}
.hb50 {
    // @extend .icon-resize-vertical;
    // &:before {
    //     margin-right: 0.5em;
    // }
}

.hb51, .hb52 {
    position: relative;
    top: 0.8em;
}
.hb51 {
    @extend .btn;
    @extend .btn-small;
    @extend .btn-info;
    @extend .icon-ok;
    margin-right: 8px;
}
.hb52 {
    @extend .btn;
    @extend .btn-small;
    @extend .btn-warning;
    @extend .icon-trash;
}


.hb6 {
    clear: both;
}
.hb6000 {
    @extend .well-light;
    margin-bottom: 1em;
}
.hb600 {
    
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
    margin-bottom: 8px;
    border-bottom: 1px solid $neutral;
}
.hb60 {
    // @extend .icon-resize-vertical;
    border-bottom: none;
    margin-bottom: 0;
    // &:before {
    //     margin-right: 0.5em;
    // }
}
.hb62 {
    @extend .btn;
    @extend .btn-small;
    @extend .btn-warning;
    @extend .icon-trash;
    position: relative;
    top: 3px;
}

.controls.hb7 {
    max-width: 100%;
    display: flex;
    flex-wrap: wrap;
}
.control-group.hb70 {
    margin-bottom: 0;
}
.hb71 {
    position: relative;
    margin-right: 8px;
    margin-bottom: 8px;
    @extend .icon-fullscreen; 
    &:before {
        position: absolute;
        top: 42px;
        left: 8px;
        background: #eee;
        border-radius: 1000px;
        padding: 4px;
        border: solid 1px #666;
    }
    img {
        
        border: solid 1px $neutral;
        border-radius: 8px;
        
    }
    &.no-sort-image {
        &:before {
            display: none;
        }
    }
    &.no-image {
        &:before {
            display: none;
        }
        .hb72 {
            display: none;
        }
        .hb78 {
            display: none;
        }
    }

}
.hb72 {
    position: absolute;
    top: 8px;
    left: 8px;
    @include circle-icon-width-expanding(
        $bg-color: #eee,
        $fg-color: #666,
        $size: 1.6em,
        $border-width: 1px,
        $border-color: #666,
    );
}
.hb73 {
    @extend .info;
    font-size: 0.9em;
    display: block;
}

.hb78 {
    @extend .btn;
    @extend .btn-warning;
    @extend .btn-small;
    @extend .icon-trash;
    top: 8px;
    right: 8px;
    position: absolute;
}
.hb79 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-small;
    @extend .icon-plus;
}

.hb81 {
    @extend .btn;
    @extend .btn-success;
    @extend .icon-plus;
}

.hb90 {
    text-align: center;
    margin-top: 2em;
    padding-top: 2em;
    padding-bottom: 2em;
    border-top: solid 1px $neutral;
}
.hb91 {
    @extend .btn;
    @extend .btn-success;
    @extend .icon-plus;
}

// hero images end

body
.ee880{
    @extend .icon-search;
    position: relative;

    &:before{
        color: #5bb75b;
        font-size: 1.1em;
        position: absolute;
        left: 8px;
        top: 5px;
        z-index: 1;
    }
}

.ee880
.empty-message {
    padding: 0 10px;
}

body
.ee881{
    padding-left: 25px;
}

.ee883{ 
    @extend .ee22;
}

.ee882{
    max-height: 50vh;
    overflow-y: auto;
}

.ee8821{
    @extend .wrapper-light;
    border-radius: 4px;
    margin: 12px 12px 0 0;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    &:not(:last-child) {
        border-bottom: $card-border;
    }
}
.ee88211 {
    width: 100%;
    @include group;
}
.ee882110 {
    float:left;
    margin-right: 12px;
    img {
        border: solid 1px $neutral;
    }
}
.ee882111{
    font-size: 1.3em;
    font-weight: bolder;
    padding-right: 32px;
}

.ee882112{
    @extend .icon-map-marker;
    color: $grey-lighter;
}

.ee88212{
    @extend .ee22;
    margin-top: 0;
}

.ee882121{
    @extend .ee221;
}

.errorInEmail--subject{
    @extend .icon-exclamation-sign;
    display: table-cell;
    background-color: $error-200 !important;

    & > a,
    &:before {
        color: $error-100;
    }

    & > a:hover{ 
        background-color: $error-100;
        color: $error-200;
    }
}

.super-admin { 
    background-color: #5d3030; 
}

.design-Event > div > div{ 
    overflow-x: auto;
}

// author

.pmga1 {
    @extend .icon-info-sign;
}
.pmga15 {
    @extend .icon-share;
}

.pmga32 {
    border-bottom: solid 1px $neutral;
    padding-bottom: 8px;
    &:last-of-type { 
        border-bottom: none;
    }
}

// end author

// picture categories

.pmpc1 {
    .dropdown-menu, .ms-trigger {
        display: none;
    }
}
.pic2452 .ms-ctn {
    min-width: 200px;
}

// end picture categories

// bulk image upload 

.manage-image-upload {
    .modal-header {
        padding: 0;
    }
    .modal-body {
        padding: 0;
    }
}
.miu9 {
    text-align: center;
}
.miu91 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-large;
    @extend .icon-light-checkmark;
}

// end bulk image upload
 
// spam

.sppr1 {
    @extend .icon-lock;
}

// end spam

// overwrites from frontend imnported styles

.dz-manage {
    .bnr0 {
        margin: 0;
    }
}
