﻿/* hundehotel theme for discoverize.com portal - created 6.05.16 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "default-variables";
@import "variables";
@import "4_layouts/_thematica-global-template-style-variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
@import "4_layouts/_thematica-global-template-style";
/* end import */

/* overwrites */
/* graphics */

body {
    position: relative;
    background: #ffffff; 
}
body {
    font-family: trebuchet ms, sans-serif;
}

/* end graphics */

/* layout */

.tagline {
    text-align: center;
    color: #666;
    padding: 0;
    font-size: 0.955em;
    float: left;
    clear: left;
}

@include header-logo-above-nav;
.logo {
    margin-bottom: 25px;
    width: 300px;
}

.logo-graphic {
    width: 300px;
    @include media("<=mobile-ui") {
        height: 50px;
    }
}
.logo:hover {
    background-color: #fff;
}
// .nav {
//     a {
//         @include primary-font;
//         color: #fff;
//         padding: 13px 12px 8px 12px;
//     }
//     a:hover {
//         background-color: $primary-lighter;
//     }
//     a.current {
//         background: $primary-lighter;
//     }
// }

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 13px 12px 8px 12px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav01 {
//     @extend .icon-home;
// }

.global-search {
    @include global-search;
    padding: 6px 3px 6px 0;
    color: #ccc;
}

.mini-search {
    @include background-responsive(
        "img/searchfield.jpg",
        $image-height-mobile: 700,
        $image-height-desktop: 565
    );
    min-height: 565px;
    background: no-repeat 0 0;
    background-size: cover;
    background-position: left;
    &:before {
        background: rgba(0, 0, 0, 0);
    }
}

.ms10 {
    color: $primary-darker;
    font-size: 1.4em;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}
.ms23 {
    font-weight: 600;
}
.ms25 {
    background-color: $primary;
    background-image: unset;
    &:hover {
        background-image: unset;
        background-color: $primary-bright;
    }
}
.ms261 {
    font-size: 20px;
    color: $primary-darker;
}
.ms12 {
    > label,
    > span {
        color: #fff;
        @include text-background-offset(#666);
    }
}

/* search page */
.premium-booked {
    /*@include stripes-close(transparentize($primary, 0.3));*/
}
.sf61-1, .epf61-1 { 
    @extend .icon-info-sign;
}
.sf61-2, .epf61-2 { 
    @extend .icon-hunde;
}
.sf61-17, .epf61-17 { 
    @extend .icon-hund-kopf;
}
.sf61-4, .epf61-4 { 
    @extend .icon-home;
}
.sf61-5, .epf61-5 { 
    @extend .icon-hotel;
}
.sf61-6, .epf61-6 { 
    @extend .icon-bicycle;
}
.sf61-3, .epf61-3 { 
    @extend .icon-map-marker;
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-snowflake;
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-snowflake;
        &:before {
            left: 0;
            top: 3px;
        }
    }
}
.map-marker-premium span.map-marker-icon:before {
    left: 1px;
    top: 3px;
}

/* end search page */

/* kategorie icons */

/* hotel kategorie */
// 1
.Klassifizierung_1 {
    @include category-stars(1);
}
// 2
.Klassifizierung_2 {
    @include category-stars(2);
}
// 3
.Klassifizierung_3 {
    @include category-stars(3);
}
// 3s
.Klassifizierung_4 {
    @include category-stars(3);
    &:after {
        content: "S";
    }
}
// 4
.Klassifizierung_5 {
    @include category-stars(4);

}
// 4s
.Klassifizierung_6 {
    @include category-stars(4);

    &:after {
        content: "S";
    }
}
// 5
.Klassifizierung_7 {
    @include category-stars(5);

}
// 5s
.Klassifizierung_8 {
    @include category-stars(5);

    &:after {
        content: "S";
    }
}
.Klassifizierung_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

/* preisniveau */

// €
.Preisniveau_1 {
    @include category-euros(1);
}
// €€
.Preisniveau_2 {
    @include category-euros(2);
}
// €€€
.Preisniveau_3 {
    @include category-euros(3);
}
// €€€€
.Preisniveau_4 {
    @include category-euros(4);
}
.Preisniveau_NOT_AVAILABLE {
    @extend .icon-minus-sign;
}

.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}
/* end kategorie icons */
.ep81-ccta {
    @include linear-gradient(
        lighten($secondary-darker, 5),
        darken($secondary-darker, 5)
    );
    border-color: darken($secondary-darker, 7) darken($secondary-darker, 15)
        darken($secondary-darker, 30) darken($secondary-darker, 7);
}

.design-guide {
    border-bottom: solid 1px #ccc;
}

footer {
    background: $primary-darker;
    padding-top: 0;
}

.footer-top {
    @include background-responsive("img/calltoaction-bg.jpg");
    background: no-repeat;
    background-position-y: 50%;
    background-size: cover;
    padding: 4.5em 1em 4em;
    position: relative;
    z-index: 1;
    height: 218px;
    @include media("<=960") {
        height: auto;
    }
    h3,
    h4 {
        @include text-background-offset(rgba(0, 0, 0, 0.3));
        color: #fff;
    }
    h3 {
        padding-top: 0;
        @include media(">page-width") {
            font-size: 2.5em;
        }
    }
}

.footer-top12,
.footer-top12:hover {
    @extend .button-secondary, .icon-arrow-right;
    @include linear-gradient($primary-darker, $primary-darker);
    max-width: 450px;
    width: 80%;
}

.footer-logo {
    padding: 30px 0 40px;
}
.foot-logo {
    @include group;
    display: block;
    float: none;
    margin-bottom: 1em;
    .tagline {
        width: 100%;
        color: #ccc;
    }
    .logo-name {
        color: #ccc;
    }
}
.foot-nav a {
    color: #fff;
}

.welcome-inside:after {
    background: url(img/divider-icon.png) no-repeat;
    display: block;
    height: 69px;
    padding: 4em 0;
    background-position: 0 50%;
    background-size: 936px 80px;
}
.explained-inside:after {
    background: url(img/divider.png) no-repeat;
    display: block;
    height: 90px;
    background-position: 0 100%;
}


/* end layout */
/* end overwrites */

/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: $page-width) {
    .premium-block {
        background: #fff;
    }
}
/* below 768 */
@media only screen and (max-width: 767px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.1em;
        padding: 0;
    }
    .mini-search legend {
        margin-bottom: 0;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

// Cookies
.cc_dialog {
    @include cookies(
        $type-of-graphic: logo,
        $graphic: 'https://skigebiete.info/themes/skigebiete/styles/img/skigebiete-logo-(hash3895113021).png'
    );
}

// Facebook button in top nav
.fb-like {
    @include fb-buttons($width: 115px);
}