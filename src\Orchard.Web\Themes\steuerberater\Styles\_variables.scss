/* variables for steuerberater - moved 23.11.2016 - by andrej telle - discoverize.com */

/* variables */
$page-width: 1161px;
$mobile-ui: $page-width;
$mini-search-theme-color: rgba(0, 0, 0, 0);
$mini-search-theme-txt-color: rgba(224, 224, 224, 0.9);

$button-options: (
    "border": false,
    "rounded-corners": true,
    "rounded-corners-value": 8px 8px 8px 8px,
    "gradient-background": false,
    "text-shadow": false,
    "shadow": false,
);

/* colors */
$primary: #2e6ea8;
$primary-darker: darken($primary, 3%);
$primary-darkest: darken($primary, 8%);

$dark: #1c1c1c;

$medium: #999;
$medium-lighter: #bababa;
$medium-lightest: #e1e1e1;

$primary-lighter: lighten($primary, 5%);
$txt-on-primary: #fff;
$txt-shadow-on-primary: rgba(0, 0, 0, 0.5);

$secondary: #92E483;
$secondary-darker: darken($secondary, 5%);
$secondary-lighter: lighten($secondary, 5%);
$txt-on-secondary: rgba(0,0,0, 0.7);
$txt-shadow-on-secondary: rgba(255, 255, 255, 0);

$tertiary: #00c1b8;
$txt-on-tertiary: #fff;

$link-color: darken($primary, 0);
$sticky-nav-link-color: $txt-on-primary;

$grey: #333333;

$nav-color: transparent;
$nav-txt-color: #fff;

$nav-color-current: darken($primary,5%);
$nav-txt-color-current: $txt-on-primary;

$nav-collapser-color: #fff;

$flat-nav-bg: $primary;
$flat-nav-txt: $txt-on-primary;

$links-cta-bg: $primary;
$links-cta-color: $txt-on-primary;
$links-background-color-default: scale-color($primary, $lightness: 90%) !default;

/* end colors */

/* Header parts heights */
$header-main-h: 66px;
$header-sub-h: 24px;

/* Screens */
$screen-xs: 767px;
$screen-sm: 991px;

/* typography */
$footer-bg: #161616;
$footer-links-color: #fff;

$footer-top-bg-color: $txt-on-primary;

$footer-custom-1-color: $txt-on-secondary;

$footer-custom-2-color: $txt-on-secondary;

/* Import Google Web Fonts*/
/* lato-regular - latin */
@font-face { font-display: fallback; 
    font-family: "Lato";
    font-style: normal;
    font-weight: 400;
    src: url("./fonts/lato-v16-latin-regular.eot"); /* IE9 Compat Modes */
    src: local("Lato Regular"), local("Lato-Regular"),
        url("./fonts/lato-v16-latin-regular.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/lato-v16-latin-regular.woff2")
            format("woff2"),
        /* Super Modern Browsers */ url("./fonts/lato-v16-latin-regular.woff")
            format("woff"),
        /* Modern Browsers */ url("./fonts/lato-v16-latin-regular.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/lato-v16-latin-regular.svg#Lato") format("svg"); /* Legacy iOS */
}
/* open-sans-regular - latin */
@font-face { font-display: fallback; 
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 400;
    src: url("./fonts/open-sans-v17-latin-regular.eot"); /* IE9 Compat Modes */
    src: local("Open Sans Regular"), local("OpenSans-Regular"),
        url("./fonts/open-sans-v17-latin-regular.eot?#iefix")
            format("embedded-opentype"),
        /* IE6-IE8 */ url("./fonts/open-sans-v17-latin-regular.woff2")
            format("woff2"),
        /* Super Modern Browsers */
            url("./fonts/open-sans-v17-latin-regular.woff") format("woff"),
        /* Modern Browsers */ url("./fonts/open-sans-v17-latin-regular.ttf")
            format("truetype"),
        /* Safari, Android, iOS */
            url("./fonts/open-sans-v17-latin-regular.svg#OpenSans")
            format("svg"); /* Legacy iOS */
}
@mixin primary-font {
    font-family: "Lato", sans-serif;
    font-weight: 300;
}
@mixin secondary-font {
    font-family: "Open Sans", sans-serif;
    font-weight: 300;
}
@mixin footer-font {
    font-family: "Lato", sans-serif;
    font-weight: 300;
}

/* end variables */
