﻿@if (Request.QueryString["accessibility"] == "true") {
    <style id="dynamic-style">
        a, button, label {
            background: #111 !important;
            color: #fff !important;
            text-decoration: underline !important;
        }

        a span, button span, a strong, button strong {
            background: #111 !important;
            color: #fff !important;
        }

        h1, h2, h3, h4, h5, h6, p, span, strong, i {
            background: #fff !important;
            color: #111 !important;
        }
    </style>
    <script>
        localStorage.setItem('styleActivated', 'true');
    </script>
}
else {
    <style id="dynamic-style"></style>
}

<pre id="dynamic-style-raw" style="display:none;">
    a, button, label {
        background: #111!important;
        color: #fff!important;
        text-decoration: underline !important;
    }
    a span, button span, a strong, button strong {
        background: #111!important;
        color: #fff!important;
    }
    h1, h2, h3, h4, h5, h6, p, span, strong, i {
        background: #fff!important;
        color: #111!important;
    }
</pre>

<script>
    const buttonSelector = '.jq-toggle-style-btn';
    const styleTag = document.getElementById('dynamic-style');
    const cssSource = document.getElementById('dynamic-style-raw');
    const styleKey = 'styleActivated';

    // Get CSS text from <pre>
    const customStyle = cssSource.textContent.trim();

    function applyStyle() {
        styleTag.innerHTML = customStyle;
        localStorage.setItem(styleKey, 'true');
    }

    function removeStyle() {
        styleTag.innerHTML = '';
        localStorage.setItem(styleKey, 'false');
    }

    // Toggle style on button click
    document.addEventListener('click', (e) => {
        if (e.target.matches(buttonSelector)) {
            if (localStorage.getItem(styleKey) === 'true') {
                removeStyle();
            } else {
                applyStyle();
            }
        }
    });

    const params = new URLSearchParams(window.location.search);

    // Apply saved style on page load
    if (localStorage.getItem(styleKey) === 'true') {
        applyStyle();
    }
</script>