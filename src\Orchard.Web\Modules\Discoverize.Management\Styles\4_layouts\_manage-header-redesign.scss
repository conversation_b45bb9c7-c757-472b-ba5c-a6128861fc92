// header redesign

/* header */

.hd1--eo{
    position: sticky;
    top: 0;
    z-index: 50;
}

.hd1 {
    @include primary-font;
    box-sizing: border-box;
    background: #333;
    // background-image: url("./img/nav-bg-christmas.jpg");
    padding: 8px 12px;
    height: $header-height;
    max-height: $header-height;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    @include media("<=mobile-ui") {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 80px;
        width: 100%;
        position: fixed;
        z-index: 1040;
        @include complex-transition(transform 300ms ease 0s);
        will-change: transform;

        &.scrolling-down, &.stopped-scrolling-down {
            transform: translateY(-80px);
        }
        &.scrolling-up {
            transform: translateY(0);
        }
    }
}
.hd1 .modal {
    color: #333;
}
.brand {
    float: left;
    margin: 0 24px 0 0;
    position: relative;
    @include media("<=mobile-ui") {
        margin: 0 24px 0 0;
    }
    span.tagline {
        display: block;
        font-size: 0.8em;
        color: #eee;
    }
}
.brand:hover {
    text-decoration: none;
    color: #fff;
    background: none;
}
.brand1 {
    max-height: 50px;
    line-height: 2.3;
    img {
        width: auto;
    }
}

.hd3 {
    float: right;
}
.hd31 {
    @extend .well;
    float: left;
    margin: 0 1em 0 0;
    padding: 0.4em 1em;
    @include media("<=1280px") {
        display: none;
    }
}
.hd310 {
    margin-bottom: 5px;
    @include media("<=mobile-ui") {
        display: none;
    }
}
.hd32,
.hd32:hover {
    @extend .icon-info-sign;
}
.hd33 {
    @extend .btn;
    @extend .btn-success;
    @extend .btn-mini;
    @extend .icon-rocket;
    @include border-box;
    min-width: 150px;
    width: 100%;
    @include media("<=mobile-ui") {
        text-indent: -9875em;
        min-width: auto;
        margin-top: 0;
        height: 54px;
        width: 54px;
        &:before {
            text-indent: 0;
            float: left; 
            font-size: 3.55em;
            margin: 3px 0 0 0;
        }
    }
}

//entry content

.hd4 {
    margin-left: auto;
    margin-right: 36px;
    display: inline-flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: stretch;
    @include media("<=mobile-ui") {
        display: none;
    }
}
.hd41, .hd442 {
    position: relative;
    margin-right: 8px;
    width: 60px;
    height: 60px;
    background: #666;
    border-radius: 1000px;
    img {
        border-radius: 1000px;
        box-sizing: border-box;
        z-index: 2;
        position: relative;
    }    
    &:hover {
		.hd414 {
			transform: rotate(0deg);
			opacity: 1;
		}
		.hd413 {
			transform: rotate(180deg);
			opacity: 0;
		}
	}
    
}
.hd413, .hd414 {
    @extend .icon-image;
    color: #fff;
    display: block;
    width: 60px;
    height: 60px;
    font-size: 240%;
    position: absolute;
    text-align: center;
    top: 7%;
    left: 2%;
    z-index: 1;
    transition: opacity .3s, transform .3s;
}
.hd414 {
    @extend .icon-upload;
    transition: opacity .3s, transform .3s;
    transform: rotate(-180deg);
    opacity: 0;
}

.hd42 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.hd421 {
    display: flex;
}

.hd4211 {
    margin-right: 16px;
    display: inline-block;
    
}
.hd42111 {
    display: block;
    color: #333;
    background: #eee;
    border-radius: 1000px;
    padding: 4px 16px 3px 16px;
    font-weight: bold;
    
}
.hd42112 {
    @extend .icon-chevron-thin-down;
}
.hd4212 {
    flex-grow: 1;
}

// entry select dropdown
.hd44 {
    max-height: 500px;
    overflow-x: hidden;
}

.dropdown-menu a.hd441 {
    border-bottom: solid 1px $neutral;
    display: flex;
    flex-wrap: nowrap;
    padding: 12px;
    
}
.dropdown-menu li:last-child a.hd441 {
    border-bottom: none;
}
.hd442 {
    min-width: 48px;
    width: 48px;
    height: 48px;
    box-sizing: border-box;
    margin-right: 8px;
    img {
        border-radius: 1000px;
        border: solid 1px #999;
    }
}
.hd4421 {
    @extend .icon-image;
    color: #fff;
    display: block;
    width: 48px;
    height: 48px;
    font-size: 180%;
    position: absolute;
    text-align: center;
    top: 21%;
    left: 2%;
    z-index: 1;
}

.hd443 {
    display: flex;
    flex-direction: column;
}
.hd444 {
    font-weight: bold;
}
.hd446 {
    @extend %bar-medium-small;
    min-width: 180px;
    background: #555;
    border: none;
    margin-top: 3px;
}

// completeness
.hd4212 {
    @extend %bar-medium-small;
    float: right;
    min-width: 180px;
    background: #555;
    border: none;
    margin-top: 3px;
}

// ctas
.hd422 {
    display: flex;
    flex-direction: row;
}
.hd4221, .hd4222, .hd4223, .hd4224 {
    @include primary-font;
    flex-grow: 1;
    text-align: center;
    border-radius: 1000px;
    background: #555;
    color: #fff;
    padding: 3px 7px 3px 7px;
    font-size: 0.8em;
    margin-right: 8px;
    &:last-child {
        margin-right: 0;
    }
}
 
.hd4221 {
    @extend .icon-pencil;
    background: $primary;
    color: $txt-on-primary;
}
.hd4222 {
    @extend .icon-upload;
}
.hd4223 { 
    @extend .icon-rocket;
}
.hd4224 {
    @extend .icon-eye; 
    background: $secondary;
}

// account 
.hd10 {
    text-align: right;
    .modal {
        text-align: left;
    } 
    font-size: 0.9em;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    @include media("<=mobile-ui"){
        display: none;
    }
}

.hd111 {
    display: block;
}

.hd113 {
    padding-top: 5px;
    clear: both; 
    display: block;
}
.hd113 + div.popover {
    @include media("<=mobile-ui") {
        position: static;
    }
}

.hd112 {
    color: $link-color;
}
.hd111,
.hd113 {
    font-size: 0.95em;
    margin-left: 8px;
    padding: 4px 0 0 0;
}
.hd111 {
    color: #aaa;
    margin-left: 0;
} 
.hd112,
.hd112:hover,
.hd113,
.hd113:hover {
    color: $neutral;
}

.hd121 {
    @include list-clean;
    margin: 0;
    li {
        padding-bottom: 0.6em;
    }
    li:last-child {
        padding-bottom: 0;
    }
}
.hd123 {
    @extend .icon-lock;
}
.hd124 {
    @extend .icon-pencil;
}
.hd125 { 
    @extend .icon-sign-out;
}

.hd2 {
    float: left;
    margin: 16px 1em 16px 0;
    padding: 0;
    color: $neutral;
    font-size: 1.3em;
    position: relative;
    @include media("<=1280px") {
        display: none;
    }
    // Logo decorations for special occasions
    // &:before {
    //   content: url("./img/santa-hat.svg");
    //   position: absolute;
    //   right: -55px;
    //   top: -15px;
    //   width: auto;
    //   height: auto;
    //   z-index: 1;                 
    // }
}
.hd2 a,
.hd2 a:hover {
    color: $neutral;
}
.hd22 {
    position: relative;
    top: -5px;
}
.hd23 {
    @extend .icon-eye;
    display: block;
    font-size: 0.5em;
    text-align: right;
    position: relative;
    top: -8px;
}




.hd9{
    display: flex;
    padding-left: 0;
}

.hd91{
    list-style: none;
}

.hd91{
    margin-right: 12px;
}

.hd911 {
    @extend .btn;
    @extend .btn-info;
}

.hd912{
    @extend .btn;
    @extend .btn-success;
}

.hd913{
    @extend .btn;
    @extend .btn-neutral;
}



