/* einkaufenmainz theme for discoverize.com portal - created 6.05.16 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */

@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";

/* end import */

/* overwrites */

/* graphics */

body {
    position: relative;
    background: #ffffff;
    font-family: trebuchet ms, sans-serif; 
}

//Cookie consent link to Datenschutzerklärung
.cc12 {
    color: $primary-lighter;
}
/* end graphics */

/* type */

h1,
h2,
h3,
h4,
h5,
h6 {
    color: $secondary-darker;
}

/* end type */

/* layout */

.header-top {
    background: $primary;
    span,
    a {
        color: $txt-on-primary;
    }
}
.tagline {
    text-align: center;
    color: $txt-on-primary;
    padding: 0;
    font-size: 0.955em;
    float: left;
    clear: left;
}

.logo img {
    height: 60px;
}

.header-main {
    background: $tertiary;
    @include media(">page-width") {
        .home & {
            background-color: transparent;
        }
    }
}

.logo {
    margin-bottom: 8px;
    margin-top: 8px;
}

.nav-8 {
    font-size: 1.2em;
}

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 9px 12px 8px 12px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav {
//     background: none;
//     a {
//         @include primary-font;
//         background: none;
//         color: $primary;
//         font-size: 1.25em;
//         padding: 9px 12px 8px 12px; 
//         @include media("<1300px") {
//             font-size: 1.1em;
//         }
//     }
//     a:hover,
//     a.current {
//         background: $primary; 
//         color: $txt-on-primary;
//     }
// }
// .nav01 {
//     @extend .icon-home;
// }

.global-search {
    @include global-search(140px);
    padding: 6px 3px 6px 0;
    color: #ccc;
    @include media("<1300px") {
        input {
            width: 120px;
        }
    }
    @include media(">1400px") {
        input {
            width: 200px;
        }
    }
}

// .nav-level-2 a {
//     color: $txt-on-primary;
//     border: none;
// }

// @media screen and (min-width: $page-width) {
//     .sticky-header {
//         background: transparentize($tertiary, 0.1);
//         .nav a {
//             color: $primary;
//             font-size: 1.05em;
//         }
//     }
// }

// login for marketplace/shop
.mn41 {
    @extend .icon-share;
    background: lighten($primary,5);
    border-right: solid $txt-on-primary;
    padding-right: 8px;
    margin-right: 8px;
}

// homepage

@include mini-search-full-page(
    #fff, 
    "",
    (#fff, 0.8)
);
.full-page-mini-search-background::before {
    background-image: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.85) 100px,
        rgba(255, 255, 255, 0.7) 150px,
        transparent 230px
    );
} 

.mini-search-entry-type {
    padding-bottom: 8px;
}
.ms10 {
    color: $secondary;
    font-size: 2em;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
    @include media("<=830px") {
        font-size: 1.4em;
    }
    @include media("<=480px") {
        font-size: 1.1em;
    }
}
.ms23 {
    color: $primary;
    font-weight: 600;
    margin-top: 1.5em;
}
.ms25 {
    background-color: $primary;
    background-image: unset;
    @include media("<=768px") {
        margin: 12px 0 0;
        width: 100%;
    }
}
.ms261 { 
    font-size: 20px;
    @include media("<=mobile-ui"){
        font-size: 16px;
    }
}

// custom button to marketplace below .mini-search
.mini-search {
    flex-direction: column;
}
.ms99 {
    @extend .button-secondary;
    @extend .button--l;
    @extend .icon-share;
    margin-top: 32px;
    margin-bottom: 32px;
    z-index: 2;
    @include media("<=mobile-ui"){
        margin-top: 8px;
        margin-bottom: 8px;    
    }
}

/* search page */
.premium-booked {
    /*@include stripes-close(transparentize($primary, 0.75));*/
}
.sp150 {
    font-size: 1.05em;
}
.sf61 {
    font-size: 0.9em;
}

.sf61-4, .epf61-4 { 
    @extend .icon-info-sign;
}
.sf61-8, .epf61-8 { 
    @extend .icon-shopping-cart;
}
.sf61-6, .epf61-6 { 
    @extend .icon-euro;
}
.sf61-5, .epf61-5 { 
    @extend .icon-truck;
}
.sf61-10, .epf61-10 { 
    @extend .icon-handshake;
}
.sf61-7, .epf61-7 { 
    @extend .icon-gift;
}
.sf61-11, .epf61-11 { 
    @extend .icon-people;
}

.map-marker {
    @include map-marker(30px, #fff, $primary);
}
.map-marker-primary,
.map-marker-active {
    @include map-marker(30px, darken($primary, 20), #fff, darken($primary, 30));
}
.map-marker-premium {
    @include map-marker(38px, $primary, $txt-on-primary, #fff, 2px);
    &.map-marker-active {
        @include map-marker(
            38px,
            darken($primary, 12),
            $txt-on-primary,
            darken($primary, 30),
            2px
        );
    }
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-shopping-cart;
    &:before {
        left: -1px;
        top: 5px;
    }
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-shopping-cart;
        &:before {
            font-size: 19px;
            left: 0px;
            top: 6px;
        }
    }
}
.map-marker-premium span.map-marker-icon {
    &:before {
        left: 1px;
        top: 7px;
        font-size: 24px;
    }
}



/* end search page */

/* entry page */
/* auszeichnung eintragsseite */
.epea1 {
    position: absolute;
    right: 8px;
    bottom: 8px;
}
.epea13 {
    display: none;
}
/* end entry page */

/* kategorie icons */

.search-selected-category-text,
.entry-selected-category-text {
    @extend %hide-text;
}

/* end kategorie icons */

.area {
    background: $tertiary;
}
.properties,
.search-area-property-links,
.sp91 {
    background: darken($tertiary, 5);
}

.design-guide {
    border-bottom: solid 1px #ccc;
}

footer {
    padding-top: 0; 
    background: $primary;
}
.lc1 {
    background: darken($tertiary, 10);
    padding: 20px 0 40px 0;
    margin-top: 0;
}
.footer-presse-inside {
    @include center;
    width: 100%;
}
.footer-top {
    @include footer-top(
        ".icon-shopping-cart",
        darken($tertiary, 15),
        darken($primary, 0),
        rgba(darken($primary, 15%), 0.12)
    );
    h3 {
        font-size: 2em;
    }
    h4 {
        font-size: 1.6em;
    }
    .footer-top12 {
        @extend .button--l, .icon-arrow-right;
        margin-top: 3em;
    }
}

.footer-logo {
    padding: 30px 20px 40px;
    h3 { 
        color: #fff;
    }
}
.foot-logo {
    @include group;
    display: block;
    float: none;
    margin-bottom: 1em;
    .tagline {
        width: 100%;
        color: #ccc;
    }
    .logo-name {
        color: #ccc;
    }
}
.foot-nav {
    width: 33.3333%;
    h3 {
        color: #fff;
        opacity: 0.9;
    }
    a {
        color: #fff;
    }
}

.premium-block-inside {
    background: transparent;
}
.premium-block-entry-page .pbi12 {
    color: $txt-color;
}
.pbi12 {
    font-size: 3em;
    &::before {
        color: $primary;
    }
    @include media("<=page-width") {
        color: $grey;
    }
}


.blgt0 {
    padding-bottom: 0.2em;
    &::before {
        color: $primary;
    }
    @include media(">page-width") {
        font-size: 2.7em;
    }
}
.blgt1 {
    background-color: $bg-area;
    &:hover {
        background: lighten($bg-area, 5%);
    }
    header {
        font-size: 16px;
    }
}
.blgt33 a,
.blgt34 {
    color: $secondary-darker;
    font-weight: 700;
}
.blgt33::before {
    color: $primary;
}
.blgt34 {
    font-size: 1.2em;
}
.blgt85 {
    font-weight: 700;
}

.area10,
.prop10 {
    color: $secondary-darker;
    font-size: 2em;
    &::before {
        color: $primary;
    }
}
.area12 {
    color: $secondary-darker;
}

/* end layout */

/* end overwrites */

/* responsive design */
@import "respond";
/* below page-width */
@media only screen and (max-width: $page-width) {
    .premium-block {
        background: #fff;
    }
    .main-wrap {
        background-image: none;
    }
}
@media only screen and (max-width: 960px) {
    .nav {
        & > li {
            width: auto;
        }
        & > li:last-child a {
            border-right: none;
        }
        a {
            color: $link-color;
        }
    }
}
/* below 768 */
@media only screen and (max-width: 767px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        padding: 0;
    }
    .mini-search legend {
        margin-bottom: 0;
    }
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

// Cookies
.cc_dialog {
    @include cookies(
        $type-of-graphic: logo,
        $graphic: 'https://kauftregional.at/themes/kauftregional/styles/img/logo-kauft-regional-at-(hash1670716629).png'
    );
}