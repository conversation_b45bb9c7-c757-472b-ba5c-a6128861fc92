﻿/* restaurant theme for discoverize.com portal - created 16.10.12 - author: andrej telle - discoverize.com */
 
/* import scss files via discoverize default theme */
@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
$cb-bg-hp: rgba(255, 255, 255, 1);

/* end import */ 

/* overwrites */

/* graphics */

body {
    background: none;
    font-family: trebuchet ms, sans-serif;
}

/* end graphics */

/* global */

h1,
h2,
h3,
h4,
h5,
h6 {
    @include secondary-font;
    color: darken($primary, 14%);
}
/* end global */

/* layout */
 
.search-page { 
    margin-top: 0;
    padding-top: 0;
}
.explained-inside .box-left-33 { 
    min-height: 295px;
    @include media("<=960px") {
        margin-left: 0px;
        margin-right: 0px;
        width: calc(50% - 7px);

        &:nth-child(3n) {
            margin-left: 0px;
        }
        &:nth-child(2n) { 
            margin-left: 14px;
        }
    }
    @include media("<660px") {
        margin: 0 0 18px;
        width: 100%;
        &:nth-child(2n) {
            margin-left: 0px;
        }
    }
    @include media("<=480px") {
        max-width: none;
    }
}
.blr1 {
    @include break-this;
}

/* end layout */

/* header */
.header {
    @include shadow-bot;
    position: relative;
}
.header-top {
    background: $primary;
}
.header-top-inside {
    padding-top: 0px;
}
.tagline {
    @include primary-font;
    color: #fff;
    padding: 7px 0 0 0;
    float: left;
    font-size: 0.85em;
}
.mini-nav {
    padding-top: 7px;
    a,
    a:hover {
        color: #fff;
        border-right: none;
    }
    a:hover {
        background: $primary;
    }
    span {
        color: #eee;
    }
    .mn11 {
        border-right: none;
    }
}

.logo {
    padding: 22px 0 20px;
    float: left;
    margin-right: 12px;
}
.logo img {
    height: 85px;
}
.mini-tagline,
.mini-tagline:hover {
    text-align: center;
    margin-top: -31px;
    font-weight: 100;
    display: block;
    width: 198px;
    color: #333;
    font-size: 12px;
    font-family: josefin sans;
    padding-bottom: 0px;
}

// .nav {
//     @include border-box;
//     margin: 0;
//     position: relative;
//     z-index: 3;
// }

// .nav-section-1,
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 10px 8px 8px 8px;
 
//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// .nav a, .sticky-header .nav a {
//     @include primary-font;
//     border: none !important;
//     background: none;
//     color: $primary;
//     font-size: 1.2em;
// }
// .nav .current {
//     background: $primary-darker;
//     color: $txt-on-primary;
// }

// .nav > li:hover > a {
//     background: $primary-darker;
// }

// a.nav01 {
//     @extend .icon-home;
// }

// .nav-level-2 {
//     @include shadow-remove;
//     background: none;
//     border: none;
// }
// .nav-level-2 a {
//     background: transparentize($primary-darker, 0.1);
//     color: #fff;
// }
// .nav-level-2 a:hover {
//     background: $primary-darker;
//     color: $txt-on-primary;
// }

.global-search {
    @include global-search;
    margin: 5px 5px 4px;
    color: #ccc;
}

/* end header */

/* homepage */

.mini-search {
    min-height: 650px;
    &:before {
        background: rgba(0, 0, 0, 0.1);
    }
}

.ms10 {
    padding-top: 0;
}
.ms10,
.ms261 {
    color: #fff;
    @include text-background-offset(rgba(0, 0, 0, 0.5));
}
.ms23 {
    @include text-background-offset(rgba(0, 0, 0, 0.5));
    @include media(">page-width") {
        margin-top: 15px;
    }
}
.content-block-5 {
    @include flex-order(3);
}
.home-page .premium-block {
    @include flex-order(7);
}
.home-page .blog-teaser-list {
    @include flex-order(6);
}

.cb-picture-list-4 .cb-picture-item {
    @include flex(1 1 auto);
}

html body .cb-picture-list-4 .cb-picture-item {
    &:before {
        background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.2) 100%
        );
        filter: drop-shadow(34px 55px 69px rgba(32, 32, 35, 0.1));
    }
}
.benefit .cb-picture-content {
    font-size: 1.5em;
}
.area10,
.prop10 {
    color: darken($primary, 14%);
}

/* end homepage */

/* search page */
.search-main {
    background-color: transparent;
}
.map-button {
    color: #fff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5), 0 0 1px rgba(0, 0, 0, 0.5);
}

.sf61-26, .epf61-26 { 
    @extend .icon-body;
}
.sf61-5, .epf61-5 { 
    @extend .icon-lamp;
}
.sf61-6, .epf61-6 { 
    @extend .icon-map-marker;
}
.sf61-16, .epf61-16 { 
    @extend .icon-arm;
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-physiofinder;
    &:before {
        top: 4px;
        font-size: 18px;
        left: -1px;
    }
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-physiofinder;
        &:before {
            left: 0;
            top: 5px;
        }
    }
}
.map-marker-premium span.map-marker-icon:before {
    left: 1px;
    top: 6px;
}
.map-marker-premium.map-marker-primary span.map-marker-icon {
    &:before {
        left: -1px;
        top: 3px;
        font-size: 28px;
    }
}

.sp19 {
    background: #fff;
    color: $primary;
    border: solid 1px $primary;
    &:hover {
        background: $primary;
        color: #fff;
    }
}
.sp150 {
    color: darken($primary, 18%);
}

/* map marker icon font-size reapplied dut to IE */

// .map-marker span.map-marker-icon::before, .map-marker-static span.map-marker-icon::before {font-size: 28px}

/* end search page */

/* blog */

.blg7 {
    top: -14px;
    right: 0;
}

/* end blog */

.design-guide {
    border-bottom: solid 1px #ccc;
}

blockquote {
    font-weight: 400;
    color: #777;
}

footer {
    border: none;
    width: 100%;
    background: #292929;
}

.footer-top {
    @include footer-top(".icon-physiofinder", $primary-darker);
}

.logo-footer {
    padding: 16px;
    display: block;
    &:hover {
        @include rounded(5px, 5px, 5px, 5px);
        background: none;
    }
}

/* end layout */
/* end overwrites */

/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: 960px) {
    .mini-tagline,
    .mini-tagline:hover {
        font-size: 10px;
        margin-top: -27px;
        width: auto;
    }
}
/* below 768 */
@media only screen and (max-width: 769px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.2em;
    }
}
/* below 380 */
@media only screen and (max-width: 380px) {
    .ms10 {
        font-size: 1em;
    }
}
/* below 320 */
@media only screen and (max-width: 319px) {
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

// welcome container
.welcome-inside {
    padding-top: 0;
}

// Cookies
.cc_dialog {
    @include cookies(
        $type-of-graphic: logo,
        $graphic: 'img/logo.png',
    );
}