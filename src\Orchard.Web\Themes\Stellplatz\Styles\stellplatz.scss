﻿/* restaurant theme for discoverize.com portal - created 16.10.12 - author: andrej telle - discoverize.com */

/* import scss files via discoverize default theme */
@import "default-variables";
@import "variables";
@import "default-custom-variables";
@import "icon-font/_icon-font.scss";
@import "imports";
@import "4_layouts/_modern-homepage";
$cb-bg-hp: rgba(255, 255, 255, 1);

/* end import */
 
/* overwrites */

/* graphics */

body {
    background: none;
}
body {
    font-family: trebuchet ms, sans-serif;
} 

/* end graphics */

/* global */

.blg4{
    a{
        text-decoration: underline;
    } 
}

h1,  
h2,
h3,
h4,
h5, 
h6 {
    @include secondary-font;
    color: $primary;
    font-weight: 600;
}

/* end global */

/* layout */

.search-page {
    margin-top: 0;
    padding-top: 0;
}

/* end layout */

/* header */

.header-top {
    background: $primary-darker;
}
.tagline {
    @include primary-font;
    color: #fff;
    padding: 0;
    float: left;
    font-size: 0.85em;
}
.mini-nav {
    a,
    a:hover {
        color: #fff;
        border-right: none;
    }
    a:hover {
        background: $primary;
    }
    span {
        color: #eee;
    }
    .mn11 {
        border-right: none;
    }
}

.header-main {
    background: $primary;
}
.logo {
    padding: 6px 0 7px;
    float: left;
    margin-right: 12px;
}
.mini-tagline,
.mini-tagline:hover {
    text-align: center;
    margin-top: -31px;
    font-weight: 100;
    display: block;
    width: 198px;
    color: #333;
    font-size: 12px;
    font-family: josefin sans; 
    padding-bottom: 0px;
}

// html
// body
// .sticky-header
// .nav{
//     a{
//         color: $txt-on-primary;
//     }
// }

// .nav {
//     @include border-box;
//     margin: 0;
//     position: relative;
//     z-index: 3;
// }
// .nav a {
//     @include primary-font;
//     border: none !important;
//     background: none; 
//     color: #fff;
//     display: block;
// }
// .nav .current {
//     background: $primary-darker;
//     color: $txt-on-primary;
// }

// .nav > li:hover > a {
//     background: $primary-darker;
// }

// .nav-section-1, 
// .nav-section-2,
// .nav-section-3,
// .nav-section-4,
// .nav-section-5,
// .nav-section-6,
// .nav1, .nav2, .nav3, .nav > li {
//     > a:first-child {
//         padding: 28px 16px 23px 16px;

//         @include media("<page-width") {
//             padding: 12px 8px;
//         }
//     }
// }

// a.nav01 {
//     @extend .icon-home;
// }
// a.nav01,
// a.nav10,
// a.nav21,
// a.nav31,
// a.nav41 {
//     @include border-box;
//     padding: 27px 16px 23px 16px;
// }
// .nav .nav19,
// .nav .nav10 {
//     @include rounded(0, 0, 0, 0);
// }

// .nav-level-2 {
//     @include shadow-remove;
// }
// .nav-level-2 a {
//     background: transparentize($primary-darker, 0.1);
//     color: #fff;
// }
// .nav-level-2 a:hover {
//     background: $primary-darker;
//     color: $txt-on-primary;
// }

.global-search {
    @include global-search;
    padding: 19px 3px 14px 0;
    color: #ccc;
}

/* end header */

/* homepage */

.mini-search {
    @include background-responsive(
        "img/stellplatz-im-gruenen-bg.jpg",
        $image-height-mobile: 705,
        $image-height-desktop: 520
    ); 
    min-height: 520px;
    background: no-repeat 0 0;
    background-size: cover;
    background-position: center 60%;
    &:before {
        background: rgba(0, 0, 0, 0.1);
    }
}
.mini-search-entry-type {
    @include media("<=480px") {
        padding: 0;
    } 
}
.mini-search__fields-wrapper {
    padding: 0.5em 2em 2em 2em;
    margin-top: 2em; 
    @include media("<=360px") {
        padding: 0.5em;
    }
}
.ms10 {
    padding-top: 0;
}
.ms10,
.ms261 {
    color: #fff;
    @include text-background-offset(rgba(0, 0, 0, 0.5));
}

.content-block-5 {
    @include flex-order(8);
}
.home-page .premium-block {
    @include flex-order(7);
}
.home-page .blog-teaser-list {
    @include flex-order(6);
}

.benefit-inside { 
    background: none;
    width: 100%;
    padding: 0px; 
}
.benefit-standout {
    padding: 0;
}

.cb-picture-list-4 .cb-picture-item {
    @include flex(1 1 auto);
}
.benefit .cb-picture-content {
    font-size: 1.5em;
}

/* end homepage */

/* search page */ 
/* FULL WIDTH SEARCH PAGE */



.map-button {
    color: #fff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5), 0 0 1px rgba(0, 0, 0, 0.5);
}

.sf61-1, .epf61-1 { 
    @extend .icon-info-sign;
}
.sf61-2, .epf61-2 { 
    @extend .icon-flag;
}
.sf61-3, .epf61-3 { 
    @extend .icon-cutlery;
}
.sf61-5, .epf61-5 { 
    @extend .icon-bicycle;
}
.sf61-6, .epf61-6 { 
    @extend .icon-map-marker;
}

.map-marker-non-hit span.map-marker-icon {
    @extend .icon-stellplatz;
}
.map-marker {
    span.map-marker-icon {
        @extend .icon-stellplatz;
        &:before {
            left: 0;
            top: 0;
            font-size: 18px;
        }
    }
}
.map-marker-premium span.map-marker-icon:before {
    left: 1px;
    top: 1px;
}
.map-marker-static span.map-marker-icon,
.map-marker-premium.map-marker-primary span.map-marker-icon {
    &:before {
        left: 1px !important;
        top: 2px !important;
    }
}

.map-marker-non-hit span.map-marker-icon:before, 
.map-marker span.map-marker-icon:before, 
.map-marker-static span.map-marker-icon:before{
    width: initial;
}

.map-marker span.map-marker-icon {
    &:before {
        left: 3px;
        top: 3px;
    }
}

// gradient overlay over stripes
.sp110 {
    background-color: #b3fff1;
    background-image: initial;
}
.sp19 {
    background: #fff;
    color: $primary;
    border: solid 1px $primary;
    &:hover {
        background: $primary;
        color: #fff;
    }
}

/* map marker icon font-size reapplied dut to IE */

// .map-marker span.map-marker-icon::before, .map-marker-static span.map-marker-icon::before {font-size: 28px}

/* end search page */

// entry page

// .ep-adblock-1 {
//     @include media(">page-width"){
//         margin-top: 22px;
//     }
// }

// end entry page 

/* blog */

.blg7 {
    top: -14px;
    right: 0;
}

/* end blog */

.design-guide {
    border-bottom: solid 1px #ccc;
}

blockquote {
    font-weight: 400;
    color: #777;
}

footer {
    border: none;
    width: 100%;
    overflow-x: clip;
    padding-top: 64px;
    background: $primary;
    // rounded curve color
    position: relative;
    &:after {
        background: $primary;
        border-radius: 50%;
        border-top: 20px solid $secondary;
        content: ""; 
        height: 160px;
        left: 50%;
        position: absolute;
        top: -40px;
        transform: translateX(-50%);
        width: 150%;
    }
}
// offset for rounded curve color
.properties, .sp91, .ep2 {
    padding-bottom: 48px;
}

.foot-nav h3, .foot-nav h4{
    color: $txt-on-primary;
}

.footer-top {
    @include footer-top(".icon-wohnmobil", $primary);
}

.footer-top12{
    @extend .button-primary;
}

.footer--custom{
    padding-top: 32px;
}

.logo-footer {
    padding: 16px;
    display: block;
    &:hover {
        @include rounded(5px, 5px, 5px, 5px);
        background: $primary;
    }
}

a.social-media-footer-icons {
    display: inline;
}
.foot-logo {
    display: block;
    .mini-tagline {
        width: auto;
        text-align: center;
    }
}
.foot-nav a {
    color: #fff;
}

/* end layout */
/* end overwrites */

@media screen and (min-width: $page-width) {
    .sticky-header {
        .mini-tagline,
        .mini-tagline:hover {
            font-size: 10px;
            margin-top: -27px;
            width: auto;
        }
    }
}
/* responsive design */
@import "respond";
/* below 960 */
@media only screen and (max-width: 960px) {
    .mini-tagline,
    .mini-tagline:hover {
        font-size: 10px;
        margin-top: -27px;
        width: auto;
    }
}
/* below 768 */
@media only screen and (max-width: 769px) {
    .welcome,
    .mini-search {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
/* below 480 */
@media only screen and (max-width: 479px) {
    .ms10 {
        font-size: 1.2em;
    }
}
/* below 380 */
@media only screen and (max-width: 380px) {
    .ms10 {
        font-size: 1em;
    }
}
/* below 320 */
@media only screen and (max-width: 319px) {
}
/* end responsive design */

/* include IE11 fixes for responsive mode */
@import "internet-explorer";

// Facebook button in top nav
.fb-like {
    @include fb-buttons($width: 126px);
}